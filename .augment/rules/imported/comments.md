---
type: "always_apply"
---

# Code Comments Policy

## Comment Guidelines
- **Minimal Comments**: Only comment when absolutely necessary for understanding
- **Function/Method Level Only**: Comments should be at function/method definitions, not inline
- **Documentation Purpose**: Comments are for API documentation, not code explanation
- **Self-Documenting Code**: Write code that explains itself through clear naming and structure

## When to Comment

```dart
// ✅ Good: Method documentation for public API
/// Creates a new calendar event with the specified parameters.
/// 
/// Returns null if the event conflicts with existing availability rules.
/// Throws [ValidationException] if required fields are missing.
VCalendarEvent? createEvent(String title, DateTime startTime);

// ✅ Good: Complex business logic explanation
/// Calculates availability windows considering business hours, 
/// buffer times, and existing appointments.
List<TimeSlot> calculateAvailability();
```

```go
// ✅ Good: Function documentation for public API
// AnalyzeBusiness processes a business description and returns
// AI-generated module recommendations and data models.
// Returns error if LLM analysis fails or business type is unsupported.
func (s *llmService) AnalyzeBusiness(ctx context.Context, description string) (*BusinessAnalysis, error) {
    // Implementation...
}
```

## What NOT to Comment

```dart
// ❌ Bad: Obvious inline comments
final theme = VTheme.of(context); // Get theme from context
if (isEnabled) { // Check if button is enabled
  // Handle button press
  onPressed?.call();
}

// ❌ Bad: Implementation details
// Using setState to update the UI
setState(() {
  _selectedDate = newDate;
});
```

```go
// ❌ Bad: Obvious inline comments
user := getUserFromDB(userID) // Get user from database
if user != nil { // Check if user exists
    // Process user
    processUser(user)
}
```

## Comment Standards
- Use `///` for Dart public API documentation (generates docs)
- Use `//` for Go function documentation and minimal internal notes
- Keep comments concise and focused on "why" not "what"
- Update comments when code changes or remove them entirely
- Never leave TODO comments in production code
- Avoid commenting out code - delete it instead (version control preserves history)