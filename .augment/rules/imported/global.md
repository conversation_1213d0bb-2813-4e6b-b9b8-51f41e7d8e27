---
type: "always_apply"
---

# Vertoie Core Requirements

## Critical Rules - NEVER VIOLATE
- **ALWAYS** Use `.envrc` with `nix` commands. Use `direnv allow` or just `source .envrc`
- **DO NOT** do anything that was not explicitly asked of you
- **ALWAYS** update STATUS.md AND TASKS.md after each task completion
- **ALL** database migrations must be wrapped in transaction BEGIN...COMMIT
- **ALL** All related table inserts should be wrapped in a transaction BEGIN...COMMIT. (ex: register user and magic link auth_token)
- **NEVER** ignore errors - always handle them explicitly

## Code Quality Standards
- Use meaningful, descriptive names for variables, functions, and classes
- Keep functions under 20 lines when possible
- Single responsibility principle - one function, one purpose
- DRY - Don't repeat yourself, extract common patterns
- Use early returns to reduce nesting
- Write self-documenting code

## Error Handling Requirements
- Go: Always check and return errors explicitly `if err != nil { return fmt.Errorf("context: %w", err) }`
- Dart: Use proper exception handling with try-catch blocks
- Never swallow errors silently
- Provide meaningful error messages with context

## Security Standards
- Always validate user inputs, especially LLM-generated content
- Use parameterized queries to prevent SQL injection
- Encrypt sensitive data at rest and in transit
- Implement proper authentication and authorization checks

## Performance Requirements
- UI components must render in <16ms for 60fps
- API endpoints should respond in <200ms
- LLM operations should complete in <5s
- Database queries should execute in <50ms for simple operations