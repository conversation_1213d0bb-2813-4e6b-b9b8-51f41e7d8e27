---
type: "always_apply"
---

# Never Do These Things in Vertoie

## Critical Never Rules
- **NEVER** do anything that was not explicitly asked of you
- **NEVER** ignore errors or handle them silently
- **NEVER** create database migrations without BEGIN...COMMIT transactions
- **NEVER** forget to update STATUS.md and TASKS.md after completing tasks

## Code Anti-Patterns
- **NEVER** use magic numbers or hard-coded strings - use constants
- **NEVER** create God classes or functions that do too much
- **NEVER** use global variables or singletons
- **NEVER** ignore null safety in Dart (avoid `!` operator)
- **NEVER** swallow exceptions without proper handling

## Security Never Rules
- **NEVER** trust user input without validation
- **NEVER** store passwords in plain text
- **NEVER** execute LLM-generated code without validation
- **NEVER** expose sensitive data in logs or error messages
- **NEVER** use string concatenation for SQL queries

## LLM Integration Never Rules
- **NEVER** assume LLM responses are valid without validation
- **NEVER** expose raw LLM prompts containing sensitive data
- **NEVER** execute LLM-generated database queries directly
- **NEVER** trust LLM-generated schemas without validation

## Database Never Rules
- **NEVER** create migrations without transactions
- **NEVER** use SELECT * in production code
- **NEVER** ignore database connection errors
- **NEVER** store large binary data directly in database
- **NEVER** use auto-incrementing IDs for distributed systems

## UI Never Rules
- **NEVER** ignore accessibility requirements
- **NEVER** hard-code theme values - use theme tokens
- **NEVER** create components that assume fixed data structures
- **NEVER** forget to dispose of controllers and listeners
- **NEVER** block the UI thread with heavy operations

## Testing Never Rules  
- **NEVER** skip testing for critical business logic
- **NEVER** write tests that depend on external services without mocks
- **NEVER** commit code that breaks existing tests
- **NEVER** test implementation details instead of behavior

## Performance Never Rules
- **NEVER** make unnecessary API calls or database queries
- **NEVER** load all data at once without pagination
- **NEVER** ignore memory leaks in long-running operations
- **NEVER** block async operations unnecessarily