---
type: "agent_requested"
---

# LLM Integration Rules for Vertoie

## Prompt Engineering Standards
- Design clear, structured prompts with sections
- Include validation instructions in prompts
- Maintain conversation context across interactions
- Use JSON schemas for structured responses

```go
// ✅ Good: Structured prompt design
func (s *llmService) buildBusinessAnalysisPrompt(description string) string {
    return fmt.Sprintf(`
Analyze this business description and return a JSON response:

BUSINESS: %s

REQUIRED OUTPUT FORMAT:
{
  "business_type": "string",
  "industry": "string", 
  "key_processes": ["string"],
  "recommended_modules": ["string"],
  "data_requirements": {
    "entities": ["string"],
    "relationships": ["string"]
  }
}

VALIDATION RULES:
- business_type must be specific (not "general business")
- industry must be from standard classifications
- key_processes should be 3-7 items
- recommended_modules should match business needs
`, description)
}
```

## Response Processing
- Implement robust JSON parsing with error handling
- Validate all LLM responses before using them
- Provide fallback options when <PERSON><PERSON> fails
- Log parsing failures for debugging

## Business Analysis Patterns
- Extract specific business requirements from descriptions
- Classify business type and industry accurately
- Identify key business processes and workflows
- Generate appropriate module recommendations
- Create JSONB schemas for business data models

## Module Generation Standards
- Generate complete module specifications from LLM analysis
- Include UI component selections based on business needs
- Create workflow definitions for business processes
- Generate validation rules for data models
- Provide customization options for users

## Caching Strategy
- Cache LLM responses by prompt hash to reduce API calls
- Implement TTL for cached responses (24 hours for business analysis)
- Cache module templates for similar business types
- Store successful prompt patterns for reuse

## Error Handling and Fallbacks
- Handle LLM API failures gracefully with fallbacks
- Provide default module templates when LLM fails
- Implement retry logic with exponential backoff
- Log all LLM interactions for debugging and training

## Performance Optimization
- Implement rate limiting for LLM API calls
- Use async processing for non-critical LLM operations
- Batch similar requests when possible
- Monitor LLM response times and costs

## Security and Validation
- Sanitize all user inputs before sending to LLM
- Validate LLM-generated code before execution
- Implement content filtering for inappropriate responses
- Never execute LLM-generated code without validation