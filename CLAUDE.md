# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

Vertoie is an AI-powered business software builder that creates custom Flutter applications through conversational AI. Instead of one-size-fits-all software, Vertoie generates tailored modules and data models based on each business's specific needs.

### Platform Components
- **Platform Backend** (Go/Fiber): Core API server at `:8000`
- **Web Server** (Go/Fiber + HTMX): Platform interface at `:8001`  
- **Flutter Components** (vertoie_ui): Reusable UI library with V-prefixed components
- **Generated Business Apps** (Flutter): Custom applications created by AI
- **Database**: PostgreSQL with multi-schema design

### Supported Business Types
- **Service Businesses**: Pool service, HVAC, property management, salon/medspa
- **Professional Services**: Consulting, legal, healthcare, real estate
- **Product Businesses**: Retail, manufacturing, e-commerce, restaurants
- **Hybrid Businesses**: Agencies, franchises, contractors

## Development Environment

This project uses Nix flake for reproducible development environments. **Note:** All development tools are provided by <PERSON> except Flutter, which is used from the host system.

### Setup Commands
```bash
# Enter development environment
nix develop

# Run all services in development mode
nix run .#dev

# Build all components
nix run .#build

# Run tests
nix run .#test

# Database migrations
nix run .#migrate-up
nix run .#migrate-down
nix run .#migrate-status
```

### Individual Service Commands
```bash
# Backend API server (port 8000)
cd platform/backend && go run main.go

# Web server (port 8001)  
cd web && go run main.go

# Flutter UI components (requires Flutter 3.32.0+ from host system)
cd components/vertoie_ui && flutter run

# Hot reload for Go services
cd platform/backend && air
cd web && air
```

## Architecture

### Backend Technology Stack
- **Language**: Go (Golang) 1.21+
- **HTTP Framework**: Fiber (replaced FastHTTP based on reference)
- **WebSockets**: For real-time communication
- **REST API**: For external integrations

### Database Schema
- **Platform Schema** (`vertoie`): Users, organizations, plans, subscriptions
- **Tenant Schemas** (planned): Individual business data with custom schemas
- **JSONB Storage**: Flexible data models for AI-generated requirements
- **Database**: PostgreSQL (with planned migration to YugabyteDB for JSONB optimization)

### Authentication
- **Passwordless**: Magic links via email (Mailgun)
- **JWT Tokens**: Session management
- **Role-Based Access**: Organization membership with roles
- **mTLS**: Planned for enhanced security

### AI Integration
- **Groq API**: Meta Llama models for business analysis (Llama 3.1 8B default, 70B for complex cases)
- **Conversation State**: Persistent business context gathering in `ConversationThreads`
- **Module Recommendation**: AI-driven suggestions based on business type
- **Code Generation** (planned): Flutter app scaffold generation
- **Version Management**: AI-generated schema evolution and migrations

## Key Models

### Platform Backend (`platform/backend/models/`)
- `User`: Platform business owners
- `Organization`: Business entities with status tracking
- `Plan`: 5-tier pricing structure ($25-$399)
- `BusinessContext`: AI-gathered business requirements

### Web Server (`web/internal/`)
- `database/`: Database connections and queries
- `handlers/`: HTTP handlers for auth, organizations, onboarding
- `llm/`: Groq API integration and business context analysis

### Shared Models (`models/`)
- Core structs shared between backend and web services
- Database migration structs

## Development Workflow

### Adding New Features
1. **Database Changes**: Add migrations in `migrations/` directory
2. **Models**: Update shared models in `models/` directory
3. **Backend API**: Add endpoints in `platform/backend/handlers.go`
4. **Web Interface**: Add handlers in `web/internal/handlers/`
5. **Templates**: Update HTMX templates in `web/templates/`

### Testing
```bash
# Go tests
go test ./...

# Flutter tests
cd components/vertoie_ui && flutter test

# Database migration tests
nix run .#test-migrations
```

### Linting
```bash
# Go linting
golangci-lint run

# Flutter linting
cd components/vertoie_ui && flutter analyze

# Format code
goimports -w .
dart format .
```

## Multi-Tenant Architecture

### Platform vs Tenant Data
- **Platform Schema**: All users, organizations, plans, billing
- **Tenant Schemas**: Individual business data isolated per organization
- **Schema Evolution**: Version management for generated business apps

### Database Connections
- Platform services connect to `vertoie` schema
- Generated apps connect to tenant-specific schemas
- Connection pooling handles multi-tenant access

## AI-Generated Applications

### Business Context Flow
1. User describes business through conversational interface
2. LLM analyzes requirements and extracts business context
3. AI recommends relevant modules and features
4. System generates custom Flutter application
5. Version management with testing and deployment

### Module System
- **Core Modules**: Authentication, users, basic CRUD
- **Business Modules**: Inventory, CRM, accounting, etc.
- **Custom Modules**: AI-generated based on specific business needs

### Version Management
- **Version States**: Stable (production), Development (testing), Historical (archived)
- **Schema Evolution**: Forward and backward compatibility with automated migrations
- **Testing Environment**: Preview mode with isolated test data
- **Rollback Capability**: Quick reversion to previous stable versions
- **A/B Testing**: Compare different versions side-by-side

## Environment Configuration

### Required Environment Variables
```bash
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/vertoie

# AI Services
GROQ_API_KEY=your_groq_api_key

# Email
MAILGUN_API_KEY=your_mailgun_key
MAILGUN_DOMAIN=your_domain

# Authentication
JWT_SECRET=your_jwt_secret
```

### Ports
- Backend API: 8000
- Web Server: 8001
- Flutter Development: varies (typically 8080+)

## Key Directories

- `platform/backend/`: Core API server with business logic
- `web/`: Web interface for platform management
- `components/vertoie_ui/`: Reusable Flutter UI components
- `models/`: Shared Go models and database schemas
- `migrations/`: Database migration files
- `nix/`: Development environment and build scripts
- `reference/`: Architecture documentation and decisions
- `.cursor/`: Editor-specific development guidelines and rules

## Development Guidelines

### Critical Rules - NEVER VIOLATE
- **ALWAYS** use `.envrc` with `nix` commands. Use `direnv allow` or `source .envrc`
- **DO NOT** do anything that was not explicitly asked
- **ALWAYS** update STATUS.md AND TASKS.md after each task completion
- **ALL** database migrations must be wrapped in transaction `BEGIN...COMMIT`
- **ALL** related table inserts should be wrapped in a transaction `BEGIN...COMMIT`
- **NEVER** ignore errors - always handle them explicitly

### Code Quality Standards
- Use meaningful, descriptive names for variables, functions, and classes
- Keep functions under 20 lines when possible
- Single responsibility principle - one function, one purpose
- DRY - Don't repeat yourself, extract common patterns
- Use early returns to reduce nesting
- Write self-documenting code

### Error Handling Requirements
- **Go**: Always check and return errors explicitly `if err != nil { return fmt.Errorf("context: %w", err) }`
- **Dart**: Use proper exception handling with try-catch blocks
- Never swallow errors silently
- Provide meaningful error messages with context

### Security Standards
- Always validate user inputs, especially LLM-generated content
- Use parameterized queries to prevent SQL injection
- Encrypt sensitive data at rest and in transit
- Implement proper authentication and authorization checks
- **NEVER** trust user input without validation
- **NEVER** store passwords in plain text
- **NEVER** execute LLM-generated code without validation
- **NEVER** expose sensitive data in logs or error messages

### Performance Requirements
- UI components must render in <16ms for 60fps
- API endpoints should respond in <200ms
- LLM operations should complete in <5s
- Database queries should execute in <50ms for simple operations

### Go Backend Standards
- Use `context.Context` for all operations with timeouts
- Pass context as the first parameter
- Use JSONB for flexible schemas generated by LLMs
- Use UUIDs for primary keys
- All migrations must be wrapped in `BEGIN...COMMIT` transactions
- Use parameterized queries to prevent SQL injection

### Flutter/Dart Standards
- Requires Dart SDK ^3.8.1 and Flutter >=3.32.0 from host system
- Always use null safety
- Avoid `!` operator when possible - use `??` and `?.` operators instead
- Use `const` constructors whenever possible
- Prefer StatelessWidget over StatefulWidget when state isn't needed
- Keep widgets focused and under 20 lines
- Use composition over inheritance

### LLM Integration Standards
- Design interfaces for testability with LLM services
- Validate and sanitize all LLM-generated content before use
- Implement robust JSON parsing with error handling
- Cache LLM responses to reduce costs and improve performance
- Use structured prompts with clear validation rules
- Handle LLM API failures gracefully with fallbacks

### Database Standards
- Use `vertoie` schema for platform data
- Create separate schemas for each user's business data
- Use proper indexing for JSONB queries
- Implement connection pooling for database operations
- Use row-level security (RLS) where appropriate
- Regular database backups and point-in-time recovery

### UI Component Standards
- All components must start with `V` prefix: `VButton`, `VInput`, `VCalendar`
- **ALWAYS** use theme properties, never hardcoded values
- Use semantic density properties: `density.horizontalPadding`, `density.verticalPadding`
- **NEVER** use scale-based properties: `density.sm`, `density.md`, `density.lg`
- Support both light and dark modes automatically
- Implement accessibility support (screen readers, keyboard navigation)
- Design components to handle dynamic JSONB data structures

## Design System Implementation Guidelines

### Web Platform Standards (HTMX-First Approach)
- **ALWAYS** use HTMX for server interactions when possible
- **ONLY** use JavaScript/Alpine.js when HTMX cannot accomplish the task
- Prefer server-side rendering and HTMX attributes over client-side JavaScript
- Use Alpine.js only for simple UI state management (show/hide, toggles)
- Avoid complex client-side state management - keep state on the server

### Web Platform Design Patterns (HTMX + Go)

#### Card Components
- **Border Radius**: Use `12px` for all cards (dashboard, modules, etc.)
- **Pricing Cards Exception**: Use `16px` border radius 
- **Border**: `1px solid var(--border-light)`
- **Shadow**: `var(--shadow-sm)` default, `var(--shadow-md)` on hover
- **Hover Transform**: `translateY(-1px)` for subtle cards, `translateY(-2px)` for interactive cards
- **Padding**: `2.4rem` (var(--space-6)) for card content
- **Transitions**: `all 0.2s ease` for standard cards

#### Grid Layouts
- **Module Grids**: Use CSS Grid with `grid-template-columns: repeat(auto-fit, minmax(32rem, 1fr))`
- **Dashboard Grids**: `grid-template-columns: repeat(auto-fit, minmax(40rem, 1fr))`
- **Gap**: Use `2.4rem` (var(--space-6)) between cards
- **Responsive**: Auto-fit with minmax for automatic responsive behavior

#### Color Usage
- **Primary Actions**: `#FF6B35` (--vertoie-orange)
- **Hover State**: `#E65100` (--vertoie-orange-dark)
- **Selected State**: Orange border with `box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1)`
- **Focus State**: Same as selected state
- **Badges**: Use transparent backgrounds with 10% opacity of semantic color

#### Typography
- **Card Titles**: `1.6rem` font-size, `600` font-weight
- **Body Text**: `1.4rem` font-size, `400` font-weight
- **Small Text**: `1.3rem` font-size
- **Muted Text**: Use `var(--text-secondary)` or `var(--text-muted)`

#### Form Elements
- **Inputs**: `border-radius: 6px` (var(--radius-md))
- **Buttons**: `border-radius: 6px` (var(--radius-md))
- **Focus**: Orange border with 3px spread shadow at 10% opacity

#### Module Cards Specific
- **Checkbox**: `2.4rem` square, `border-radius: 4px` (var(--radius-sm))
- **Badge Pills**: Use `border-radius: 9999px` (var(--radius-full))
- **Benefits Section**: Border-top separator with icon + text

## Development Phases

### Phase 1: Foundation (Current)
- [x] Project structure and development environment  
- [x] Basic backend with Fiber framework
- [x] Database setup with PostgreSQL
- [x] Component library foundation
- [ ] LLM integration and basic analysis
- [ ] Module generation framework
- [ ] Vertoie platform UI (Fiber/HTMX)
- [ ] Basic version management system

### Phase 2: AI Generation
- [ ] Business analysis and requirement extraction
- [ ] Flutter scaffold generation system
- [ ] Dynamic data model creation
- [ ] Module template system
- [ ] Generated app testing and validation
- [ ] Advanced version management with testing environments

### Phase 3: Platform Enhancement
- [ ] Advanced conversation interface
- [ ] Module customization tools
- [ ] Business app deployment system
- [ ] Performance optimization
- [ ] Security hardening
- [ ] Schema evolution and migration tools

### Phase 4: Production Readiness
- [ ] User management and billing
- [ ] Multi-tenant architecture
- [ ] Monitoring and analytics
- [ ] Documentation and support
- [ ] Production deployment
- [ ] Enterprise features and integrations