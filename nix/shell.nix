{ pkgs, devTools }:

pkgs.mkShell {
  buildInputs = devTools;

  shellHook = ''
    echo "🚀 Vertoie development environment loaded!"
    echo ""
    echo "Available tools:"
    echo "  - Go $(go version | cut -d' ' -f3)"
    echo "  - Flutter $(flutter --version | head -1 | cut -d' ' -f2)"
    echo "  - Node.js $(node --version)"
    echo "  - PostgreSQL $(postgres --version | cut -d' ' -f3)"
    echo ""
    echo "Quick start commands:"
    echo "  nix run .#help       - Show available commands"
    echo "  nix run .#backend    - Run backend server"
    echo "  nix run .#web        - Run web server"
    echo "  nix run .#flutter    - Run Flutter app"
    echo "  nix run .#components - Run UI components example"
    echo ""
    echo "Environment variables are loaded from .envrc"
  '';
} 