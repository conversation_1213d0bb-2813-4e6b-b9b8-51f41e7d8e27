#!/bin/bash

echo "📊 Vertoie Development Status"
echo "=============================="
echo ""
echo "🔧 Environment:"
if command -v nix >/dev/null 2>&1; then echo "  ✅ Nix available"; else echo "  ❌ Nix not found"; fi
if command -v direnv >/dev/null 2>&1; then echo "  ✅ direnv available"; else echo "  ⚠️  direnv not found (optional)"; fi
if command -v code >/dev/null 2>&1; then echo "  ✅ VS Code available"; else echo "  ⚠️  VS Code not found (optional)"; fi
echo ""
echo "🐹 Go:"
if command -v go >/dev/null 2>&1; then echo "  ✅ Go $(go version | cut -d' ' -f3)"; else echo "  ❌ Go not found"; fi
echo ""
echo "🎯 Dart/Flutter:"
if command -v dart >/dev/null 2>&1; then echo "  ✅ Dart $(dart --version | cut -d' ' -f4)"; else echo "  ❌ Dart not found"; fi
if command -v flutter >/dev/null 2>&1; then echo "  ✅ Flutter $(flutter --version | head -n1 | cut -d' ' -f2)"; else echo "  ❌ Flutter not found"; fi
echo ""
echo "🗄️ Databases:"
if command -v psql >/dev/null 2>&1; then echo "  ✅ PostgreSQL available"; else echo "  ❌ PostgreSQL not found"; fi
if command -v redis-cli >/dev/null 2>&1; then echo "  ✅ Redis available"; else echo "  ❌ Redis not found"; fi
echo ""
echo "📂 Project Structure:"
if [ -d "platform/backend" ]; then echo "  ✅ Platform Backend"; else echo "  ❌ Platform Backend missing"; fi
if [ -d "platform/frontend" ]; then echo "  ✅ Platform Frontend"; else echo "  ⚠️  Platform Frontend missing"; fi
if [ -d "web" ]; then echo "  ✅ Web Server"; else echo "  ❌ Web Server missing"; fi
if [ -d "components/vertoie_ui" ]; then echo "  ✅ UI Components"; else echo "  ❌ UI Components missing"; fi
if [ -d "brand" ]; then echo "  ✅ Brand Assets"; else echo "  ❌ Brand Assets missing"; fi 