#!/bin/bash

echo "🎨 Formatting code..."
find . -name "*.go" -not -path "./vendor/*" -exec goimports -w {} \; 2>/dev/null || echo "goimports not available"
find . -name "*.dart" -not -path "./build/*" -exec dart format {} \; 2>/dev/null || echo "dart format not available"
find . -name "*.nix" -exec nixpkgs-fmt {} \; 2>/dev/null || echo "nixpkgs-fmt not available"
find . -name "*.js" -o -name "*.ts" -o -name "*.json" | grep -v node_modules | xargs prettier --write 2>/dev/null || echo "prettier not available" 