#!/bin/bash

echo "Vertoie Development Commands"
echo "============================="
echo ""
echo "Core Commands:"
echo "  setup    - Set up the development environment"
echo "  dev      - Start all development servers"
echo "  help     - Show this help message"
echo ""
echo "Service Commands:"
echo "  backend  - Run platform backend server"
echo "  web      - Run web server with hot reload"
echo "  flutter  - Run Flutter frontend"
echo "  components - Run UI components example"
echo ""
echo "Backend Commands:"
echo "  backend-build - Build backend binary"
echo "  backend-test  - Run backend tests"
echo "  backend-lint  - Lint backend code"
echo ""
echo "Migration Commands:"
echo "  migrate-up     - Run platform migrations"
echo "  migrate-down   - Rollback migrations (use: nix run .#migrate-down <count>)"
echo "  migrate-status - Show migration status"
echo "  migrate-new    - Create new migration (use: nix run .#migrate-new <name>)"
echo "  migrate-reset  - Reset database (⚠️ DANGER: removes all data)"
echo ""
echo "Development Commands:"
echo "  test     - Run all tests"
echo "  build    - Build all applications"
echo "  format   - Format all code"
echo "  lint     - Lint all code"
echo "  clean    - Clean build artifacts"
echo "  deps     - Get all dependencies"
echo ""
echo "Utility Commands:"
echo "  status   - Show development environment status"
echo "  check-ports - Check which development ports are in use"
echo ""
echo "Examples:"
echo "  nix run .#migrate-new add_user_settings"
echo "  nix run .#migrate-down 2"
echo "  nix run .#backend-test" 