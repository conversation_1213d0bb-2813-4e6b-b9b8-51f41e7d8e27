#!/bin/bash

set -e

echo "🔨 Resetting dev database..."

# Use a different DB to connect to (e.g., postgres) because you can't drop a DB you're connected to
# Remove the DB name part from the DATABASE_URL and replace it with 'postgres'
BASE_URL="${DATABASE_URL%/*}"
DB_NAME="${DATABASE_URL##*/}"
# Strip query params from DB_NAME
DB_NAME="${DB_NAME%%\?*}"

# Connect to 'postgres' DB to run drop/create
ADMIN_URL="${BASE_URL}/postgres"

# Drop and recreate the target DB
psql "$ADMIN_URL" -c "DROP DATABASE IF EXISTS \"$DB_NAME\";"
psql "$ADMIN_URL" -c "CREATE DATABASE \"$DB_NAME\";"
nix run .#migrate-up

echo "✅ Dev database reset"