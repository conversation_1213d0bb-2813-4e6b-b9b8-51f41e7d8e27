#!/bin/bash

echo "🚀 Setting up Vertoie development environment..."

# Create necessary directories
mkdir -p .go/bin
mkdir -p tmp

# Initialize Go modules if they don't exist
if [ ! -f "platform/backend/go.mod" ]; then
    echo "Initializing Go module for platform backend..."
    cd platform/backend && go mod init vertoie/platform-backend
fi

if [ ! -f "web/go.mod" ]; then
    echo "Initializing Go module for web server..."
    cd web && go mod init vertoie/web-server
fi

# Get dependencies
echo "📦 Getting dependencies..."
find . -name go.mod -execdir sh -c 'echo "Getting Go deps in $$(pwd)" && go mod tidy' \;
find . -name "pubspec.yaml" -execdir sh -c 'echo "Getting Flutter deps in $$(pwd)" && flutter pub get' \;

echo "✅ Setup complete!" 