{ pkgs, unstablePkgs }:

let
  # Base development tools
  baseTools = with pkgs; [
    # Go development
    go
    golangci-lint
    gotools
    go-migrate
    air # Live reload for Go
    
    # Database tools
    postgresql
    
    # Web development tools
    nodejs_22
    tailwindcss_4
    
    # Development utilities
    git
    gnumake
    jq
    curl
  ];

  # Flutter/Dart tools (from unstable)
  flutterTools = [
    unstablePkgs.flutter
    unstablePkgs.dart
  ];

  # Platform-specific tools
  darwinTools = pkgs.lib.optionals pkgs.stdenv.isDarwin [
    pkgs.darwin.apple_sdk.frameworks.Security
    pkgs.darwin.apple_sdk.frameworks.SystemConfiguration
  ];

  linuxTools = pkgs.lib.optionals pkgs.stdenv.isLinux [
    pkgs.pkg-config
    pkgs.openssl
    pkgs.libgtk3
    pkgs.glib
  ];

in baseTools ++ flutterTools ++ darwinTools ++ linuxTools 