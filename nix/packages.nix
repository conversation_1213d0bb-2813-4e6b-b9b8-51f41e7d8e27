{ pkgs, unstablePkgs }:

let
  # Helper function to create a script that runs a shell script
  mkScript = name: scriptPath: pkgs.writeShellScriptBin name ''
    exec ${scriptPath}
  '';

  # Get the path to the scripts directory - use builtins.path to make it explicit
  scriptsDir = builtins.path { path = ./scripts; name = "vertoie-scripts"; };

in {
  # Core commands
  setup = mkScript "setup" "${scriptsDir}/setup.sh";
  dev = mkScript "dev" "${scriptsDir}/dev.sh";
  help = mkScript "help" "${scriptsDir}/help.sh";

  # Service commands
  backend = mkScript "backend" "${scriptsDir}/backend.sh";
  web = mkScript "web" "${scriptsDir}/web.sh";
  flutter = mkScript "flutter" "${scriptsDir}/flutter.sh";
  components = mkScript "components" "${scriptsDir}/components.sh";

  # Backend commands
  backend-build = mkScript "backend-build" "${scriptsDir}/backend-build.sh";
  backend-test = mkScript "backend-test" "${scriptsDir}/backend-test.sh";
  backend-lint = mkScript "backend-lint" "${scriptsDir}/backend-lint.sh";

  # Migration commands
  migrate-up = mkScript "migrate-up" "${scriptsDir}/migrate-up.sh";
  migrate-down = mkScript "migrate-down" "${scriptsDir}/migrate-down.sh";
  migrate-status = mkScript "migrate-status" "${scriptsDir}/migrate-status.sh";
  migrate-new = mkScript "migrate-new" "${scriptsDir}/migrate-new.sh";
  migrate-reset = mkScript "migrate-reset" "${scriptsDir}/migrate-reset.sh";

  # Development commands
  test = mkScript "test" "${scriptsDir}/test.sh";
  build = mkScript "build" "${scriptsDir}/build.sh";
  format = mkScript "format" "${scriptsDir}/format.sh";
  lint = mkScript "lint" "${scriptsDir}/lint.sh";
  clean = mkScript "clean" "${scriptsDir}/clean.sh";
  deps = mkScript "deps" "${scriptsDir}/deps.sh";
  reset-dev-db = mkScript "reset-dev-db" "${scriptsDir}/reset-dev-database.sh";

  # Utility commands
  status = mkScript "status" "${scriptsDir}/status.sh";
  check-ports = mkScript "check-ports" "${scriptsDir}/check-ports.sh";
  shell = mkScript "shell" "${scriptsDir}/shell.sh";
} 