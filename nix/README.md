# Vertoie Nix Configuration

This directory contains the modular Nix configuration for the Vertoie development environment.

## Structure

- `dev-tools.nix` - Defines all development tools and dependencies
- `shell.nix` - Configures the development shell environment
- `packages.nix` - Defines packages that run shell scripts
- `scripts/` - Directory containing all shell scripts
- `README.md` - This documentation file

## Files

### dev-tools.nix

Contains all the development tools needed for the Vertoie project:

- **Go development**: `go`, `golangci-lint`, `gotools`, `go-migrate`, `air`
- **Database tools**: `postgresql`
- **Web development**: `nodejs_22`
- **Flutter/Dart**: `flutter`, `dart` (from unstable channel)
- **Development utilities**: `git`, `gnumake`, `jq`, `curl`
- **Platform-specific tools**: macOS frameworks, Linux libraries

### shell.nix

Configures the development shell with:

- Clean shell hook for information display
- No environment variables (handled by `.envrc`)

### packages.nix

Defines packages that execute shell scripts from `nix/scripts/`:

#### Core Commands
- `setup` - Initialize the development environment
- `dev` - Start all development servers
- `help` - Show available commands

#### Service Commands
- `backend` - Run platform backend server
- `web` - Run web server with hot reload
- `flutter` - Run Flutter frontend
- `components` - Run UI components example

#### Backend Commands
- `backend-build` - Build backend binary
- `backend-test` - Run backend tests
- `backend-lint` - Lint backend code

#### Migration Commands
- `migrate-up` - Run platform migrations
- `migrate-down` - Rollback migrations
- `migrate-status` - Show migration status
- `migrate-new` - Create new migration
- `migrate-reset` - Reset database

#### Development Commands
- `test` - Run all tests
- `build` - Build all applications
- `format` - Format all code
- `lint` - Lint all code
- `clean` - Clean build artifacts
- `deps` - Get all dependencies
- `status` - Show development environment status
- `check-ports` - Check which development ports are in use

### scripts/

Contains all shell scripts that implement the actual functionality:

- `setup.sh` - Environment setup
- `dev.sh` - Start all servers
- `backend.sh` - Run backend
- `web.sh` - Run web server
- `flutter.sh` - Run Flutter app
- `components.sh` - Run UI components
- `backend-build.sh` - Build backend
- `backend-test.sh` - Test backend
- `backend-lint.sh` - Lint backend
- `migrate-up.sh` - Run migrations
- `migrate-down.sh` - Rollback migrations
- `migrate-status.sh` - Show migration status
- `migrate-new.sh` - Create migration
- `migrate-reset.sh` - Reset database
- `test.sh` - Run tests
- `build.sh` - Build applications
- `format.sh` - Format code
- `lint.sh` - Lint code
- `clean.sh` - Clean artifacts
- `deps.sh` - Get dependencies
- `status.sh` - Show status
- `check-ports.sh` - Check ports
- `shell.sh` - Enter shell
- `help.sh` - Show help

## Environment Configuration

All environment variables are configured in the root `.envrc` file:

```bash
# Database URLs
export DATABASE_URL="*******************************************/vertoie_dev?sslmode=disable"
export WEB_DATABASE_URL="********************************************/vertoie_dev?sslmode=disable"
export REDIS_URL="redis://192.168.1.41:6379"

# Development settings
export GO_ENV="development"
export PORT="8000"
export WEB_PORT="8001"

# Go environment
export GOPATH="$PWD/.go"
export PATH="$GOPATH/bin:$PATH"
export GO111MODULE="on"
export CGO_ENABLED="1"
```

## Usage

### Enter Development Shell
```bash
nix develop
```

### Run Commands
```bash
# Setup environment
nix run .#setup

# Start all development servers
nix run .#dev

# Run specific services
nix run .#backend
nix run .#web
nix run .#flutter
nix run .#components

# Development tasks
nix run .#test
nix run .#build
nix run .#format
nix run .#lint
nix run .#clean
nix run .#deps

# Utilities
nix run .#status
nix run .#check-ports
```

### Show Help
```bash
nix run .#help
# or simply
nix run .
```

## Project Structure

The Vertoie project consists of:

- **Platform Backend** (`platform/backend/`) - Go backend server
- **Platform Frontend** (`platform/frontend/`) - Flutter mobile app
- **Web Server** (`web/`) - Go web server
- **UI Components** (`components/vertoie_ui/`) - Flutter UI component library
- **Brand Assets** (`brand/`) - Logos, colors, and design assets

## Development Workflow

### Typical Development Session

1. **Setup once:**
   ```bash
   nix run .#setup
   ```

2. **Start development servers:**
   ```bash
   nix run .#dev
   ```

3. **Or run individual services in separate terminals:**
   ```bash
   # Terminal 1
   nix run .#backend
   
   # Terminal 2  
   nix run .#web
   
   # Terminal 3
   nix run .#flutter
   ```

### Development Ports

- **Platform Backend**: `:8000`
- **Web Server**: `:8001`
- **Flutter DevTools**: `:3001`
- **PostgreSQL**: `:5434` (remote)
- **Redis**: `:6379` (remote)

Check port usage with:
```bash
nix run .#check-ports
```

## Benefits of This Structure

1. **Clean Separation**: Environment variables in `.envrc`, scripts in `nix/scripts/`
2. **Maintainable**: Easy to edit shell scripts without touching nix files
3. **Testable**: Shell scripts can be tested independently
4. **Readable**: Clear structure with minimal nix code
5. **Flexible**: Easy to add new scripts or modify existing ones 