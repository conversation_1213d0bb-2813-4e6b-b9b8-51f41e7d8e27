---
description: 
globs: **/*.css,**/*.scss,**/*.html,**/tailwind.config.*
alwaysApply: false
---
# CSS & Tailwind Rules for Vertoie

## CSS Organization Standards

### File Structure
- **ALWAYS** keep CSS in dedicated CSS files, never inline
- Use modular CSS organization with clear naming conventions
- Group related styles together in logical files
- Import CSS files properly in HTML or component files

```css
/* ✅ Good: Organized CSS file structure */
/* components/buttons.css */
.v-button {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
}

.v-button--primary {
  @apply bg-orange-500 text-white hover:bg-orange-600;
}

.v-button--secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300;
}
```

### Tailwind Integration
- **ALWAYS** use Tailwind utility classes as the foundation
- Use `@apply` directive to create reusable component classes
- Follow Tailwind's mobile-first responsive design approach
- Leverage Tailwind's design system for consistency

## Vertoie Brand Colors in Tailwind

### Primary Brand Colors
```css
/* tailwind.config.js - Extend with Vertoie colors */
module.exports = {
  theme: {
    extend: {
      colors: {
        'vertoie-orange': {
          50: '#FFF4F0',
          100: '#FFE5D9',
          200: '#FFCAB0',
          300: '#FFA87A',
          400: '#FF8A56',
          500: '#FF6B35', // Primary brand color
          600: '#E65100',
          700: '#CC4700',
          800: '#B33E00',
          900: '#992E00',
        },
        'vertoie-amber': {
          100: '#FDF6E3',
          400: '#FBBF24',
          500: '#F7931E', // Secondary brand color
          600: '#D97706',
          800: '#92400E',
        }
      }
    }
  }
}
```

### Using Brand Colors
```css
/* ✅ Good: Consistent brand color usage */
.v-primary-button {
  @apply bg-vertoie-orange-500 text-white hover:bg-vertoie-orange-600;
}

.v-secondary-button {
  @apply bg-vertoie-amber-500 text-white hover:bg-vertoie-amber-600;
}

.v-brand-gradient {
  @apply bg-gradient-to-r from-vertoie-orange-500 to-vertoie-amber-500;
}
```

## Component-Based CSS Architecture

### BEM-Like Naming with Tailwind
```css
/* ✅ Good: Component-based approach */
.v-card {
  @apply bg-white rounded-xl shadow-lg overflow-hidden;
}

.v-card__header {
  @apply px-6 py-4 border-b border-gray-200;
}

.v-card__content {
  @apply p-6;
}

.v-card__footer {
  @apply px-6 py-4 bg-gray-50 border-t border-gray-200;
}

/* Modifiers */
.v-card--elevated {
  @apply shadow-2xl;
}

.v-card--compact {
  @apply text-sm;
}
```

### Responsive Design Patterns
```css
/* ✅ Good: Mobile-first responsive design */
.v-layout-grid {
  @apply grid grid-cols-1 gap-4;
  @apply md:grid-cols-2 md:gap-6;
  @apply lg:grid-cols-3 lg:gap-8;
  @apply xl:grid-cols-4;
}

.v-hero-section {
  @apply py-12 px-4;
  @apply md:py-20 md:px-8;
  @apply lg:py-32;
}
```

## Reusability Patterns

### Utility Classes for Common Patterns
```css
/* ✅ Good: Reusable utility patterns */
.v-focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-vertoie-orange-500 focus:ring-offset-2;
}

.v-transition-smooth {
  @apply transition-all duration-200 ease-in-out;
}

.v-text-gradient {
  @apply bg-gradient-to-r from-vertoie-orange-500 to-vertoie-amber-500 bg-clip-text text-transparent;
}

.v-surface-elevated {
  @apply bg-white shadow-lg rounded-lg border border-gray-200;
}
```

### Layout Utilities
```css
/* ✅ Good: Common layout patterns */
.v-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.v-section {
  @apply py-16 lg:py-24;
}

.v-flex-center {
  @apply flex items-center justify-center;
}

.v-flex-between {
  @apply flex items-center justify-between;
}
```

## CSS Anti-Patterns to Avoid

### Never Use Inline Styles
```html
<!-- ❌ Bad: Inline styles -->
<button style="background-color: #FF6B35; padding: 8px 16px;">
  Click me
</button>

<!-- ✅ Good: CSS classes -->
<button class="v-button v-button--primary">
  Click me
</button>
```

### Avoid Magic Numbers
```css
/* ❌ Bad: Magic numbers and arbitrary values */
.bad-spacing {
  margin-top: 23px;
  padding-left: 17px;
  width: 387px;
}

/* ✅ Good: Tailwind spacing scale */
.good-spacing {
  @apply mt-6 pl-4 w-96;
}
```

### Don't Override Tailwind Defaults Unnecessarily
```css
/* ❌ Bad: Fighting against Tailwind */
.bad-override {
  font-size: 14.5px !important;
  line-height: 1.23 !important;
}

/* ✅ Good: Use Tailwind's design system */
.good-text {
  @apply text-sm leading-relaxed;
}
```

## Performance & Optimization

### CSS Organization for Performance
```css
/* ✅ Good: Organized imports */
/* main.css */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import './components/buttons.css';
@import './components/forms.css';
@import './components/layout.css';
@import 'tailwindcss/utilities';
```

### Efficient Tailwind Usage
- Use Tailwind's purge/content configuration to remove unused CSS
- Prefer Tailwind utilities over custom CSS when possible
- Group related utilities in component classes using `@apply`
- Use CSS custom properties for dynamic values

```css
/* ✅ Good: CSS custom properties for dynamic values */
.v-dynamic-card {
  @apply rounded-lg p-4 transition-colors;
  background-color: var(--card-bg-color, theme('colors.white'));
  border-color: var(--card-border-color, theme('colors.gray.200'));
}
```

## Dark Mode Support

### Tailwind Dark Mode Classes
```css
/* ✅ Good: Dark mode support */
.v-card-dark-mode {
  @apply bg-white dark:bg-gray-800;
  @apply text-gray-900 dark:text-white;
  @apply border-gray-200 dark:border-gray-700;
}

.v-button-dark-mode {
  @apply bg-vertoie-orange-500 hover:bg-vertoie-orange-600;
  @apply dark:bg-vertoie-orange-400 dark:hover:bg-vertoie-orange-500;
}
```

## Accessibility in CSS

### Focus States and Contrast
```css
/* ✅ Good: Accessible focus states */
.v-interactive {
  @apply focus:outline-none focus:ring-2 focus:ring-vertoie-orange-500;
  @apply focus:ring-offset-2 focus:ring-offset-white;
  @apply dark:focus:ring-offset-gray-800;
}

/* High contrast support */
@media (prefers-contrast: high) {
  .v-button {
    @apply border-2 border-current;
  }
}
```

### Reduced Motion Support
```css
/* ✅ Good: Respect motion preferences */
.v-animated {
  @apply transition-transform duration-200;
}

@media (prefers-reduced-motion: reduce) {
  .v-animated {
    @apply transition-none;
  }
}
```

## Documentation Standards

### CSS Comments
```css
/* ✅ Good: Minimal, purposeful comments */
/* Button component variants following Vertoie design system */
.v-button {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
}

/* Primary action button - Vertoie orange brand color */
.v-button--primary {
  @apply bg-vertoie-orange-500 text-white hover:bg-vertoie-orange-600;
}
```

### Class Naming Documentation
- Use `v-` prefix for all Vertoie component classes
- Follow BEM-like conventions: `block__element--modifier`
- Document color choices and brand compliance
- Keep comments at the component level, not for individual properties