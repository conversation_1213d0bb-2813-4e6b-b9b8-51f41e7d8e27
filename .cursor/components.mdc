---
description: 
globs: **/components/*.dart
alwaysApply: false
---
---
type: auto_attached
patterns: ["**/lib/src/components/**/*.dart", "**/lib/src/theme/**/*.dart", "**/test/**/*_test.dart"]
---

# Vertoie UI Component Architecture Rules

## Component Design Principles

### Clean Component Architecture
- Each component must have a single, clear responsibility
- Use composition over inheritance for complex components
- Follow the VWidget base class pattern for consistency
- Keep components under 200 lines when possible
- Extract complex logic into separate utility classes

### Theme System Integration
- **ALWAYS** use theme properties, never hardcoded values
- Use semantic density properties: `density.horizontalPadding`, `density.verticalPadding`, `density.height`
- **NEVER** use scale-based properties: `density.sm`, `density.md`, `density.lg`
- Use semantic colors: `colors.onSurface`, `colors.surface`, `colors.primary`
- Support both light and dark modes automatically

```dart
// ✅ Good: Proper theme integration
class VButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = VTheme.of(context);
    final colors = theme.colors;
    final density = theme.density;
    
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: density.horizontalPadding,
        vertical: density.verticalPadding,
      ),
      decoration: BoxDecoration(
        color: colors.primary,
        borderRadius: BorderRadius.circular(theme.cornerRadius.button),
      ),
      child: child,
    );
  }
}

// ❌ Bad: Hardcoded values
class BadButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.0), // Hardcoded
      decoration: BoxDecoration(
        color: Colors.blue, // Hardcoded
        borderRadius: BorderRadius.circular(8.0), // Hardcoded
      ),
      child: child,
    );
  }
}
```

## Component Naming Conventions

### Component Names
- All components must start with `V` prefix: `VButton`, `VInput`, `VCalendar`
- Use descriptive names: `VDatePicker` not `VPicker`
- Group related components: `VCheckbox`, `VCheckboxGroup`
- Variants use descriptive suffixes: `VButtonVariant.primary`, `VInputType.email`

### File Organization
```
lib/src/components/
├── buttons/
│   ├── v_button.dart
│   └── v_button_variant.dart
├── inputs/
│   ├── v_input.dart
│   ├── v_input_type.dart
│   └── v_text_area.dart
├── calendar/
│   ├── v_calendar.dart
│   ├── v_calendar_event.dart
│   └── v_date_picker.dart
```

## API Design Standards

### Constructor Patterns
```dart
// ✅ Good: Clean, consistent API
class VButton extends StatelessWidget {
  const VButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.variant = VButtonVariant.primary,
    this.isEnabled = true,
  });

  final VoidCallback? onPressed;
  final Widget child;
  final VButtonVariant variant;
  final bool isEnabled;
}
```

### Property Guidelines
- Use required parameters for essential properties
- Provide sensible defaults for optional parameters
- Use nullable types appropriately (`VoidCallback?` for optional callbacks)
- Group related properties into data classes when needed

### Callback Conventions
```dart
// ✅ Good: Descriptive callback names
final ValueChanged<DateTime>? onDateSelected;
final VoidCallback? onPressed;
final ValueChanged<String>? onTextChanged;
final ValueChanged<VCalendarEvent>? onEventTapped;

// ❌ Bad: Generic callback names
final Function? callback;
final VoidCallback? onTap; // Too generic for specific actions
```

## Dynamic Data Handling

### LLM-Generated Content Support
- Design components to handle dynamic JSONB data structures
- Use `Map<String, dynamic>` for flexible data models
- Implement runtime validation for dynamic schemas
- Provide fallbacks for missing or invalid data

```dart
// ✅ Good: Flexible data handling
class VDynamicFormField extends StatelessWidget {
  const VDynamicFormField({
    super.key,
    required this.fieldDefinition,
    required this.onChanged,
  });

  final Map<String, dynamic> fieldDefinition;
  final ValueChanged<dynamic> onChanged;

  @override
  Widget build(BuildContext context) {
    final fieldType = fieldDefinition['type'] as String? ?? 'text';
    final isRequired = fieldDefinition['required'] as bool? ?? false;
    final label = fieldDefinition['label'] as String? ?? 'Field';
    
    return _buildFieldByType(fieldType, label, isRequired);
  }
}
```

### Calendar System Integration
- Follow the composable calendar architecture
- Use VCalendarEvent model for all calendar data
- Support color coding (status, category, client-based)
- Implement availability management features
- Use proper event rendering with overflow handling

## Accessibility Requirements

### Screen Reader Support
- All interactive components must have proper semantics
- Use `Semantics` widget for custom components
- Provide meaningful labels and hints
- Support voice-over navigation

```dart
// ✅ Good: Accessibility support
return Semantics(
  label: 'Calendar event: ${event.title}',
  hint: 'Double tap to edit event',
  child: GestureDetector(
    onTap: onEventTapped,
    child: eventWidget,
  ),
);
```

### Keyboard Navigation
- Support tab navigation for all interactive elements
- Implement proper focus management
- Use `FocusScope` for complex components
- Handle keyboard shortcuts where appropriate

### High Contrast Support
- Use theme colors that automatically support high contrast
- Test components in both light and dark modes
- Ensure proper color contrast ratios (WCAG AA compliance)
- Use semantic colors that adapt to system settings

## State Management Patterns

### Local State
- Use `useState` for simple component state
- Keep state as local as possible
- Use proper disposal for controllers and listeners

```dart
// ✅ Good: Proper state management
class VInput extends StatefulWidget {
  @override
  State<VInput> createState() => _VInputState();
}

class _VInputState extends State<VInput> {
  late final TextEditingController _controller;
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }
}
```

### Theme State
- Always use `VTheme.of(context)` to access theme
- Never cache theme values across builds
- Support theme changes at runtime
- Use theme-aware color calculations

## Performance Optimization

### Rendering Performance
- Use `const` constructors whenever possible
- Implement proper `build` method optimization
- Use `RepaintBoundary` for expensive components
- Avoid unnecessary rebuilds with proper key usage

### Memory Management
- Dispose of all controllers and listeners
- Use weak references for long-lived callbacks
- Implement proper lifecycle management
- Avoid memory leaks in stateful components

```dart
// ✅ Good: Performance optimization
class VCalendar extends StatefulWidget {
  const VCalendar({
    super.key,
    required this.events,
    this.onEventTapped,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: CustomPaint(
        painter: CalendarPainter(
          events: widget.events,
          theme: VTheme.of(context),
        ),
        child: gestureLayer,
      ),
    );
  }
}
```

## Testing Requirements

### Widget Tests
- Test all public API methods and properties
- Test theme integration across density settings
- Test accessibility features
- Mock external dependencies

### Golden Tests
- Create golden tests for visual regression testing
- Test all component variants and states
- Test both light and dark themes
- Test different density settings

### Accessibility Tests
- Use semantic testing helpers
- Test screen reader announcements
- Verify keyboard navigation
- Test high contrast support

```dart
// ✅ Good: Comprehensive testing
testWidgets('VButton supports all variants', (tester) async {
  for (final variant in VButtonVariant.values) {
    await tester.pumpWidget(
      VTestApp(
        child: VButton(
          onPressed: () {},
          variant: variant,
          child: Text('Test'),
        ),
      ),
    );
    
    expect(find.byType(VButton), findsOneWidget);
    await expectLater(
      find.byType(VButton),
      matchesGoldenFile('button_${variant.name}.png'),
    );
  }
});
```

## Documentation Standards

### Component Documentation
- Document all public properties and methods
- Provide usage examples for complex components
- Include accessibility considerations
- Document theme integration points

### Code Comments
- Comment complex business logic only
- Explain "why" not "what"
- Document any workarounds or platform-specific code
- Keep comments up-to-date with code changes

## Industry Module Integration

### Business Logic Separation
- Components should be UI-focused, not business logic
- Use callbacks to communicate with business logic
- Support industry-specific customization through themes
- Design for reusability across different business types

### Calendar-Centric Design
- Many components should integrate with calendar system
- Support appointment scheduling workflows
- Handle client/resource management
- Enable availability and booking management

### Voice Command Integration Points
- Design components with voice command accessibility
- Use clear, semantic labels for voice recognition
- Support voice-driven navigation and actions
- Provide audio feedback for important actions