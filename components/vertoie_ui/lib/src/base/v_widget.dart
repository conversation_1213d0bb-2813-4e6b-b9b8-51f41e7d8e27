import 'package:flutter/material.dart';
import '../theme/v_theme_data.dart';
import '../theme/v_theme_provider.dart';
import '../theme/v_theme.dart';

/// Base class for all Vertoie UI widgets.
/// Provides common functionality and ensures consistent patterns across components.
abstract class VWidget extends StatelessWidget {
  const VWidget({super.key});

  /// Gets the theme data from the current context
  VThemeData getTheme(BuildContext context) {
    return VThemeProvider.of(context);
  }

  /// Gets theme colors for convenience
  VColors getColors(BuildContext context) {
    return getTheme(context).colors;
  }

  /// Gets theme density for convenience (includes spacing values)
  VComponentDensity getDensity(BuildContext context) {
    return getTheme(context).componentDensity;
  }

  /// Gets theme corners for convenience
  VCorners getCorners(BuildContext context) {
    return getTheme(context).corners;
  }

  /// Gets theme elevation for convenience
  VElevation getElevation(BuildContext context) {
    return getTheme(context).elevation;
  }



  /// Gets theme motion for convenience
  VMotion getMotion(BuildContext context) {
    return getTheme(context).motion;
  }
}

/// Base class for stateful Vertoie UI widgets.
/// Provides the same theme access methods as VWidget.
abstract class VStatefulWidget extends StatefulWidget {
  const VStatefulWidget({super.key});
}

/// Base state class that provides theme access methods
abstract class VState<T extends VStatefulWidget> extends State<T> {
  /// Gets the theme data from the current context
  VThemeData getTheme() {
    return VThemeProvider.of(context);
  }

  /// Gets theme colors for convenience
  VColors getColors() {
    return getTheme().colors;
  }

  /// Gets theme density for convenience (includes spacing values)
  VComponentDensity getDensity() {
    return getTheme().componentDensity;
  }

  /// Gets theme corners for convenience
  VCorners getCorners() {
    return getTheme().corners;
  }

  /// Gets theme elevation for convenience
  VElevation getElevation() {
    return getTheme().elevation;
  }

  /// Gets theme motion for convenience
  VMotion getMotion() {
    return getTheme().motion;
  }
}
