import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import '../theme/v_theme_provider.dart';

/// A dropdown item for use with VDropdown
class VDropdownItem<T> {
  const VDropdownItem({
    required this.value,
    required this.child,
    this.enabled = true,
  });

  final T value;
  final Widget child;
  final bool enabled;
}

/// A themed dropdown component that follows Vertoie design patterns
class VDropdown<T> extends VWidget {
  const VDropdown({
    super.key,
    required this.items,
    required this.onChanged,
    this.value,
    this.hint,
    this.label,
    this.enabled = true,
    this.isExpanded = true,
    this.icon,
    this.style,
    this.dropdownColor,
    this.focusNode,
    this.autofocus = false,
  });

  final List<VDropdownItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final T? value;
  final String? hint;
  final String? label;
  final bool enabled;
  final bool isExpanded;
  final Widget? icon;
  final TextStyle? style;
  final Color? dropdownColor;
  final FocusNode? focusNode;
  final bool autofocus;

  @override
  Widget build(BuildContext context) {
    final theme = VThemeProvider.of(context);
    final colors = theme.colors;
    final corners = theme.corners;
    final density = theme.componentDensity;

    // Calculate height and padding to match VInput and VButton exactly
    final dropdownHeight = density.height * 1.05; // Same as VInput and medium VButton
    final horizontalPadding = density.horizontalPadding;

    // Use minimal vertical padding to match input field density
    final verticalPadding = (density.height * 1.05 - 20) / 2; // Calculate to center 14px text + small buffer

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          Text(
            label!,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: colors.onSurface,
            ),
          ),
          SizedBox(height: density.xs),
        ],
        SizedBox(
          height: dropdownHeight,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: enabled
                  ? colors.onSurface.withValues(alpha: 0.3)
                  : colors.onSurface.withValues(alpha: 0.1),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(corners.md),
              color: enabled
                ? colors.surface
                : colors.surfaceVariant,
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<T>(
                value: value,
                hint: Text(
                  hint ?? 'Select an option',
                  style: TextStyle(
                    color: colors.onSurface.withValues(alpha: 0.6),
                    fontSize: 14,
                  ),
                ),
                onChanged: enabled ? onChanged : null,
                items: items.map((VDropdownItem<T> item) {
                  return DropdownMenuItem<T>(
                    value: item.value,
                    enabled: item.enabled,
                    child: item.child,
                  );
                }).toList(),
                isExpanded: isExpanded,
                icon: icon ?? Icon(
                  Icons.keyboard_arrow_down,
                  color: enabled
                    ? colors.onSurface
                    : colors.onSurface.withValues(alpha: 0.6),
                ),
                style: style ?? TextStyle(
                  color: colors.onSurface,
                  fontSize: 14,
                ),
                dropdownColor: dropdownColor ?? colors.surface,
                focusNode: focusNode,
                autofocus: autofocus,
                padding: EdgeInsets.symmetric(
                  horizontal: horizontalPadding,
                  vertical: verticalPadding,
                ),
                borderRadius: BorderRadius.circular(corners.md),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
