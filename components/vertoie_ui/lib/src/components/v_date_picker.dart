import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import '../theme/v_theme_data.dart';
import '../theme/v_theme.dart';

/// A themeable date picker widget that follows Vertoie design system.
/// Provides single date selection with calendar popup.
class VDatePicker extends VStatefulWidget {
  const VDatePicker({
    super.key,
    this.value,
    this.onChanged,
    this.firstDate,
    this.lastDate,
    this.placeholder = 'Select date',
    this.label,
    this.enabled = true,
    this.showClearButton = true,
    this.dateFormat,
  });

  /// The currently selected date
  final DateTime? value;

  /// Called when the date selection changes
  final ValueChanged<DateTime?>? onChanged;

  /// The earliest allowable date
  final DateTime? firstDate;

  /// The latest allowable date  
  final DateTime? lastDate;

  /// Placeholder text when no date is selected
  final String placeholder;

  /// Optional label for the date picker
  final String? label;

  /// Whether the date picker is enabled
  final bool enabled;

  /// Whether to show a clear button when a date is selected
  final bool showClearButton;

  /// Custom date format function. If null, uses default format.
  final String Function(DateTime)? dateFormat;

  @override
  VState<VDatePicker> createState() => _VDatePickerState();
}

class _VDatePickerState extends VState<VDatePicker> {
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colors = getColors();
    final corners = getCorners();
    final density = getDensity();
    final motion = getMotion();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: TextStyle(
              color: colors.onSurface,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: density.smallGap), // Semantic small gap for label spacing
        ],
        _buildDateInput(colors, corners, density, motion),
      ],
    );
  }

  Widget _buildDateInput(VColors colors, VCorners corners, VComponentDensity density, VMotion motion) {
    final hasValue = widget.value != null;
    final displayText = hasValue 
        ? _formatDate(widget.value!)
        : widget.placeholder;

    return AnimatedContainer(
      duration: motion.duration,
      curve: motion.curve,
      height: density.height, // Use density height directly
      decoration: BoxDecoration(
        color: widget.enabled ? colors.surface : colors.surfaceVariant,
        border: Border.all(
          color: _focusNode.hasFocus
              ? colors.primary
              : colors.onSurface.withValues(alpha: 0.3),
          width: _focusNode.hasFocus ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(corners.md),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.enabled ? _showDatePicker : null,
          borderRadius: BorderRadius.circular(corners.md),
          focusNode: _focusNode,
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: density.horizontalPadding, // Semantic property
              vertical: density.verticalPadding,     // Semantic property
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    displayText,
                    style: TextStyle(
                      color: hasValue
                          ? colors.onSurface
                          : colors.onSurface.withValues(alpha: 0.6),
                      fontSize: 14, // Match VInput font size
                      height: 1, // Add line height for better text positioning
                    ),
                  ),
                ),
                if (hasValue && widget.showClearButton && widget.enabled) ...[
                  SizedBox(width: density.gap), // Semantic gap property
                  _buildClearButton(colors, corners),
                  SizedBox(width: density.smallGap), // Semantic small gap property
                ],
                Icon(
                  Icons.calendar_today,
                  color: colors.onSurface.withValues(alpha: 0.6),
                  size: density.iconSize, // Semantic icon size property
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildClearButton(VColors colors, VCorners corners) {
    final density = getDensity();
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => widget.onChanged?.call(null),
        borderRadius: BorderRadius.circular(corners.sm),
        child: Padding(
          padding: EdgeInsets.all(density.smallGap), // Semantic small gap for clear button padding
          child: Icon(
            Icons.clear,
            color: colors.onSurface.withValues(alpha: 0.6),
            size: density.smallIconSize, // Semantic small icon size property
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    if (widget.dateFormat != null) {
      return widget.dateFormat!(date);
    }
    
    // Default format: MMM dd, yyyy (e.g., "Jan 15, 2024")
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    return '${months[date.month - 1]} ${date.day.toString().padLeft(2, '0')}, ${date.year}';
  }

  Future<void> _showDatePicker() async {
    final colors = getColors();
    
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: widget.value ?? DateTime.now(),
      firstDate: widget.firstDate ?? DateTime(1900),
      lastDate: widget.lastDate ?? DateTime(2100),
    );

    if (selectedDate != null) {
      widget.onChanged?.call(selectedDate);
    }
  }
}
