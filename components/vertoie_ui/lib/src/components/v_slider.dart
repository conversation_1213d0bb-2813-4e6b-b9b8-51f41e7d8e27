import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import '../theme/v_theme_data.dart';

/// Slider types supported by VSlider
enum VSliderType {
  single,
  range,
}

/// A themeable slider widget that follows Vertoie design system.
/// Supports single value, range, and stepped variants with brand orange colors.
class VSlider extends VWidget {
  const VSlider({
    super.key,
    required this.type,
    this.value,
    this.rangeValues,
    this.onChanged,
    this.onRangeChanged,
    this.min = 0.0,
    this.max = 1.0,
    this.divisions,
    this.label,
    this.enabled = true,
  }) : assert(
          (type == VSliderType.single && value != null) ||
          (type == VSliderType.range && rangeValues != null),
          'Single sliders require value, range sliders require rangeValues',
        );

  /// Type of slider (single or range)
  final VSliderType type;

  /// Current value for single slider
  final double? value;

  /// Current range values for range slider
  final RangeValues? rangeValues;

  /// Callback for single slider value changes
  final ValueChanged<double>? onChanged;

  /// Callback for range slider value changes
  final ValueChanged<RangeValues>? onRangeChanged;

  /// Minimum value
  final double min;

  /// Maximum value
  final double max;

  /// Number of discrete divisions (null for continuous)
  final int? divisions;

  /// Optional label text
  final String? label;

  /// Whether the slider is enabled
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    final theme = getTheme(context);
    final colors = theme.colors;
    final density = theme.componentDensity;

    // Create slider theme
    final sliderTheme = SliderTheme.of(context).copyWith(
      activeTrackColor: colors.primary,
      inactiveTrackColor: colors.neutral.shade300,
      thumbColor: colors.primary,
      overlayColor: colors.primary.withOpacity(0.2),
      valueIndicatorColor: colors.primary,
      valueIndicatorTextStyle: TextStyle(
        color: colors.onPrimary,
        fontSize: 12,
      ),
      trackHeight: 4.0,
      thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 10.0),
      overlayShape: const RoundSliderOverlayShape(overlayRadius: 20.0),
    );

    Widget sliderWidget;

    if (type == VSliderType.single) {
      sliderWidget = SliderTheme(
        data: sliderTheme,
        child: Slider(
          value: value!,
          onChanged: enabled ? onChanged : null,
          min: min,
          max: max,
          divisions: divisions,
          label: divisions != null ? value!.round().toString() : null,
        ),
      );
    } else {
      sliderWidget = SliderTheme(
        data: sliderTheme,
        child: RangeSlider(
          values: rangeValues!,
          onChanged: enabled ? onRangeChanged : null,
          min: min,
          max: max,
          divisions: divisions,
          labels: divisions != null
              ? RangeLabels(
                  rangeValues!.start.round().toString(),
                  rangeValues!.end.round().toString(),
                )
              : null,
        ),
      );
    }

    // If no label, return just the slider
    if (label == null) {
      return Opacity(
        opacity: enabled ? 1.0 : 0.6,
        child: sliderWidget,
      );
    }

    // Return slider with label
    return Opacity(
      opacity: enabled ? 1.0 : 0.6,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label!,
            style: TextStyle(
              color: colors.onSurface,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: density.xs),
          sliderWidget,
        ],
      ),
    );
  }
}
