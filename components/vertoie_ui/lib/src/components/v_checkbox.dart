import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import '../theme/v_theme_data.dart';

/// Checkbox states supported by VCheckbox
enum VCheckboxState {
  unchecked,
  checked,
  indeterminate,
}

/// A themeable checkbox widget that follows Vertoie design system.
/// Supports indeterminate state and uses brand orange colors.
class VCheckbox extends VWidget {
  const VCheckbox({
    super.key,
    required this.value,
    required this.onChanged,
    this.label,
    this.enabled = true,
    this.tristate = false,
  });

  /// The current state of the checkbox
  final bool? value;

  /// Called when the checkbox state changes
  final ValueChanged<bool?>? onChanged;

  /// Optional label text to display next to the checkbox
  final String? label;

  /// Whether the checkbox is enabled
  final bool enabled;

  /// Whether the checkbox supports three states (unchecked, checked, indeterminate)
  final bool tristate;

  /// Gets the current checkbox state based on value and tristate setting
  VCheckboxState get state {
    if (value == null && tristate) {
      return VCheckboxState.indeterminate;
    }
    return value == true ? VCheckboxState.checked : VCheckboxState.unchecked;
  }

  @override
  Widget build(BuildContext context) {
    final colors = getColors(context);
    final density = getDensity(context);
    final corners = getCorners(context);
    final motion = getMotion(context);

    // Calculate colors based on state and theme
    final isActive = state != VCheckboxState.unchecked;
    final checkboxColor = _getCheckboxColor(colors, isActive);
    final borderColor = _getBorderColor(colors, isActive);
    final iconColor = _getIconColor(colors, isActive);

    final Widget checkbox = GestureDetector(
      onTap: enabled ? _handleTap : null,
      child: AnimatedContainer(
        duration: motion.duration,
        curve: motion.curve,
        width: density.height * 0.5, // Match button density proportionally
        height: density.height * 0.5,
        decoration: BoxDecoration(
          color: checkboxColor,
          border: Border.all(
            color: borderColor,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(corners.sm),
        ),
        child: _buildCheckboxIcon(iconColor),
      ),
    );

    // If no label, return just the checkbox
    if (label == null) {
      return Opacity(
        opacity: enabled ? 1.0 : 0.6,
        child: checkbox,
      );
    }

    // Return checkbox with label
    return Opacity(
      opacity: enabled ? 1.0 : 0.6,
      child: GestureDetector(
        onTap: enabled ? _handleTap : null,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            checkbox,
            SizedBox(width: density.horizontalPadding),
            Flexible(
              child: Text(
                label!,
                style: TextStyle(
                  color: colors.onSurface,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the appropriate icon for the current checkbox state
  Widget _buildCheckboxIcon(Color iconColor) {
    switch (state) {
      case VCheckboxState.checked:
        return Icon(
          Icons.check,
          color: iconColor,
          size: 16,
        );
      case VCheckboxState.indeterminate:
        return Icon(
          Icons.remove,
          color: iconColor,
          size: 16,
        );
      case VCheckboxState.unchecked:
        return const SizedBox.shrink();
    }
  }

  /// Gets the background color for the checkbox
  Color _getCheckboxColor(VColors colors, bool isActive) {
    if (!enabled) {
      return isActive ? colors.neutral.shade300 : Colors.transparent;
    }
    return isActive ? colors.primary : Colors.transparent;
  }

  /// Gets the border color for the checkbox
  Color _getBorderColor(VColors colors, bool isActive) {
    if (!enabled) {
      return colors.neutral.shade300;
    }
    return isActive ? colors.primary : colors.neutral.shade400;
  }

  /// Gets the icon color for the checkbox
  Color _getIconColor(VColors colors, bool isActive) {
    if (!enabled) {
      return colors.neutral.shade500;
    }
    return isActive ? colors.onPrimary : colors.onSurface;
  }

  /// Handles tap events and cycles through states appropriately
  void _handleTap() {
    if (!enabled || onChanged == null) return;

    if (tristate) {
      // Cycle through: unchecked -> checked -> indeterminate -> unchecked
      switch (state) {
        case VCheckboxState.unchecked:
          onChanged!(true);
          break;
        case VCheckboxState.checked:
          onChanged!(null);
          break;
        case VCheckboxState.indeterminate:
          onChanged!(false);
          break;
      }
    } else {
      // Simple toggle between checked and unchecked
      onChanged!(value != true);
    }
  }
}
