import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import '../theme/v_theme_data.dart';
import '../theme/v_theme.dart';

/// Button variants supported by VButton
enum VButtonVariant {
  primary,
  secondary,
  text,
  outlined,
}

/// Size variants for VButton
enum VButtonSize {
  small,
  medium,
  large,
}

/// A themeable button component that follows Vertoie design system.
/// Supports multiple variants, sizes, and states.
class VButton extends VWidget {
  const VButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.variant = VButtonVariant.primary,
    this.size = VButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.fullWidth = false,
    this.icon,
    this.tooltip,
  });

  /// Called when the button is pressed
  final VoidCallback? onPressed;

  /// The widget to display inside the button (typically text)
  final Widget child;

  /// The visual variant of the button
  final VButtonVariant variant;

  /// The size of the button
  final VButtonSize size;

  /// Whether the button is in a loading state
  final bool isLoading;

  /// Whether the button is disabled
  final bool isDisabled;

  /// Whether the button should take the full width of its parent
  final bool fullWidth;

  /// Optional icon to display before the text
  final Widget? icon;

  /// Optional tooltip text
  final String? tooltip;

  @override
  Widget build(BuildContext context) {
    final theme = getTheme(context);
    final colors = theme.colors;
    final corners = theme.corners;
    final density = theme.componentDensity;
    final motion = theme.motion;

    // Determine button colors based on variant
    final buttonColors = _getButtonColors(colors);
    
    // Determine button size
    final buttonSize = _getButtonSize(size, density);

    // Build button content
    Widget buttonChild = _buildButtonContent();

    // Wrap with loading indicator if needed
    if (isLoading) {
      buttonChild = _buildLoadingContent(colors);
    }

    // Create the base button
    Widget button = AnimatedContainer(
      duration: motion.duration,
      curve: motion.curve,
      width: fullWidth ? double.infinity : null,
      height: buttonSize.height,
      child: ElevatedButton(
        onPressed: _getOnPressed(),
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColors.background,
          foregroundColor: buttonColors.foreground,
          disabledBackgroundColor: buttonColors.disabledBackground,
          disabledForegroundColor: buttonColors.disabledForeground,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(corners.md),
            side: variant == VButtonVariant.outlined
                ? BorderSide(
                    color: (isDisabled || isLoading)
                        ? (buttonColors.disabledBorder ?? colors.neutral.shade300)
                        : (buttonColors.border ?? colors.primary),
                  )
                : BorderSide.none,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: buttonSize.paddingHorizontal,
            vertical: buttonSize.paddingVertical,
          ),
        ).copyWith(
          elevation: WidgetStateProperty.resolveWith<double>((states) {
            if (variant == VButtonVariant.text || variant == VButtonVariant.outlined) {
              return 0;
            }
            if (states.contains(WidgetState.hovered)) {
              return 4;
            }
            return 2;
          }),
        ),
        child: buttonChild,
      ),
    );

    // Wrap with tooltip if provided
    if (tooltip != null) {
      button = Tooltip(
        message: tooltip!,
        child: button,
      );
    }

    return button;
  }

  /// Builds the button content (icon + text)
  Widget _buildButtonContent() {
    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon!,
          const SizedBox(width: 8),
          child,
        ],
      );
    }
    return child;
  }

  /// Builds the loading state content
  Widget _buildLoadingContent(VColors colors) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              variant == VButtonVariant.primary 
                  ? colors.onPrimary 
                  : colors.primary,
            ),
          ),
        ),
        const SizedBox(width: 8),
        child,
      ],
    );
  }

  /// Determines the onPressed callback based on state
  VoidCallback? _getOnPressed() {
    if (isDisabled || isLoading) return null;
    return onPressed;
  }

  /// Gets button colors based on variant
  _ButtonColors _getButtonColors(VColors colors) {
    switch (variant) {
      case VButtonVariant.primary:
        return _ButtonColors(
          background: colors.primary,
          foreground: colors.onPrimary,
          disabledBackground: colors.primary.withValues(alpha: 0.9),
          disabledForeground: colors.onPrimary.withValues(alpha: 1),
        );
      case VButtonVariant.secondary:
        return _ButtonColors(
          background: colors.secondary,
          foreground: colors.onSecondary,
          disabledBackground: colors.secondary.withValues(alpha: 0.9),
          disabledForeground: colors.onSecondary.withValues(alpha: 1),
        );
      case VButtonVariant.outlined:
        return _ButtonColors(
          background: colors.background,
          foreground: colors.primary,
          disabledBackground: colors.background,
          disabledForeground: colors.primary.withValues(alpha: 0.9),
          border: colors.primary,
          disabledBorder: colors.primary.withValues(alpha: 1),
        );
      case VButtonVariant.text:
        return _ButtonColors(
          background: colors.background,
          foreground: colors.primary,
          disabledBackground: colors.background,
          disabledForeground: colors.primary.withValues(alpha: 1),
        );
    }
  }

  /// Gets button size configuration
  _ButtonSize _getButtonSize(VButtonSize size, VComponentDensity density) {
    final baseHeight = density.height;
    final basePaddingH = density.horizontalPadding;
    final basePaddingV = density.verticalPadding;

    switch (size) {
      case VButtonSize.small:
        return _ButtonSize(
          height: baseHeight * 0.85, // Increased from 0.8 to give more room for text
          paddingHorizontal: basePaddingH * 0.7,
          paddingVertical: basePaddingV * 0.6,
        );
      case VButtonSize.medium:
        return _ButtonSize(
          height: baseHeight * 1.05, // Slightly increased for better text spacing
          paddingHorizontal: basePaddingH,
          paddingVertical: basePaddingV,
        );
      case VButtonSize.large:
        return _ButtonSize(
          height: baseHeight * 1.25, // Increased from 1.2 for better text spacing
          paddingHorizontal: basePaddingH * 1.3,
          paddingVertical: basePaddingV * 1.2,
        );
    }
  }
}

/// Internal class to hold button color configuration
class _ButtonColors {
  const _ButtonColors({
    required this.background,
    required this.foreground,
    required this.disabledBackground,
    required this.disabledForeground,
    this.border,
    this.disabledBorder,
  });

  final Color background;
  final Color foreground;
  final Color disabledBackground;
  final Color disabledForeground;
  final Color? border;
  final Color? disabledBorder;
}

/// Internal class to hold button size configuration
class _ButtonSize {
  const _ButtonSize({
    required this.height,
    required this.paddingHorizontal,
    required this.paddingVertical,
  });

  final double height;
  final double paddingHorizontal;
  final double paddingVertical;
}
