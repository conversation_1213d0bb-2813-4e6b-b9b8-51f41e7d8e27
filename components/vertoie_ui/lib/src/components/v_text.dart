import 'package:flutter/material.dart';
import '../base/v_widget.dart';

/// A themeable text widget that follows Vertoie design system.
/// This is a placeholder implementation.
class VText extends VWidget {
  const VText(
    this.data, {
    super.key,
    this.style,
    this.textAlign,
    this.overflow,
    this.maxLines,
  });

  final String data;
  final TextStyle? style;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final int? maxLines;

  @override
  Widget build(BuildContext context) {
    final colors = getColors(context);
    
    return Text(
      data,
      style: TextStyle(
        color: colors.onSurface,
      ).merge(style),
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
    );
  }
}
