import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import '../theme/v_theme_data.dart';
import '../theme/v_theme.dart';

/// Represents availability status for a specific day
class _DayAvailability {
  final bool hasBusinessHours;
  final bool isAvailable;
  final List<VTimeBlock> timeBlocks;
  final VBusinessHours? businessHours;

  _DayAvailability({
    required this.hasBusinessHours,
    required this.isAvailable,
    required this.timeBlocks,
    this.businessHours,
  });
}

/// Calendar view modes
enum VCalendarView {
  /// Month view showing full month grid
  month,
  /// Week view showing 7 days
  week,
  /// Day view showing single day
  day,
}

/// First day of week constants for VCalendar
class VCalendarWeekStart {
  /// Start week on Monday (ISO 8601 standard)
  static const int monday = DateTime.monday;
  
  /// Start week on Sunday (common in US)
  static const int sunday = DateTime.sunday;
  
  /// Start week on Saturday
  static const int saturday = DateTime.saturday;
}

/// Event status types for color coding
enum VEventStatus {
  /// Event is confirmed/scheduled
  confirmed,
  /// Event is tentative/pending
  tentative,
  /// Event is cancelled
  cancelled,
  /// Event is completed
  completed,
  /// Event is in progress
  inProgress,
  /// Event is overdue
  overdue,
}

/// Event category types for color coding
enum VEventCategory {
  /// General appointment
  appointment,
  /// Meeting event
  meeting,
  /// Personal event
  personal,
  /// Work/business event
  work,
  /// Holiday event
  holiday,
  /// Travel event
  travel,
  /// Custom category (use with customCategoryName)
  custom,
}

/// Color coding configuration for calendar events
class VEventColorConfig {
  const VEventColorConfig({
    this.useStatusColors = true,
    this.useCategoryColors = true,
    this.useClientColors = false,
    this.statusColors = const {},
    this.categoryColors = const {},
    this.clientColors = const {},
    this.defaultColor,
  });

  /// Whether to use status-based coloring
  final bool useStatusColors;
  
  /// Whether to use category-based coloring
  final bool useCategoryColors;
  
  /// Whether to use client-based coloring
  final bool useClientColors;
  
  /// Custom colors for each status type
  final Map<VEventStatus, Color> statusColors;
  
  /// Custom colors for each category type
  final Map<VEventCategory, Color> categoryColors;
  
  /// Custom colors for client IDs
  final Map<String, Color> clientColors;
  
  /// Default color when no specific color is defined
  final Color? defaultColor;

  /// Default status colors following Vertoie design system
  static Map<VEventStatus, Color> get defaultStatusColors => {
    VEventStatus.confirmed: const Color(0xFF059669), // Green
    VEventStatus.tentative: const Color(0xFFD97706), // Orange
    VEventStatus.cancelled: const Color(0xFFDC2626), // Red
    VEventStatus.completed: const Color(0xFF6366F1), // Indigo
    VEventStatus.inProgress: const Color(0xFF0891B2), // Cyan
    VEventStatus.overdue: const Color(0xFFBE123C), // Rose
  };

  /// Default category colors following Vertoie design system
  static Map<VEventCategory, Color> get defaultCategoryColors => {
    VEventCategory.appointment: const Color(0xFF3B82F6), // Blue
    VEventCategory.meeting: const Color(0xFF8B5CF6), // Purple
    VEventCategory.personal: const Color(0xFF10B981), // Emerald
    VEventCategory.work: const Color(0xFFF59E0B), // Amber
    VEventCategory.holiday: const Color(0xFFEF4444), // Red
    VEventCategory.travel: const Color(0xFF06B6D4), // Cyan
    VEventCategory.custom: const Color(0xFF6B7280), // Gray
  };
}

/// Event reminder configuration
class VEventReminder {
  const VEventReminder({
    required this.type,
    required this.duration,
  });

  final VReminderType type;
  final Duration duration;
}

/// Types of event reminders
enum VReminderType {
  notification,
  email,
  popup,
}

/// Recurrence pattern for repeating events
class VRecurrencePattern {
  const VRecurrencePattern({
    required this.type,
    this.interval = 1,
    this.endDate,
    this.occurrences,
    this.daysOfWeek = const [],
    this.monthlyType = VRecurrenceMonthlyType.dayOfMonth,
    this.weekOfMonth,
    this.dayOfMonth,
  });

  final VRecurrenceType type;
  final int interval;
  final DateTime? endDate;
  final int? occurrences;
  final List<int> daysOfWeek; // 1=Monday, 7=Sunday
  final VRecurrenceMonthlyType monthlyType;
  final int? weekOfMonth; // 1=first, 2=second, etc.
  final int? dayOfMonth; // 1-31

  /// Generate next occurrence date
  DateTime getNextOccurrence(DateTime currentDate) {
    switch (type) {
      case VRecurrenceType.daily:
        return currentDate.add(Duration(days: interval));
      case VRecurrenceType.weekly:
        return currentDate.add(Duration(days: 7 * interval));
      case VRecurrenceType.monthly:
        return DateTime(
          currentDate.year,
          currentDate.month + interval,
          currentDate.day,
        );
      case VRecurrenceType.yearly:
        return DateTime(
          currentDate.year + interval,
          currentDate.month,
          currentDate.day,
        );
    }
  }

  /// Check if recurrence should end
  bool shouldEnd(DateTime date, int occurrenceCount) {
    if (endDate != null && date.isAfter(endDate!)) return true;
    if (occurrences != null && occurrenceCount >= occurrences!) return true;
    return false;
  }
}

/// Types of recurrence patterns
enum VRecurrenceType {
  daily,
  weekly,
  monthly,
  yearly,
}

/// Monthly recurrence types
enum VRecurrenceMonthlyType {
  dayOfMonth, // Same day of month (15th)
  weekOfMonth, // Same week and day (2nd Tuesday)
}

/// Enhanced event data for calendar display with advanced color coding
class VCalendarEvent {
  const VCalendarEvent({
    required this.id,
    required this.title,
    required this.date,
    this.description,
    this.color,
    this.isAllDay = false,
    this.startTime,
    this.endTime,
    // Advanced color coding properties
    this.status = VEventStatus.confirmed,
    this.category = VEventCategory.appointment,
    this.clientId,
    this.customCategoryName,
    this.priority = 1,
    this.tags = const [],
    // Recurring events support
    this.recurrencePattern,
    this.isRecurring = false,
    this.recurrenceId,
    this.originalEventId,
    // Custom fields support
    this.customFields,
    // Enhanced event properties
    this.location,
    this.attendees = const [],
    this.url,
    this.reminder,
  });

  /// Unique identifier for the event
  final String id;
  
  /// Event title
  final String title;
  
  /// Event date
  final DateTime date;
  
  /// Optional event description
  final String? description;
  
  /// Optional custom color for the event indicator (overrides color coding)
  final Color? color;
  
  /// Whether this is an all-day event
  final bool isAllDay;
  
  /// Start time for timed events
  final TimeOfDay? startTime;
  
  /// End time for timed events
  final TimeOfDay? endTime;

  // Advanced color coding properties
  
  /// Event status for status-based color coding
  final VEventStatus status;
  
  /// Event category for category-based color coding
  final VEventCategory category;
  
  /// Client ID for client-based coloring
  final String? clientId;
  
  /// Custom category name when category is VEventCategory.custom
  final String? customCategoryName;
  
  /// Event priority (1-5, where 1 is highest priority)
  final int priority;
  
  /// Tags for additional categorization
  final List<String> tags;

  // Recurring events support
  
  /// Recurrence pattern for repeating events
  final VRecurrencePattern? recurrencePattern;
  
  /// Whether this event is part of a recurring series
  final bool isRecurring;
  
  /// ID linking this event to its recurrence series
  final String? recurrenceId;
  
  /// Original event ID if this is a modified occurrence
  final String? originalEventId;

  // Enhanced event properties
  
  /// Custom fields for extensibility
  final Map<String, dynamic>? customFields;
  
  /// Event location
  final String? location;
  
  /// List of attendees
  final List<String> attendees;
  
  /// Associated URL
  final String? url;
  
  /// Reminder settings
  final VEventReminder? reminder;

  /// Get the display color based on color coding configuration
  Color getDisplayColor(VEventColorConfig colorConfig, VThemeData theme) {
    // If explicit color is set, use it
    if (color != null) return color!;

    // Client-based coloring (highest priority)
    if (colorConfig.useClientColors && clientId != null) {
      final clientColor = colorConfig.clientColors[clientId];
      if (clientColor != null) return clientColor;
    }

    // Status-based coloring
    if (colorConfig.useStatusColors) {
      final statusColor = colorConfig.statusColors[status] ?? 
                         VEventColorConfig.defaultStatusColors[status];
      if (statusColor != null) return statusColor;
    }

    // Category-based coloring
    if (colorConfig.useCategoryColors) {
      final categoryColor = colorConfig.categoryColors[category] ?? 
                           VEventColorConfig.defaultCategoryColors[category];
      if (categoryColor != null) return categoryColor;
    }

    // Default color
    return colorConfig.defaultColor ?? theme.colors.primary;
  }

  /// Get a lighter tint of the display color for backgrounds
  Color getBackgroundColor(VEventColorConfig colorConfig, VThemeData theme) {
    final displayColor = getDisplayColor(colorConfig, theme);
    return displayColor.withValues(alpha: 0.1);
  }

  /// Get appropriate text color for readability
  Color getTextColor(VEventColorConfig colorConfig, VThemeData theme) {
    final displayColor = getDisplayColor(colorConfig, theme);
    // Use a simple brightness check to determine text color
    final brightness = displayColor.computeLuminance();
    return brightness > 0.5 ? Colors.black87 : Colors.white;
  }
}

/// Types of time blocks for availability management
enum VTimeBlockType {
  /// Available time slot
  available,
  /// Blocked/unavailable time slot
  blocked,
  /// Business hours (default available time)
  businessHours,
  /// Holiday time
  holiday,
  /// Break/lunch time
  break_,
  /// Buffer time between appointments
  buffer,
}

/// Represents a time block for availability management
class VTimeBlock {
  const VTimeBlock({
    required this.id,
    required this.start,
    required this.end,
    required this.type,
    this.title,
    this.description,
    this.isRecurring = false,
    this.recurrencePattern,
    this.color,
  });

  /// Unique identifier for the time block
  final String id;
  
  /// Start date and time
  final DateTime start;
  
  /// End date and time
  final DateTime end;
  
  /// Type of time block
  final VTimeBlockType type;
  
  /// Optional title for the time block
  final String? title;
  
  /// Optional description
  final String? description;
  
  /// Whether this is a recurring time block
  final bool isRecurring;
  
  /// Recurrence pattern (daily, weekly, etc.)
  final String? recurrencePattern;
  
  /// Optional custom color
  final Color? color;

  /// Check if this time block covers a specific date/time
  bool coversDateTime(DateTime dateTime) {
    return dateTime.isAfter(start.subtract(const Duration(seconds: 1))) &&
           dateTime.isBefore(end.add(const Duration(seconds: 1)));
  }

  /// Check if this time block overlaps with another time period
  bool overlaps(DateTime startTime, DateTime endTime) {
    return start.isBefore(endTime) && end.isAfter(startTime);
  }
}

/// Business hours configuration for a specific day of the week
class VBusinessHours {
  const VBusinessHours({
    required this.dayOfWeek,
    required this.isOpen,
    this.openTime = const TimeOfDay(hour: 9, minute: 0),
    this.closeTime = const TimeOfDay(hour: 17, minute: 0),
    this.breakStart,
    this.breakEnd,
  });

  /// Day of the week (1 = Monday, 7 = Sunday)
  final int dayOfWeek;
  
  /// Whether the business is open on this day
  final bool isOpen;
  
  /// Opening time
  final TimeOfDay openTime;
  
  /// Closing time
  final TimeOfDay closeTime;
  
  /// Optional break start time
  final TimeOfDay? breakStart;
  
  /// Optional break end time
  final TimeOfDay? breakEnd;

  /// Check if a specific time is within business hours
  bool isWithinBusinessHours(TimeOfDay time) {
    if (!isOpen) return false;
    
    final timeMinutes = time.hour * 60 + time.minute;
    final openMinutes = openTime.hour * 60 + openTime.minute;
    final closeMinutes = closeTime.hour * 60 + closeTime.minute;
    
    bool withinMainHours = timeMinutes >= openMinutes && timeMinutes <= closeMinutes;
    
    // Check if time falls within break period
    if (withinMainHours && breakStart != null && breakEnd != null) {
      final breakStartMinutes = breakStart!.hour * 60 + breakStart!.minute;
      final breakEndMinutes = breakEnd!.hour * 60 + breakEnd!.minute;
      
      if (timeMinutes >= breakStartMinutes && timeMinutes <= breakEndMinutes) {
        return false; // Time is during break
      }
    }
    
    return withinMainHours;
  }
}

/// Availability management configuration for calendar
class VAvailabilityConfig {
  const VAvailabilityConfig({
    this.businessHours = const {},
    this.timeBlocks = const [],
    this.bufferMinutes = 0,
    this.minimumBookingNotice = 0,
    this.maximumBookingAdvance = 365,
    this.showAvailabilityOverlay = true,
    this.unavailableColor = const Color(0xFFEF4444),
    this.availableColor = const Color(0xFF10B981),
    this.businessHoursColor = const Color(0xFF3B82F6),
    this.bufferColor = const Color(0xFFF59E0B),
  });

  /// Business hours for each day of the week
  final Map<int, VBusinessHours> businessHours;
  
  /// List of time blocks (holidays, blocked times, etc.)
  final List<VTimeBlock> timeBlocks;
  
  /// Buffer time in minutes between appointments
  final int bufferMinutes;
  
  /// Minimum notice required for booking (in hours)
  final int minimumBookingNotice;
  
  /// Maximum advance booking allowed (in days)
  final int maximumBookingAdvance;
  
  /// Whether to show availability overlay on calendar
  final bool showAvailabilityOverlay;
  
  /// Color for unavailable time slots
  final Color unavailableColor;
  
  /// Color for available time slots
  final Color availableColor;
  
  /// Color for business hours indication
  final Color businessHoursColor;
  
  /// Color for buffer time indication
  final Color bufferColor;

  /// Check if a specific date/time is available for booking
  bool isAvailable(DateTime dateTime, {Duration? duration}) {
    // Check if it's too early to book
    final now = DateTime.now();
    if (dateTime.isBefore(now.add(Duration(hours: minimumBookingNotice)))) {
      return false;
    }
    
    // Check if it's too far in advance
    if (dateTime.isAfter(now.add(Duration(days: maximumBookingAdvance)))) {
      return false;
    }
    
    // Check business hours
    final dayOfWeek = dateTime.weekday;
    final businessHour = businessHours[dayOfWeek];
    if (businessHour != null && !businessHour.isWithinBusinessHours(TimeOfDay.fromDateTime(dateTime))) {
      return false;
    }
    
    // Check for blocked time blocks
    final endTime = duration != null ? dateTime.add(duration) : dateTime;
    for (final block in timeBlocks) {
      if (block.type == VTimeBlockType.blocked || 
          block.type == VTimeBlockType.holiday ||
          block.type == VTimeBlockType.break_) {
        if (block.overlaps(dateTime, endTime)) {
          return false;
        }
      }
    }
    
    return true;
  }

  /// Get the next available time slot after a given date/time
  DateTime? getNextAvailableSlot(DateTime after, Duration slotDuration) {
    DateTime current = after;
    final maxCheck = after.add(Duration(days: maximumBookingAdvance));
    
    while (current.isBefore(maxCheck)) {
      if (isAvailable(current, duration: slotDuration)) {
        return current;
      }
      current = current.add(const Duration(minutes: 15)); // Check every 15 minutes
    }
    
    return null; // No available slot found
  }

  /// Get all available slots for a specific date
  List<DateTime> getAvailableSlotsForDate(DateTime date, Duration slotDuration) {
    final slots = <DateTime>[];
    final dayOfWeek = date.weekday;
    final businessHour = businessHours[dayOfWeek];
    
    if (businessHour == null || !businessHour.isOpen) {
      return slots;
    }
    
    // Start from business hours opening time
    DateTime current = DateTime(
      date.year,
      date.month,
      date.day,
      businessHour.openTime.hour,
      businessHour.openTime.minute,
    );
    
    final endOfDay = DateTime(
      date.year,
      date.month,
      date.day,
      businessHour.closeTime.hour,
      businessHour.closeTime.minute,
    );
    
    while (current.add(slotDuration).isBefore(endOfDay) || current.add(slotDuration).isAtSameMomentAs(endOfDay)) {
      if (isAvailable(current, duration: slotDuration)) {
        slots.add(current);
      }
      current = current.add(const Duration(minutes: 15)); // 15-minute intervals
    }
    
    return slots;
  }
}

/// Enhanced time slot configuration for week and day views
class VTimeSlotConfig {
  const VTimeSlotConfig({
    this.slotHeight = 60.0,
    this.slotDuration = const Duration(hours: 1),
    this.minorSlotDivisions = 4, // 15-minute intervals
    this.startHour = 0,
    this.endHour = 24,
    this.showTimeLabels = true,
    this.showMinorGridLines = true,
    this.showNowIndicator = true,
    this.snapToSlots = true,
  });

  /// Height of each hour slot in pixels
  final double slotHeight;
  
  /// Duration represented by each major slot
  final Duration slotDuration;
  
  /// Number of minor divisions per major slot (e.g., 4 = 15-min intervals)
  final int minorSlotDivisions;
  
  /// Start hour for the time grid (0-23)
  final int startHour;
  
  /// End hour for the time grid (1-24)
  final int endHour;
  
  /// Whether to show time labels on the left
  final bool showTimeLabels;
  
  /// Whether to show minor grid lines for divisions
  final bool showMinorGridLines;
  
  /// Whether to show current time indicator
  final bool showNowIndicator;
  
  /// Whether to snap events to time slots
  final bool snapToSlots;

  /// Get the height for a specific duration
  double getHeightForDuration(Duration duration) {
    final slots = duration.inMinutes / slotDuration.inMinutes;
    return slots * slotHeight;
  }

  /// Get the Y position for a specific time
  double getYPositionForTime(TimeOfDay time) {
    final totalMinutes = time.hour * 60 + time.minute;
    final startMinutes = startHour * 60;
    final minutesFromStart = totalMinutes - startMinutes;
    return (minutesFromStart / slotDuration.inMinutes) * slotHeight;
  }

  /// Snap a time to the nearest slot boundary
  TimeOfDay snapTimeToSlot(TimeOfDay time) {
    if (!snapToSlots) return time;
    
    final totalMinutes = time.hour * 60 + time.minute;
    final slotMinutes = slotDuration.inMinutes ~/ minorSlotDivisions;
    final snappedMinutes = (totalMinutes / slotMinutes).round() * slotMinutes;
    
    return TimeOfDay(
      hour: snappedMinutes ~/ 60,
      minute: snappedMinutes % 60,
    );
  }
}

/// A themeable calendar widget that follows Vertoie design system.
/// Supports Month, Week, and Day views with event indicators and navigation.
/// Enhanced with configurable advanced features for better usability.
class VCalendar extends VStatefulWidget {
  const VCalendar({
    super.key,
    this.initialDate,
    this.initialView = VCalendarView.month,
    this.events = const [],
    this.onDateSelected,
    this.onEventTapped,
    this.onViewChanged,
    this.onMonthChanged,
    this.showNavigationControls = true,
    this.showViewSwitcher = true,
    this.firstDayOfWeek = VCalendarWeekStart.monday,
    this.enableSwipeNavigation = true,
    this.headerHeight = 60.0,
    this.dayHeight = 48.0,
    // Enhanced Month View Features
    this.showMiniNavigation = false,
    this.showTodayHighlight = true,
    this.showCurrentPeriodHighlight = true,
    this.enableKeyboardNavigation = false,
    this.multiWeekRows = 6,
    // Color Coding Configuration
    this.colorConfig = const VEventColorConfig(),
    // Availability Management Configuration
    this.availabilityConfig,
    // Time Slot Configuration (for week/day views)
    this.timeSlotConfig = const VTimeSlotConfig(),
  });

  /// Initial date to display (defaults to today)
  final DateTime? initialDate;
  
  /// Initial view mode
  final VCalendarView initialView;
  
  /// List of events to display
  final List<VCalendarEvent> events;
  
  /// Called when a date is selected
  final ValueChanged<DateTime>? onDateSelected;
  
  /// Called when an event is tapped
  final ValueChanged<VCalendarEvent>? onEventTapped;
  
  /// Called when the view changes
  final ValueChanged<VCalendarView>? onViewChanged;
  
  /// Called when the month changes (enhanced feature)
  final ValueChanged<DateTime>? onMonthChanged;
  
  /// Whether to show navigation controls (prev/next/today)
  final bool showNavigationControls;
  
  /// Whether to show view switcher buttons
  final bool showViewSwitcher;
  
  /// First day of the week for calendar display
  /// Use [VCalendarWeekStart.monday] (default) for Monday start
  /// Use [VCalendarWeekStart.sunday] for Sunday start
  /// Or use DateTime constants: DateTime.monday, DateTime.sunday, etc.
  final int firstDayOfWeek;
  
  /// Whether to enable swipe navigation
  final bool enableSwipeNavigation;
  
  /// Height of the header section
  final double headerHeight;
  
  /// Height of each day cell in month view
  final double dayHeight;

  // Enhanced Month View Features
  
  /// Show mini calendar navigation sidebar (month view only)
  final bool showMiniNavigation;
  
  /// Enhanced today highlighting with better visual emphasis
  final bool showTodayHighlight;
  
  /// Show current period highlighting (week/month boundaries)
  final bool showCurrentPeriodHighlight;
  
  /// Enable keyboard navigation (arrow keys, Enter, Home/End)
  final bool enableKeyboardNavigation;
  
  /// Number of week rows to display in month view (4-6 weeks)
  final int multiWeekRows;

  /// Color coding configuration for events
  final VEventColorConfig colorConfig;
  
  /// Availability management configuration (optional)
  final VAvailabilityConfig? availabilityConfig;

  /// Time slot configuration for week and day views
  final VTimeSlotConfig timeSlotConfig;

  @override
  VState<VCalendar> createState() => _VCalendarState();
}

class _VCalendarState extends VState<VCalendar> {
  late DateTime _currentDate;
  late VCalendarView _currentView;
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _currentDate = widget.initialDate ?? DateTime.now();
    _currentView = widget.initialView;
  }

  @override
  Widget build(BuildContext context) {
    final colors = getColors();
    final corners = getCorners();
    final density = getDensity();
    final elevation = getElevation();

    return Container(
      decoration: BoxDecoration(
        color: colors.surface,
        borderRadius: BorderRadius.circular(corners.md),
        boxShadow: elevation.boxShadow,
      ),
      child: Column(
        children: [
          if (widget.showNavigationControls || widget.showViewSwitcher)
            _buildHeader(colors, corners, density),
          Expanded(
            child: _buildCalendarView(colors, corners, density),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(VColors colors, VCorners corners, VComponentDensity density) {
    return Container(
      height: widget.headerHeight,
      padding: EdgeInsets.symmetric(horizontal: density.horizontalPadding),
      decoration: BoxDecoration(
        color: colors.surfaceVariant,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(corners.md),
          topRight: Radius.circular(corners.md),
        ),
      ),
      child: Row(
        children: [
          if (widget.showNavigationControls) ...[
            _buildNavigationButton(
              icon: Icons.chevron_left,
              onPressed: _goToPrevious,
              colors: colors,
              corners: corners,
              density: density,
            ),
            SizedBox(width: density.horizontalPadding),
            _buildTodayButton(colors, corners, density),
            SizedBox(width: density.horizontalPadding),
          ],
          Expanded(
            child: Center(
              child: Text(
                _getHeaderTitle(),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: colors.onSurface,
                ),
              ),
            ),
          ),
          if (widget.showNavigationControls) ...[
            SizedBox(width: density.horizontalPadding),
            _buildNavigationButton(
              icon: Icons.chevron_right,
              onPressed: _goToNext,
              colors: colors,
              corners: corners,
              density: density,
            ),
          ],
          if (widget.showViewSwitcher) ...[
            SizedBox(width: density.horizontalPadding),
            _buildViewSwitcher(colors, corners, density),
          ],
        ],
      ),
    );
  }

  Widget _buildNavigationButton({
    required IconData icon,
    required VoidCallback onPressed,
    required VColors colors,
    required VCorners corners,
    required VComponentDensity density,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(corners.sm),
        child: Padding(
          padding: EdgeInsets.all(density.horizontalPadding),
          child: Icon(
            icon,
            color: colors.onSurface,
            size: 20,
          ),
        ),
      ),
    );
  }

  Widget _buildTodayButton(VColors colors, VCorners corners, VComponentDensity density) {
    final today = DateTime.now();
    final isToday = _isSameDay(_currentDate, today) ||
                   (_currentView == VCalendarView.week && _isDateInCurrentWeek(today)) ||
                   (_currentView == VCalendarView.month && _isDateInCurrentMonth(today));

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _goToToday,
        borderRadius: BorderRadius.circular(corners.sm),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: density.horizontalPadding,
            vertical: density.xs,
          ),
          decoration: BoxDecoration(
            color: isToday ? colors.orange.primary.withValues(alpha: 0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(corners.sm),
            border: isToday ? Border.all(color: colors.orange.primary.withValues(alpha: 0.3)) : null,
          ),
          child: Text(
            'Today',
            style: TextStyle(
              fontSize: 12,
              fontWeight: isToday ? FontWeight.w600 : FontWeight.w400,
              color: isToday ? colors.orange.primary : colors.onSurface,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildViewSwitcher(VColors colors, VCorners corners, VComponentDensity density) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: VCalendarView.values.map((view) {
        final isSelected = view == _currentView;
        return Padding(
          padding: EdgeInsets.only(left: density.xs),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _changeView(view),
              borderRadius: BorderRadius.circular(corners.sm),
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: density.horizontalPadding,
                  vertical: density.xs,
                ),
                decoration: BoxDecoration(
                  color: isSelected ? colors.orange.primary : Colors.transparent,
                  borderRadius: BorderRadius.circular(corners.sm),
                ),
                child: Text(
                  _getViewLabel(view),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    color: isSelected ? colors.onPrimary : colors.onSurface,
                  ),
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCalendarView(VColors colors, VCorners corners, VComponentDensity density) {
    switch (_currentView) {
      case VCalendarView.month:
        return _buildMonthView(colors, corners, density);
      case VCalendarView.week:
        return _buildWeekView(colors, corners, density);
      case VCalendarView.day:
        return _buildDayView(colors, corners, density);
    }
  }

  Widget _buildMonthView(VColors colors, VCorners corners, VComponentDensity density) {
    final firstDayOfMonth = DateTime(_currentDate.year, _currentDate.month, 1);
    final lastDayOfMonth = DateTime(_currentDate.year, _currentDate.month + 1, 0);
    final firstDayOfCalendar = _getFirstDayOfCalendar(firstDayOfMonth);
    final lastDayOfCalendar = _getLastDayOfCalendar(lastDayOfMonth);

    final totalDays = lastDayOfCalendar.difference(firstDayOfCalendar).inDays + 1;
    final weeks = (totalDays / 7).ceil();

    return Column(
      children: [
        _buildWeekdayHeaders(colors, corners, density),
        Expanded(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return Column(
                children: List.generate(weeks, (weekIndex) {
                  return Expanded(
                    child: Row(
                      children: List.generate(7, (dayIndex) {
                        final dayOffset = weekIndex * 7 + dayIndex;
                        final date = firstDayOfCalendar.add(Duration(days: dayOffset));
                        return Expanded(
                          child: _buildDayCell(date, colors, corners, density),
                        );
                      }),
                    ),
                  );
                }),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildWeekView(VColors colors, VCorners corners, VComponentDensity density) {
    final startOfWeek = _getStartOfWeek(_currentDate);
    final config = widget.timeSlotConfig;
    final weekDates = List.generate(7, (index) => startOfWeek.add(Duration(days: index)));

    return Column(
      children: [
        // Day headers (compact, single line)
        _buildWeekdayHeaders(colors, corners, density),
        
        // All-day events section
        _buildAllDayEventsSection(weekDates, colors, corners, density, config),
        
        // Time grid with events
        Expanded(
          child: SingleChildScrollView(
            child: Container(
              height: _getTotalGridHeight(config),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Time labels column - now inside the scrollable area
                  if (config.showTimeLabels)
                    _buildTimeLabelsColumn(colors, density, config),
                  
                  // Days columns with time grid
                  Expanded(
                    child: Row(
                      children: weekDates.map((date) {
                        return Expanded(
                          child: _buildDayColumnWithTimeSlots(date, colors, corners, density, config),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDayView(VColors colors, VCorners corners, VComponentDensity density) {
    final config = widget.timeSlotConfig;
    
    return Column(
      children: [
        // Day header (compact)
        Container(
          height: 40,
          padding: EdgeInsets.symmetric(horizontal: density.xs),
          child: Center(
            child: Text(
              _formatDate(_currentDate, 'EEEE, MMMM d'),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: colors.onSurface,
              ),
            ),
          ),
        ),
        
        // All-day events section
        _buildAllDayEventsSection([_currentDate], colors, corners, density, config),
        
        // Time grid with events
        Expanded(
          child: SingleChildScrollView(
            child: Container(
              height: _getTotalGridHeight(config),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Time labels column - now inside the scrollable area
                  if (config.showTimeLabels)
                    _buildTimeLabelsColumn(colors, density, config),
                  
                  // Single day column with time grid
                  Expanded(
                    child: _buildDayColumnWithTimeSlots(_currentDate, colors, corners, density, config),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWeekdayHeaders(VColors colors, VCorners corners, VComponentDensity density) {
    final weekdays = _getWeekdayLabels();
    final config = widget.timeSlotConfig;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: density.xs),
      child: Row(
        children: [
          // Space for time labels column when shown
          if (config.showTimeLabels)
            SizedBox(width: 60), // Match the time labels column width
          
          // Compact day headers (single line)
          Expanded(
            child: Row(
              children: weekdays.map((weekday) {
                return Expanded(
                  child: _buildCompactDayHeader(weekday, colors, density),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDayCell(
    DateTime date,
    VColors colors,
    VCorners corners,
    VComponentDensity density, {
    bool isWeekView = false,
    bool isDayView = false,
  }) {
    final isToday = _isSameDay(date, DateTime.now());
    final isSelected = _selectedDate != null && _isSameDay(date, _selectedDate!);
    final isCurrentMonth = date.month == _currentDate.month;
    final dayEvents = _getEventsForDate(date);
    
    // Availability management
    final availability = widget.availabilityConfig;
    final dayAvailability = availability != null ? _getDayAvailability(date, availability) : null;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _selectDate(date),
        borderRadius: BorderRadius.circular(corners.sm),
        child: Container(
          margin: const EdgeInsets.all(0.5), // Smaller margin for tighter grid
          decoration: BoxDecoration(
            color: isDayView 
                ? colors.surface  // Use base surface color for day view
                : _getDayBackgroundColor(
                    isSelected, 
                    isToday, 
                    dayAvailability, 
                    colors, 
                    availability,
                  ),
            border: isDayView
                ? null  // No border for day view
                : Border.all(
                    color: isSelected
                        ? colors.orange.primary
                        : isToday
                            ? colors.orange.primary
                            : colors.neutral.shade200.withValues(alpha: 0.5), // Subtle border for all cells
                    width: isSelected ? 2 : (isToday ? 1 : 0.5),
                  ),
            borderRadius: BorderRadius.circular(corners.sm),
          ),
          child: Stack(
            children: [
              // Availability overlay
              if (availability != null && availability.showAvailabilityOverlay && dayAvailability != null)
                _buildAvailabilityOverlay(dayAvailability, availability, corners),
              
              // Main content
              Column(
                children: [
                  if (!isDayView)
                    Container(
                      height: 32,
                      alignment: Alignment.center,
                      child: Text(
                        date.day.toString(),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: isToday ? FontWeight.w600 : FontWeight.w400,
                          color: isCurrentMonth || isWeekView || isDayView
                              ? (isSelected ? colors.orange.primary : (isToday ? colors.orange.primary : colors.onSurface))
                              : colors.onSurface.withValues(alpha: 0.4),
                        ),
                      ),
                    ),
                  if (dayEvents.isNotEmpty && !isDayView)
                    Expanded(
                      child: _buildEventIndicators(dayEvents, colors, density),
                    ),
                  if (isDayView && dayEvents.isNotEmpty)
                    Expanded(
                      child: _buildEventList(dayEvents, colors, corners, density),
                    ),
                ],
              ),
              
              // Availability status indicator
              if (availability != null && dayAvailability != null)
                _buildAvailabilityStatusIndicator(dayAvailability, availability, corners),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEventIndicators(List<VCalendarEvent> events, VColors colors, VComponentDensity density) {
    if (events.isEmpty) return const SizedBox.shrink();

    // Separate all-day and timed events for month view
    final allDayEvents = events.where((event) => event.isAllDay).toList();
    final timedEvents = events.where((event) => !event.isAllDay).toList();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: density.xs),
      child: Column(
        children: [
          // All-day events as text strips at top
          ...allDayEvents.take(2).map((event) {
            final theme = getTheme();
            final eventColor = event.getDisplayColor(widget.colorConfig, theme);
            final backgroundColor = event.getBackgroundColor(widget.colorConfig, theme);
            return Container(
              height: 16,
              margin: EdgeInsets.only(bottom: density.xs / 2),
              padding: EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: eventColor, width: 0.5),
              ),
              child: Text(
                event.title,
                style: TextStyle(
                  fontSize: 9,
                  fontWeight: FontWeight.w500,
                  color: colors.onSurface,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            );
          }),
          
          // Timed events as small colored bars
          ...timedEvents.take(3 - allDayEvents.length).map((event) {
            final theme = getTheme();
            final eventColor = event.getDisplayColor(widget.colorConfig, theme);
            return Container(
              height: 4,
              margin: EdgeInsets.only(bottom: density.xs / 2),
              decoration: BoxDecoration(
                color: eventColor,
                borderRadius: BorderRadius.circular(2),
              ),
            );
          }),
          
          // Show "+" indicator if there are more events
          if (events.length > 3)
            Container(
              height: 12,
              child: Text(
                '+${events.length - 3} more',
                style: TextStyle(
                  fontSize: 8,
                  color: colors.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEventList(List<VCalendarEvent> events, VColors colors, VCorners corners, VComponentDensity density) {
    return ListView.builder(
      padding: EdgeInsets.all(density.horizontalPadding),
      itemCount: events.length,
      itemBuilder: (context, index) {
        final event = events[index];
        final theme = getTheme(); // Get theme from context
        final eventColor = event.getDisplayColor(widget.colorConfig, theme);
        final backgroundColor = event.getBackgroundColor(widget.colorConfig, theme);
        final textColor = event.getTextColor(widget.colorConfig, theme);
        
        return Container(
          margin: EdgeInsets.only(bottom: density.horizontalPadding),
          padding: EdgeInsets.all(density.horizontalPadding),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(corners.sm),
            border: Border.all(
              color: eventColor,
              width: 1,
            ),
          ),
          child: InkWell(
            onTap: () => widget.onEventTapped?.call(event),
            borderRadius: BorderRadius.circular(corners.sm),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        event.title,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: colors.onSurface,
                        ),
                      ),
                    ),
                    // Show status indicator
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: density.xs, vertical: density.xs / 2),
                      decoration: BoxDecoration(
                        color: eventColor,
                        borderRadius: BorderRadius.circular(corners.sm / 2),
                      ),
                      child: Text(
                        event.status.name.toUpperCase(),
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: textColor,
                        ),
                      ),
                    ),
                  ],
                ),
                if (event.description != null) ...[
                  SizedBox(height: density.xs),
                  Text(
                    event.description!,
                    style: TextStyle(
                      fontSize: 12,
                      color: colors.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
                if (!event.isAllDay && event.startTime != null) ...[
                  SizedBox(height: density.xs),
                  Text(
                    _formatTimeRange(event.startTime!, event.endTime),
                    style: TextStyle(
                      fontSize: 12,
                      color: colors.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  // Helper methods
  String _getHeaderTitle() {
    switch (_currentView) {
      case VCalendarView.month:
        return _formatDate(_currentDate, 'MMMM y');
      case VCalendarView.week:
        final startOfWeek = _getStartOfWeek(_currentDate);
        final endOfWeek = startOfWeek.add(const Duration(days: 6));
        if (startOfWeek.month == endOfWeek.month) {
          return '${_formatDate(startOfWeek, 'MMMM d')} - ${endOfWeek.day}, ${_formatDate(endOfWeek, 'y')}';
        } else {
          return '${_formatDate(startOfWeek, 'MMM d')} - ${_formatDate(endOfWeek, 'MMM d, y')}';
        }
      case VCalendarView.day:
        return _formatDate(_currentDate, 'EEEE, MMMM d, y');
    }
  }

  String _getViewLabel(VCalendarView view) {
    switch (view) {
      case VCalendarView.month:
        return 'Month';
      case VCalendarView.week:
        return 'Week';
      case VCalendarView.day:
        return 'Day';
    }
  }

  List<String> _getWeekdayLabels() {
    const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    final startIndex = widget.firstDayOfWeek == DateTime.sunday ? 6 : widget.firstDayOfWeek - 1;
    return [...weekdays.sublist(startIndex), ...weekdays.sublist(0, startIndex)];
  }

  DateTime _getFirstDayOfCalendar(DateTime firstDayOfMonth) {
    final weekday = firstDayOfMonth.weekday;
    final daysToSubtract = (weekday - widget.firstDayOfWeek) % 7;
    return firstDayOfMonth.subtract(Duration(days: daysToSubtract));
  }

  DateTime _getLastDayOfCalendar(DateTime lastDayOfMonth) {
    final weekday = lastDayOfMonth.weekday;
    final daysToAdd = (widget.firstDayOfWeek + 6 - weekday) % 7;
    return lastDayOfMonth.add(Duration(days: daysToAdd));
  }

  DateTime _getStartOfWeek(DateTime date) {
    final weekday = date.weekday;
    final daysToSubtract = (weekday - widget.firstDayOfWeek) % 7;
    return date.subtract(Duration(days: daysToSubtract));
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  bool _isDateInCurrentWeek(DateTime date) {
    final startOfWeek = _getStartOfWeek(_currentDate);
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return date.isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
           date.isBefore(endOfWeek.add(const Duration(days: 1)));
  }

  bool _isDateInCurrentMonth(DateTime date) {
    return date.year == _currentDate.year && date.month == _currentDate.month;
  }

  List<VCalendarEvent> _getEventsForDate(DateTime date) {
    return widget.events.where((event) => _isSameDay(event.date, date)).toList();
  }

  String _formatDate(DateTime date, String pattern) {
    // Simple date formatting - in a real app, you'd use intl package
    switch (pattern) {
      case 'MMMM y':
        return '${_getMonthName(date.month)} ${date.year}';
      case 'EEEE, MMMM d, y':
        return '${_getWeekdayName(date.weekday)}, ${_getMonthName(date.month)} ${date.day}, ${date.year}';
      case 'MMMM d':
        return '${_getMonthName(date.month)} ${date.day}';
      case 'MMM d':
        return '${_getMonthNameShort(date.month)} ${date.day}';
      case 'MMM d, y':
        return '${_getMonthNameShort(date.month)} ${date.day}, ${date.year}';
      case 'y':
        return '${date.year}';
      default:
        return '${date.month}/${date.day}/${date.year}';
    }
  }

  String _formatTimeRange(TimeOfDay startTime, TimeOfDay? endTime) {
    final start = _formatTime(startTime);
    if (endTime == null) return start;
    final end = _formatTime(endTime);
    return '$start - $end';
  }

  String _formatTime(TimeOfDay time) {
    final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }

  String _getMonthName(int month) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1];
  }

  String _getMonthNameShort(int month) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month - 1];
  }

  String _getWeekdayName(int weekday) {
    const weekdays = [
      'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
    ];
    return weekdays[weekday - 1];
  }

  // Navigation methods
  void _goToPrevious() {
    setState(() {
      switch (_currentView) {
        case VCalendarView.month:
          _currentDate = DateTime(_currentDate.year, _currentDate.month - 1, 1);
          break;
        case VCalendarView.week:
          _currentDate = _currentDate.subtract(const Duration(days: 7));
          break;
        case VCalendarView.day:
          _currentDate = _currentDate.subtract(const Duration(days: 1));
          break;
      }
    });
  }

  void _goToNext() {
    setState(() {
      switch (_currentView) {
        case VCalendarView.month:
          _currentDate = DateTime(_currentDate.year, _currentDate.month + 1, 1);
          break;
        case VCalendarView.week:
          _currentDate = _currentDate.add(const Duration(days: 7));
          break;
        case VCalendarView.day:
          _currentDate = _currentDate.add(const Duration(days: 1));
          break;
      }
    });
  }

  void _goToToday() {
    final today = DateTime.now();
    setState(() {
      switch (_currentView) {
        case VCalendarView.month:
          _currentDate = DateTime(today.year, today.month, 1);
          break;
        case VCalendarView.week:
        case VCalendarView.day:
          _currentDate = today;
          break;
      }
    });
    
    // Select today's date using the proper selection method
    _selectDate(today);
  }

  void _changeView(VCalendarView view) {
    if (view != _currentView) {
      setState(() {
        _currentView = view;
      });
      widget.onViewChanged?.call(view);
    }
  }

  void _selectDate(DateTime date) {
    setState(() {
      _selectedDate = date;
      _currentDate = date;
    });
    widget.onDateSelected?.call(date);
  }

  // Availability management helper methods

  /// Get availability information for a specific date
  _DayAvailability _getDayAvailability(DateTime date, VAvailabilityConfig config) {
    final dayOfWeek = date.weekday;
    final businessHours = config.businessHours[dayOfWeek];
    
    // Get time blocks that apply to this date
    final dayTimeBlocks = config.timeBlocks.where((block) {
      return _isSameDay(block.start, date) || 
             (block.isRecurring && _blockAppliesToDate(block, date));
    }).toList();
    
    // Check if there are any available slots
    final hasBusinessHours = businessHours?.isOpen ?? false;
    final hasBlockedTime = dayTimeBlocks.any((block) => 
      block.type == VTimeBlockType.blocked ||
      block.type == VTimeBlockType.holiday ||
      block.type == VTimeBlockType.break_
    );
    
    final isAvailable = hasBusinessHours && !hasBlockedTime;
    
    return _DayAvailability(
      hasBusinessHours: hasBusinessHours,
      isAvailable: isAvailable,
      timeBlocks: dayTimeBlocks,
      businessHours: businessHours,
    );
  }

  /// Check if a recurring time block applies to a specific date
  bool _blockAppliesToDate(VTimeBlock block, DateTime date) {
    if (!block.isRecurring || block.recurrencePattern == null) return false;
    
    switch (block.recurrencePattern) {
      case 'daily':
        return true;
      case 'weekly':
        return block.start.weekday == date.weekday;
      case 'monthly':
        return block.start.day == date.day;
      default:
        return false;
    }
  }

  /// Get the background color for a day cell based on availability
  Color _getDayBackgroundColor(
    bool isSelected,
    bool isToday,
    _DayAvailability? dayAvailability,
    VColors colors,
    VAvailabilityConfig? availability,
  ) {
    if (isSelected) {
      return colors.orange.primary.withValues(alpha: 0.2);
    }
    
    if (isToday) {
      return colors.orange.primary.withValues(alpha: 0.1);
    }
    
    if (availability != null && availability.showAvailabilityOverlay && dayAvailability != null) {
      if (!dayAvailability.hasBusinessHours) {
        return availability.unavailableColor.withValues(alpha: 0.05);
      } else if (!dayAvailability.isAvailable) {
        return availability.unavailableColor.withValues(alpha: 0.1);
      }
    }
    
    return Colors.transparent;
  }

  /// Build availability overlay for a day cell
  Widget _buildAvailabilityOverlay(
    _DayAvailability dayAvailability,
    VAvailabilityConfig config,
    VCorners corners,
  ) {
    if (dayAvailability.timeBlocks.isEmpty && dayAvailability.hasBusinessHours) {
      return const SizedBox.shrink();
    }

    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(corners.sm),
        ),
        child: Column(
          children: [
            // Business hours indicator (top strip)
            if (dayAvailability.hasBusinessHours)
              Container(
                height: 3,
                decoration: BoxDecoration(
                  color: config.businessHoursColor.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(corners.sm),
                    topRight: Radius.circular(corners.sm),
                  ),
                ),
              ),
            
            Expanded(
              child: Row(
                children: [
                  // Time block indicators (left strip)
                  if (dayAvailability.timeBlocks.isNotEmpty)
                    Container(
                      width: 3,
                      decoration: BoxDecoration(
                        color: _getTimeBlockColor(dayAvailability.timeBlocks, config),
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(corners.sm),
                        ),
                      ),
                    ),
                  
                  const Spacer(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build availability status indicator for a day cell
  Widget _buildAvailabilityStatusIndicator(
    _DayAvailability dayAvailability,
    VAvailabilityConfig config,
    VCorners corners,
  ) {
    if (dayAvailability.hasBusinessHours && dayAvailability.isAvailable) {
      return const SizedBox.shrink();
    }

    return Positioned(
      bottom: 2,
      right: 2,
      child: Container(
        width: 8,
        height: 8,
        decoration: BoxDecoration(
          color: dayAvailability.hasBusinessHours 
            ? (dayAvailability.isAvailable ? config.availableColor : config.unavailableColor)
            : config.unavailableColor,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 1),
        ),
      ),
    );
  }

  /// Get the appropriate color for time blocks
  Color _getTimeBlockColor(List<VTimeBlock> timeBlocks, VAvailabilityConfig config) {
    if (timeBlocks.isEmpty) return config.availableColor;
    
    // Priority order: blocked > holiday > break > buffer > available
    if (timeBlocks.any((block) => block.type == VTimeBlockType.blocked)) {
      return config.unavailableColor;
    } else if (timeBlocks.any((block) => block.type == VTimeBlockType.holiday)) {
      return config.unavailableColor;
    } else if (timeBlocks.any((block) => block.type == VTimeBlockType.break_)) {
      return config.bufferColor;
    } else if (timeBlocks.any((block) => block.type == VTimeBlockType.buffer)) {
      return config.bufferColor;
    } else {
      return config.availableColor;
    }
  }

  // Enhanced Week/Day View Methods for Time Slots

  /// Build time labels column for week/day view
  Widget _buildTimeLabelsColumn(VColors colors, VComponentDensity density, VTimeSlotConfig config) {
    return Container(
      width: 60,
      child: Container(
        height: _getTotalGridHeight(config),
        child: Stack(
          children: _buildTimeLabels(colors, config),
        ),
      ),
    );
  }

  /// Build individual time labels
  List<Widget> _buildTimeLabels(VColors colors, VTimeSlotConfig config) {
    final labels = <Widget>[];
    
    for (int hour = config.startHour; hour < config.endHour; hour++) {
      final position = (hour - config.startHour) * config.slotHeight;
      
      labels.add(
        Positioned(
          top: position,
          left: 0,
          right: 0,
          child: Container(
            height: config.slotHeight,
            alignment: Alignment.topRight,
            padding: const EdgeInsets.only(right: 8, top: 4),
            child: Text(
              _formatHour(hour),
              style: TextStyle(
                fontSize: 11,
                color: colors.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ),
        ),
      );
    }
    
    return labels;
  }

  /// Get total height needed for the time grid
  double _getTotalGridHeight(VTimeSlotConfig config) {
    final totalHours = config.endHour - config.startHour;
    return totalHours * config.slotHeight;
  }

  /// Build a day column with time slots and events
  Widget _buildDayColumnWithTimeSlots(
    DateTime date,
    VColors colors,
    VCorners corners,
    VComponentDensity density,
    VTimeSlotConfig config,
  ) {
    final dayEvents = _getEventsForDate(date);
    final isToday = _isSameDay(date, DateTime.now());
    
    return Container(
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(
            color: colors.neutral.shade200.withValues(alpha: 0.5),
            width: 0.5,
          ),
        ),
      ),
      child: Stack(
        children: [
          // Time grid background
          _buildTimeGrid(colors, config),
          
          // Current time indicator
          if (isToday && config.showNowIndicator)
            _buildNowIndicator(colors, config),
          
          // Events positioned on time grid
          ..._buildPositionedEvents(dayEvents, colors, corners, density, config),
        ],
      ),
    );
  }

  /// Build time grid background with hour lines
  Widget _buildTimeGrid(VColors colors, VTimeSlotConfig config) {
    return Container(
      child: Stack(
        children: _buildGridLines(colors, config),
      ),
    );
  }

  /// Build grid lines for hours and sub-intervals
  List<Widget> _buildGridLines(VColors colors, VTimeSlotConfig config) {
    final lines = <Widget>[];
    
    for (int hour = config.startHour; hour <= config.endHour; hour++) {
      final position = (hour - config.startHour) * config.slotHeight;
      
      // Major hour line
      lines.add(
        Positioned(
          top: position,
          left: 0,
          right: 0,
          child: Container(
            height: 1,
            color: colors.neutral.shade300.withValues(alpha: 0.8),
          ),
        ),
      );
      
      // Minor grid lines (15-minute intervals)
      if (config.showMinorGridLines && hour < config.endHour) {
        for (int division = 1; division < config.minorSlotDivisions; division++) {
          final minorPosition = position + (division * config.slotHeight / config.minorSlotDivisions);
          
          lines.add(
            Positioned(
              top: minorPosition,
              left: 0,
              right: 0,
              child: Container(
                height: 0.5,
                color: colors.neutral.shade200.withValues(alpha: 0.4),
              ),
            ),
          );
        }
      }
    }
    
    return lines;
  }

  /// Build current time indicator line
  Widget _buildNowIndicator(VColors colors, VTimeSlotConfig config) {
    final now = TimeOfDay.now();
    final position = config.getYPositionForTime(now);
    
    return Positioned(
      top: position,
      left: 0,
      right: 0,
      child: Container(
        height: 2,
        color: colors.orange.primary,
        child: Row(
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: colors.orange.primary,
                shape: BoxShape.circle,
              ),
            ),
            Expanded(
              child: Container(
                height: 2,
                color: colors.orange.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build positioned events on the time grid
  List<Widget> _buildPositionedEvents(
    List<VCalendarEvent> events,
    VColors colors,
    VCorners corners,
    VComponentDensity density,
    VTimeSlotConfig config,
  ) {
    final positionedEvents = <Widget>[];
    
    // Only process timed events - all-day events are now in the header
    final timedEvents = events.where((event) => !event.isAllDay && event.startTime != null);
    
    for (final event in timedEvents) {
      // Timed events positioned by time
      final startY = config.getYPositionForTime(event.startTime!);
      final duration = event.endTime != null 
        ? Duration(
            hours: event.endTime!.hour - event.startTime!.hour,
            minutes: event.endTime!.minute - event.startTime!.minute,
          )
        : const Duration(hours: 1);
      final height = config.getHeightForDuration(duration);
      
      positionedEvents.add(
        Positioned(
          top: startY,
          left: 2,
          right: 2,
          height: height.clamp(30.0, config.slotHeight * 4), // Min 30px for readable text, max 4 hours
          child: _buildTimedEvent(event, colors, corners, density),
        ),
      );
    }
    
    return positionedEvents;
  }

  /// Build all-day event widget
  Widget _buildAllDayEvent(
    VCalendarEvent event,
    VColors colors,
    VCorners corners,
    VComponentDensity density,
  ) {
    final theme = getTheme();
    final eventColor = event.getDisplayColor(widget.colorConfig, theme);
    final backgroundColor = event.getBackgroundColor(widget.colorConfig, theme);
    
    return GestureDetector(
      onTap: () {
        // TODO: Add modal or details view for full event information
        print('Tapped all-day event: ${event.title}');
      },
      child: Container(
        height: 24,
        margin: EdgeInsets.only(bottom: density.xs / 2),
        padding: EdgeInsets.symmetric(horizontal: density.xs),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(corners.sm / 2),
          border: Border.all(color: eventColor, width: 1),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                event.title,
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: colors.onSurface,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build timed event widget with smart content layout
  Widget _buildTimedEvent(
    VCalendarEvent event,
    VColors colors,
    VCorners corners,
    VComponentDensity density,
  ) {
    final theme = getTheme();
    final eventColor = event.getDisplayColor(widget.colorConfig, theme);
    final backgroundColor = event.getBackgroundColor(widget.colorConfig, theme);
    
    return GestureDetector(
      onTap: () {
        // TODO: Add modal or details view for full event information
        print('Tapped event: ${event.title}');
      },
      child: Container(
        margin: EdgeInsets.only(bottom: density.xs / 3), // Reduced margin for more space
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(corners.sm / 2),
          border: Border.all(color: eventColor, width: 2),
        ),
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Smart layout based on available height
            final availableHeight = constraints.maxHeight;
            final hasTimeSlot = event.startTime != null;
            
            // More aggressive layout - utilize space better
            double titleFontSize;
            double timeFontSize;
            int titleMaxLines;
            double containerPadding;
            
            if (availableHeight < 30) {
              // Very small space - minimal padding, compact text
              titleFontSize = 9;
              timeFontSize = 8;
              titleMaxLines = 1;
              containerPadding = 2;
            } else if (availableHeight < 45) {
              // Small space - allow 2 lines for title
              titleFontSize = 10;
              timeFontSize = 8;
              titleMaxLines = 2;
              containerPadding = 3;
            } else if (availableHeight < 70) {
              // Medium space - better spacing
              titleFontSize = 11;
              timeFontSize = 9;
              titleMaxLines = hasTimeSlot ? 2 : 3;
              containerPadding = 4;
            } else {
              // Large space - full layout
              titleFontSize = 12;
              timeFontSize = 10;
              titleMaxLines = hasTimeSlot ? 3 : 4;
              containerPadding = density.xs;
            }
            
            return Padding(
              padding: EdgeInsets.all(containerPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Title - always shown, takes priority
                  Expanded(
                    flex: hasTimeSlot && availableHeight > 35 ? 3 : 4,
                    child: Text(
                      event.title,
                      style: TextStyle(
                        fontSize: titleFontSize,
                        fontWeight: FontWeight.w600,
                        color: colors.onSurface,
                        height: 1.1, // Slightly looser for readability
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: titleMaxLines,
                    ),
                  ),
                  
                  // Time - only shown if there's reasonable space
                  if (hasTimeSlot && availableHeight >= 35) ...[
                    SizedBox(height: 1),
                    Expanded(
                      flex: 1,
                      child: Text(
                        _formatTimeRange(event.startTime!, event.endTime),
                        style: TextStyle(
                          fontSize: timeFontSize,
                          color: colors.onSurface.withValues(alpha: 0.7),
                          height: 1.0,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ],
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  /// Format hour for time labels
  String _formatHour(int hour) {
    if (hour == 0) return '12 AM';
    if (hour < 12) return '$hour AM';
    if (hour == 12) return '12 PM';
    return '${hour - 12} PM';
  }

  /// Build compact day header (just the day label)
  Widget _buildCompactDayHeader(String label, VColors colors, VComponentDensity density) {
    return Container(
      height: 40,
      child: Center(
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: colors.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ),
    );
  }

  /// Build all-day events section for week/day views
  Widget _buildAllDayEventsSection(
    List<DateTime> dates,
    VColors colors,
    VCorners corners,
    VComponentDensity density,
    VTimeSlotConfig config,
  ) {
    // Check if any of the dates have all-day events
    final hasAllDayEvents = dates.any((date) => 
      _getEventsForDate(date).any((event) => event.isAllDay));
    
    if (!hasAllDayEvents) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: density.xs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // All-day label
          Container(
            height: 24,
            padding: EdgeInsets.only(left: config.showTimeLabels ? 68 : 8), // Align with time grid
            child: Text(
              'All Day',
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w500,
                color: colors.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ),
          
          // All-day events grid
          Container(
            padding: EdgeInsets.only(left: config.showTimeLabels ? 60 : 0), // Space for time labels
            child: Row(
              children: dates.map((date) {
                final allDayEvents = _getEventsForDate(date).where((event) => event.isAllDay).toList();
                return Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 2),
                    child: Column(
                      children: allDayEvents.map((event) => Container(
                        margin: EdgeInsets.only(bottom: density.xs / 2),
                        child: _buildAllDayEvent(event, colors, corners, density),
                      )).toList(),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          
          SizedBox(height: density.sm),
        ],
      ),
    );
  }
}
