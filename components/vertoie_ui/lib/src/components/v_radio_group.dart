import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import 'v_radio.dart';

/// A radio option for use with VRadioGroup
class VRadioOption<T> {
  const VRadioOption({
    required this.value,
    required this.label,
    this.enabled = true,
  });

  final T value;
  final String label;
  final bool enabled;
}

/// Layout direction for VRadioGroup
enum VRadioGroupDirection {
  vertical,
  horizontal,
}

/// A themeable radio group widget that manages multiple radio buttons.
/// Supports both horizontal and vertical layouts.
class VRadioGroup<T> extends VWidget {
  const VRadioGroup({
    super.key,
    required this.options,
    required this.onChanged,
    this.value,
    this.direction = VRadioGroupDirection.vertical,
    this.enabled = true,
    this.label,
  });

  /// List of radio options to display
  final List<VRadioOption<T>> options;

  /// Called when the selection changes
  final ValueChanged<T?> onChanged;

  /// Currently selected value
  final T? value;

  /// Layout direction for the radio buttons
  final VRadioGroupDirection direction;

  /// Whether the entire group is enabled
  final bool enabled;

  /// Optional label for the group
  final String? label;

  @override
  Widget build(BuildContext context) {
    final density = getDensity(context);
    final colors = getColors(context);

    final children = <Widget>[];

    // Add group label if provided
    if (label != null) {
      children.add(
        Padding(
          padding: EdgeInsets.only(bottom: density.verticalPadding),
          child: Text(
            label!,
            style: TextStyle(
              color: colors.onSurface,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      );
    }

    // Create radio button widgets
    final radioWidgets = options.map((option) {
      return VRadio<T>(
        value: option.value,
        groupValue: value,
        enabled: enabled && option.enabled,
        label: option.label,
        onChanged: onChanged,
      );
    }).toList();

    if (direction == VRadioGroupDirection.vertical) {
      // Add radio buttons vertically with spacing
      for (int i = 0; i < radioWidgets.length; i++) {
        children.add(radioWidgets[i]);
        if (i < radioWidgets.length - 1) {
          children.add(SizedBox(height: density.verticalPadding));
        }
      }
      
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      );
    } else {
      // Add radio buttons horizontally with spacing
      final horizontalChildren = <Widget>[];
      for (int i = 0; i < radioWidgets.length; i++) {
        horizontalChildren.add(radioWidgets[i]);
        if (i < radioWidgets.length - 1) {
          horizontalChildren.add(SizedBox(width: density.horizontalPadding));
        }
      }
      
      children.add(
        Wrap(
          spacing: density.horizontalPadding,
          runSpacing: density.verticalPadding,
          children: horizontalChildren,
        ),
      );
      
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      );
    }
  }
}
