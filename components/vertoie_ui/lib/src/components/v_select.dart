import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import '../theme/v_theme_provider.dart';
import '../theme/v_theme_data.dart';

/// A select item for use with VSelect
class VSelectItem<T> {
  const VSelectItem({
    required this.value,
    required this.label,
    this.searchableText,
    this.enabled = true,
    this.child,
  });

  final T value;
  final String label;
  final String? searchableText;
  final bool enabled;
  final Widget? child;

  /// Get the text to search against
  String get searchText => searchableText ?? label;
}

/// Selection mode for VSelect
enum VSelectMode {
  single,
  multiple,
}

/// A themed select component with single-select, multi-select, and search capabilities
class VSelect<T> extends VWidget {
  const VSelect({
    super.key,
    required this.items,
    this.onChanged,
    this.onMultiChanged,
    this.value,
    this.values = const [],
    this.mode = VSelectMode.single,
    this.searchable = false,
    this.hint,
    this.label,
    this.enabled = true,
    this.maxHeight = 200,
    this.searchHint = 'Search...',
    this.noItemsFoundText = 'No items found',
    this.focusNode,
    this.autofocus = false,
  }) : assert(
         mode == VSelectMode.single ? onChanged != null : onMultiChanged != null,
         'Single select mode requires onChanged, multiple select requires onMultiChanged',
       );

  final List<VSelectItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final ValueChanged<List<T>>? onMultiChanged;
  final T? value;
  final List<T> values;
  final VSelectMode mode;
  final bool searchable;
  final String? hint;
  final String? label;
  final bool enabled;
  final double maxHeight;
  final String searchHint;
  final String noItemsFoundText;
  final FocusNode? focusNode;
  final bool autofocus;

  @override
  Widget build(BuildContext context) {
    return _VSelectWidget<T>(
      items: items,
      onChanged: onChanged,
      onMultiChanged: onMultiChanged,
      value: value,
      values: values,
      mode: mode,
      searchable: searchable,
      hint: hint,
      label: label,
      enabled: enabled,
      maxHeight: maxHeight,
      searchHint: searchHint,
      noItemsFoundText: noItemsFoundText,
      focusNode: focusNode,
      autofocus: autofocus,
    );
  }
}

/// Internal widget that handles the select functionality
class _VSelectWidget<T> extends StatefulWidget {
  const _VSelectWidget({
    required this.items,
    this.onChanged,
    this.onMultiChanged,
    this.value,
    this.values = const [],
    this.mode = VSelectMode.single,
    this.searchable = false,
    this.hint,
    this.label,
    this.enabled = true,
    this.maxHeight = 200,
    this.searchHint = 'Search...',
    this.noItemsFoundText = 'No items found',
    this.focusNode,
    this.autofocus = false,
  });

  final List<VSelectItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final ValueChanged<List<T>>? onMultiChanged;
  final T? value;
  final List<T> values;
  final VSelectMode mode;
  final bool searchable;
  final String? hint;
  final String? label;
  final bool enabled;
  final double maxHeight;
  final String searchHint;
  final String noItemsFoundText;
  final FocusNode? focusNode;
  final bool autofocus;

  @override
  State<_VSelectWidget<T>> createState() => _VSelectWidgetState<T>();
}

class _VSelectWidgetState<T> extends State<_VSelectWidget<T>> {
  late FocusNode _focusNode;
  final FocusNode _searchFocusNode = FocusNode();
  final TextEditingController _searchController = TextEditingController();
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isOpen = false;
  List<VSelectItem<T>> _filteredItems = [];
  List<T> _selectedValues = [];

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _filteredItems = widget.items;
    _selectedValues = widget.mode == VSelectMode.single
        ? (widget.value != null ? [widget.value as T] : [])
        : List.from(widget.values);
    
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void didUpdateWidget(_VSelectWidget<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.items != oldWidget.items) {
      _filteredItems = widget.items;
      _onSearchChanged();
    }
    if (widget.mode == VSelectMode.single && widget.value != oldWidget.value) {
      _selectedValues = widget.value != null ? [widget.value as T] : [];
    }
    if (widget.mode == VSelectMode.multiple && widget.values != oldWidget.values) {
      _selectedValues = List.from(widget.values);
    }
  }

  @override
  void dispose() {
    _searchController
      ..removeListener(_onSearchChanged)
      ..dispose();
    _searchFocusNode.dispose();
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _removeOverlay();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase();
    _filteredItems = query.isEmpty
        ? widget.items
        : widget.items.where((item) =>
            item.searchText.toLowerCase().contains(query)).toList();

    // Update the overlay content without rebuilding the entire overlay
    _overlayEntry?.markNeedsBuild();
  }

  void _toggleDropdown() {
    if (!widget.enabled) return;
    
    if (_isOpen) {
      _closeDropdown();
    } else {
      _openDropdown();
    }
  }

  void _openDropdown() {
    if (_isOpen) return;

    _searchController.clear();
    _filteredItems = widget.items;

    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);

    setState(() {
      _isOpen = true;
    });

    // Focus the search field if searchable
    if (widget.searchable) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _searchFocusNode.requestFocus();
      });
    }
  }

  void _closeDropdown() {
    if (!_isOpen) return;
    
    _removeOverlay();
    setState(() {
      _isOpen = false;
    });
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _onItemSelected(T value) {
    if (widget.mode == VSelectMode.single) {
      setState(() {
        _selectedValues = [value];
      });
      widget.onChanged?.call(value);
      _closeDropdown();
    } else {
      setState(() {
        if (_selectedValues.contains(value)) {
          _selectedValues.remove(value);
        } else {
          _selectedValues.add(value);
        }
      });
      widget.onMultiChanged?.call(List.from(_selectedValues));
      // Update the overlay content to reflect the new selection state
      _overlayEntry?.markNeedsBuild();
    }
  }

  void _removeSelectedItem(T value) {
    if (widget.mode == VSelectMode.multiple) {
      setState(() {
        _selectedValues.remove(value);
      });
      widget.onMultiChanged?.call(List.from(_selectedValues));
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = VThemeProvider.of(context);
    final colors = theme.colors;
    final density = theme.componentDensity;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: colors.onSurface,
            ),
          ),
          SizedBox(height: density.xs),
        ],
        CompositedTransformTarget(
          link: _layerLink,
          child: _buildSelectField(theme),
        ),
      ],
    );
  }

  Widget _buildSelectField(VThemeData theme) {
    final colors = theme.colors;
    final corners = theme.corners;
    final density = theme.componentDensity;

    final selectHeight = density.height * 1.05;
    final horizontalPadding = density.horizontalPadding;
    final verticalPadding = (selectHeight - 20) / 2; // Center 14px text with 1.2 line height

    return GestureDetector(
      onTap: _toggleDropdown,
      child: Container(
        height: widget.mode == VSelectMode.multiple && _selectedValues.isNotEmpty
            ? null
            : selectHeight,
        constraints: widget.mode == VSelectMode.multiple
            ? BoxConstraints(minHeight: selectHeight)
            : null,
        decoration: BoxDecoration(
          border: Border.all(
            color: _isOpen
                ? colors.primary
                : (widget.enabled ? colors.onSurface.withValues(alpha: 0.3) : colors.onSurface.withValues(alpha: 0.1)),
            width: _isOpen ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(corners.md),
          color: widget.enabled ? colors.surface : colors.surfaceVariant,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: widget.mode == VSelectMode.multiple && _selectedValues.isNotEmpty
              ? density.xs
              : verticalPadding,
        ),
        child: _buildSelectContent(theme),
      ),
    );
  }

  Widget _buildSelectContent(VThemeData theme) {
    if (widget.mode == VSelectMode.multiple) {
      return _buildMultiSelectContent(theme);
    } else {
      return _buildSingleSelectContent(theme);
    }
  }

  Widget _buildSingleSelectContent(VThemeData theme) {
    final colors = theme.colors;

    final selectedItem = _selectedValues.isNotEmpty
        ? widget.items.firstWhere((item) => item.value == _selectedValues.first,
            orElse: () => widget.items.first)
        : null;

    return Row(
      children: [
        Expanded(
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              selectedItem?.label ?? widget.hint ?? 'Select an option',
              style: TextStyle(
                color: selectedItem != null
                    ? colors.onSurface
                    : colors.onSurface.withValues(alpha: 0.6),
                fontSize: 14,
                height: 1.2,
              ),
            ),
          ),
        ),
        Icon(
          _isOpen ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
          color: widget.enabled
              ? colors.onSurface
              : colors.onSurface.withValues(alpha: 0.6),
        ),
      ],
    );
  }

  Widget _buildMultiSelectContent(VThemeData theme) {
    final colors = theme.colors;
    final density = theme.componentDensity;

    if (_selectedValues.isEmpty) {
      return Row(
        children: [
          Expanded(
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                widget.hint ?? 'Select options',
                style: TextStyle(
                  color: colors.onSurface.withValues(alpha: 0.6),
                  fontSize: 14,
                  height: 1.2,
                ),
              ),
            ),
          ),
          Icon(
            _isOpen ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
            color: widget.enabled
                ? colors.onSurface
                : colors.onSurface.withValues(alpha: 0.6),
          ),
        ],
      );
    }

    return Row(
      children: [
        Expanded(
          child: Wrap(
            spacing: density.xs,
            runSpacing: density.xs,
            children: _selectedValues.map((value) {
              final item = widget.items.firstWhere((item) => item.value == value);
              return _buildChip(item, theme);
            }).toList(),
          ),
        ),
        SizedBox(width: density.horizontalPadding),
        Icon(
          _isOpen ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
          color: widget.enabled
              ? colors.onSurface
              : colors.onSurface.withValues(alpha: 0.6),
        ),
      ],
    );
  }

  Widget _buildChip(VSelectItem<T> item, VThemeData theme) {
    final colors = theme.colors;
    final corners = theme.corners;
    final density = theme.componentDensity;

    return Container(
      decoration: BoxDecoration(
        color: colors.primaryVariant.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(corners.sm),
        border: Border.all(color: colors.primaryVariant.withValues(alpha: 0.3)),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: density.horizontalPadding,
        vertical: density.xs / 2,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            item.label,
            style: TextStyle(
              color: colors.onSurface,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: density.xs),
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () => _removeSelectedItem(item.value),
              child: Icon(
                Icons.close,
                size: 14,
                color: colors.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  OverlayEntry _createOverlayEntry() {
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    return OverlayEntry(
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _closeDropdown,
        child: Stack(
          children: [
            // Invisible full-screen area to catch outside taps
            Positioned.fill(
              child: Container(color: Colors.transparent),
            ),
            // Actual dropdown
            Positioned(
              width: size.width,
              child: CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                offset: Offset(0, size.height - (size.height / 3)),
                child: GestureDetector(
                  onTap: () {}, // Prevent tap from bubbling up to close dropdown
                  child: Material(
                    elevation: 8,
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.transparent,
                    child: StatefulBuilder(
                      builder: (context, setOverlayState) => _buildDropdownContent(),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdownContent() {
    final theme = VThemeProvider.of(context);
    final colors = theme.colors;
    final corners = theme.corners;

    return ClipRRect(
      borderRadius: BorderRadius.circular(corners.md),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: widget.searchable ? widget.maxHeight + 60 : widget.maxHeight,
        ),
        decoration: BoxDecoration(
          color: colors.surface,
          borderRadius: BorderRadius.circular(corners.md),
          border: Border.all(color: colors.onSurface.withValues(alpha: 0.3)),
          boxShadow: [
            BoxShadow(
              color: colors.onSurface.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.searchable) _buildSearchField(theme),
            Flexible(
              child: _filteredItems.isEmpty
                  ? _buildNoItemsFound(theme)
                  : _buildItemsList(theme),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchField(VThemeData theme) {
    final colors = theme.colors;
    final corners = theme.corners;
    final density = theme.componentDensity;

    return Container(
      padding: EdgeInsets.all(density.horizontalPadding),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: colors.onSurface.withValues(alpha: 0.2)),
        ),
      ),
      child: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        autofocus: true,
        style: TextStyle(fontSize: 14, color: colors.onSurface),
        decoration: InputDecoration(
          hintText: widget.searchHint,
          hintStyle: TextStyle(color: colors.onSurface.withValues(alpha: 0.6)),
          prefixIcon: Icon(Icons.search, color: colors.onSurface.withValues(alpha: 0.6)),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(corners.sm),
            borderSide: BorderSide(color: colors.onSurface.withValues(alpha: 0.3)),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(corners.sm),
            borderSide: BorderSide(color: colors.onSurface.withValues(alpha: 0.3)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(corners.sm),
            borderSide: BorderSide(color: colors.primary, width: 2),
          ),
          fillColor: colors.surfaceVariant,
          filled: true,
          contentPadding: EdgeInsets.symmetric(
            horizontal: density.horizontalPadding,
            vertical: density.xs,
          ),
          isDense: true,
        ),
      ),
    );
  }

  Widget _buildNoItemsFound(VThemeData theme) {
    final colors = theme.colors;
    final density = theme.componentDensity;

    return Padding(
      padding: EdgeInsets.all(density.horizontalPadding),
      child: Text(
        widget.noItemsFoundText,
        style: TextStyle(
          color: colors.onSurface.withValues(alpha: 0.6),
          fontSize: 14,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildItemsList(VThemeData theme) {
    final colors = theme.colors;
    final corners = theme.corners;
    final density = theme.componentDensity;

    return ListView.builder(
      shrinkWrap: true,
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        final isSelected = _selectedValues.contains(item.value);

        return Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: item.enabled ? () => _onItemSelected(item.value) : null,
            hoverColor: colors.primaryVariant.withValues(alpha: 0.08),
            highlightColor: colors.primaryVariant.withValues(alpha: 0.12),
            borderRadius: BorderRadius.circular(corners.sm),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: density.horizontalPadding,
                vertical: density.verticalPadding,
              ),
              decoration: BoxDecoration(
                color: isSelected ? colors.primaryVariant.withValues(alpha: 0.1) : Colors.transparent,
              ),
              child: Row(
                children: [
                  if (widget.mode == VSelectMode.multiple) ...[
                    Icon(
                      isSelected ? Icons.check_box : Icons.check_box_outline_blank,
                      color: isSelected ? colors.primary : colors.onSurface.withValues(alpha: 0.6),
                      size: 20,
                    ),
                    SizedBox(width: density.horizontalPadding),
                  ],
                  Expanded(
                    child: item.child ?? Text(
                      item.label,
                      style: TextStyle(
                        color: item.enabled
                            ? colors.onSurface
                            : colors.onSurface.withValues(alpha: 0.4),
                        fontSize: 14,
                        fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                      ),
                    ),
                  ),
                  if (widget.mode == VSelectMode.single && isSelected)
                    Icon(
                      Icons.check,
                      color: colors.primary,
                      size: 20,
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
