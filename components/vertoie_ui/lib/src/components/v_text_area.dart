import 'package:flutter/material.dart';
import '../base/v_widget.dart';

/// A themeable multi-line text area widget that follows Vertoie design system.
/// Features auto-expanding functionality and optional character counter.
class VTextArea extends VWidget {
  const VTextArea({
    super.key,
    this.controller,
    this.placeholder,
    this.onChanged,
    this.validator,
    this.enabled = true,
    this.minLines = 3,
    this.maxLines,
    this.maxLength,
    this.showCharacterCounter = false,
    this.autoExpand = true,
  });

  final TextEditingController? controller;
  final String? placeholder;
  final ValueChanged<String>? onChanged;
  final String? Function(String?)? validator;
  final bool enabled;
  final int minLines;
  final int? maxLines;
  final int? maxLength;
  final bool showCharacterCounter;
  final bool autoExpand;

  @override
  Widget build(BuildContext context) {
    final colors = getColors(context);
    final corners = getCorners(context);
    final density = getDensity(context);

    // Calculate padding to match VInput styling
    final horizontalPadding = density.horizontalPadding;
    final verticalPadding = density.verticalPadding;

    // Calculate content padding
    final contentPadding = EdgeInsets.symmetric(
      horizontal: horizontalPadding,
      vertical: verticalPadding,
    );

    // Define border colors with proper dark mode support (same as VInput)
    final borderColor = colors.onSurface.withValues(alpha: 0.3);
    final focusedBorderColor = colors.primary;
    final disabledBorderColor = colors.onSurface.withValues(alpha: 0.1);
    final errorBorderColor = colors.error;

    // Define text colors for dark mode support (same as VInput)
    final textColor = colors.onSurface;
    final hintColor = colors.onSurface.withValues(alpha: 0.6);
    final disabledTextColor = colors.onSurface.withValues(alpha: 0.4);
    final counterColor = colors.onSurface.withValues(alpha: 0.6);

    // Calculate effective max lines for auto-expand
    final effectiveMaxLines = autoExpand ? maxLines : minLines;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: controller,
          onChanged: onChanged,
          validator: validator,
          enabled: enabled,
          keyboardType: TextInputType.multiline,
          textInputAction: TextInputAction.newline,
          minLines: minLines,
          maxLines: effectiveMaxLines,
          maxLength: maxLength,
          style: TextStyle(
            color: enabled ? textColor : disabledTextColor,
            fontSize: 14,
          ),
          decoration: InputDecoration(
            hintText: placeholder,
            hintStyle: TextStyle(
              color: hintColor,
              fontSize: 14,
            ),
            filled: true,
            fillColor: enabled ? colors.surface : colors.surfaceVariant,
            contentPadding: contentPadding,
            counterText: '', // Hide default counter, we'll show our own

            // Default border (unfocused)
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(corners.md),
              borderSide: BorderSide(
                color: borderColor,
                width: 1,
              ),
            ),

            // Focused border (orange)
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(corners.md),
              borderSide: BorderSide(
                color: focusedBorderColor,
                width: 2,
              ),
            ),

            // Disabled border
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(corners.md),
              borderSide: BorderSide(
                color: disabledBorderColor,
                width: 1,
              ),
            ),

            // Error border
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(corners.md),
              borderSide: BorderSide(
                color: errorBorderColor,
                width: 1,
              ),
            ),

            // Focused error border
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(corners.md),
              borderSide: BorderSide(
                color: errorBorderColor,
                width: 2,
              ),
            ),
          ),
        ),

        // Custom character counter
        if (showCharacterCounter && maxLength != null) ...[
          SizedBox(height: density.xs),
          Align(
            alignment: Alignment.centerRight,
            child: _CharacterCounter(
              controller: controller,
              maxLength: maxLength!,
              color: counterColor,
            ),
          ),
        ],
      ],
    );
  }
}

/// Internal widget for displaying character count
class _CharacterCounter extends StatefulWidget {
  const _CharacterCounter({
    required this.controller,
    required this.maxLength,
    required this.color,
  });

  final TextEditingController? controller;
  final int maxLength;
  final Color color;

  @override
  State<_CharacterCounter> createState() => _CharacterCounterState();
}

class _CharacterCounterState extends State<_CharacterCounter> {
  late TextEditingController _controller;
  bool _isControllerInternal = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _isControllerInternal = widget.controller == null;
    _controller.addListener(_onTextChanged);
  }

  @override
  void didUpdateWidget(_CharacterCounter oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.controller != widget.controller) {
      _controller.removeListener(_onTextChanged);
      if (_isControllerInternal) {
        _controller.dispose();
      }
      _controller = widget.controller ?? TextEditingController();
      _isControllerInternal = widget.controller == null;
      _controller.addListener(_onTextChanged);
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    if (_isControllerInternal) {
      _controller.dispose();
    }
    super.dispose();
  }

  void _onTextChanged() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final currentLength = _controller.text.length;
    final isOverLimit = currentLength > widget.maxLength;
    
    return Text(
      '$currentLength/${widget.maxLength}',
      style: TextStyle(
        fontSize: 12,
        color: isOverLimit ? Colors.red : widget.color,
      ),
    );
  }
}
