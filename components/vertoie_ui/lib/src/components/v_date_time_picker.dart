import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import '../theme/v_theme_data.dart';
import '../theme/v_theme.dart';
import 'v_time_picker.dart';

/// A themeable date-time picker widget that follows Vertoie design system.
/// Combines date and time selection in a single component.
class VDateTimePicker extends VStatefulWidget {
  const VDateTimePicker({
    super.key,
    this.value,
    this.onChanged,
    this.firstDate,
    this.lastDate,
    this.placeholder = 'Select date & time',
    this.label,
    this.enabled = true,
    this.showClearButton = true,
    this.timeFormat = VTimeFormat.hour12,
    this.dateTimeFormat,
    this.separateFields = false,
    this.datePlaceholder = 'Date',
    this.timePlaceholder = 'Time',
  });

  /// The currently selected date and time
  final DateTime? value;

  /// Called when the date-time selection changes
  final ValueChanged<DateTime?>? onChanged;

  /// The earliest allowable date
  final DateTime? firstDate;

  /// The latest allowable date  
  final DateTime? lastDate;

  /// Placeholder text when no date-time is selected
  final String placeholder;

  /// Optional label for the date-time picker
  final String? label;

  /// Whether the date-time picker is enabled
  final bool enabled;

  /// Whether to show a clear button when a date-time is selected
  final bool showClearButton;

  /// Time format (12-hour or 24-hour)
  final VTimeFormat timeFormat;

  /// Custom date-time format function. If null, uses default format.
  final String Function(DateTime)? dateTimeFormat;

  /// Whether to show separate fields for date and time
  final bool separateFields;

  /// Placeholder text for date field when using separate fields
  final String datePlaceholder;

  /// Placeholder text for time field when using separate fields
  final String timePlaceholder;

  @override
  VState<VDateTimePicker> createState() => _VDateTimePickerState();
}

class _VDateTimePickerState extends VState<VDateTimePicker> {
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colors = getColors();
    final corners = getCorners();
    final density = getDensity();
    final motion = getMotion();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: TextStyle(
              color: colors.onSurface,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: density.smallGap), // Semantic small gap for label spacing
        ],
        if (widget.separateFields)
          _buildSeparateFields(colors, corners, density, motion)
        else
          _buildCombinedField(colors, corners, density, motion),
      ],
    );
  }

  Widget _buildCombinedField(VColors colors, VCorners corners, VComponentDensity density, VMotion motion) {
    final hasValue = widget.value != null;
    final displayText = hasValue 
        ? _formatDateTime(widget.value!)
        : widget.placeholder;

    return AnimatedContainer(
      duration: motion.duration,
      curve: motion.curve,
      height: density.height, // Use semantic height directly
      decoration: BoxDecoration(
        color: widget.enabled ? colors.surface : colors.neutral.shade100,
        border: Border.all(
          color: _focusNode.hasFocus
              ? colors.orange.primary
              : colors.neutral.shade300,
          width: _focusNode.hasFocus ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(corners.md),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.enabled ? _showDateTimePicker : null,
          borderRadius: BorderRadius.circular(corners.md),
          focusNode: _focusNode,
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: density.horizontalPadding, // Semantic horizontal padding
              vertical: density.verticalPadding,     // Semantic vertical padding
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    displayText,
                    style: TextStyle(
                      color: hasValue
                          ? colors.onSurface
                          : colors.neutral.shade500,
                      fontSize: 14, // Match VInput font size
                      height: 1.2, // Add line height for better text positioning
                    ),
                  ),
                ),
                if (hasValue && widget.showClearButton && widget.enabled) ...[
                  SizedBox(width: density.gap), // Semantic gap
                  _buildClearButton(colors, corners),
                  SizedBox(width: density.smallGap), // Semantic small gap
                ],
                Icon(
                  Icons.event_available,
                  color: colors.neutral.shade500,
                  size: density.iconSize, // Semantic icon size
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSeparateFields(VColors colors, VCorners corners, VComponentDensity density, VMotion motion) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: _buildDateField(colors, corners, density, motion),
        ),
        SizedBox(width: density.gap), // Semantic gap between date and time fields
        Expanded(
          child: _buildTimeField(colors, corners, density, motion),
        ),
      ],
    );
  }

  Widget _buildDateField(VColors colors, VCorners corners, VComponentDensity density, VMotion motion) {
    final hasValue = widget.value != null;
    final displayText = hasValue
        ? _formatDate(widget.value!)
        : widget.datePlaceholder;

    return AnimatedContainer(
      duration: motion.duration,
      curve: motion.curve,
      height: density.height, // Use semantic height directly
      decoration: BoxDecoration(
        color: widget.enabled ? colors.surface : colors.neutral.shade100,
        border: Border.all(
          color: colors.neutral.shade300,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(corners.md),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.enabled ? _showDatePicker : null,
          borderRadius: BorderRadius.circular(corners.md),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: density.horizontalPadding, // Semantic horizontal padding
              vertical: density.verticalPadding,     // Semantic vertical padding
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    displayText,
                    style: TextStyle(
                      color: hasValue
                          ? colors.onSurface
                          : colors.neutral.shade500,
                      fontSize: 14, // Match VInput font size
                      height: 1.2, // Add line height for better text positioning
                    ),
                  ),
                ),
                Icon(
                  Icons.calendar_today,
                  color: colors.neutral.shade500,
                  size: density.iconSize, // Semantic icon size
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTimeField(VColors colors, VCorners corners, VComponentDensity density, VMotion motion) {
    final hasValue = widget.value != null;
    final displayText = hasValue
        ? _formatTime(TimeOfDay.fromDateTime(widget.value!))
        : widget.timePlaceholder;

    return AnimatedContainer(
      duration: motion.duration,
      curve: motion.curve,
      height: density.height, // Use semantic height directly
      decoration: BoxDecoration(
        color: widget.enabled ? colors.surface : colors.neutral.shade100,
        border: Border.all(
          color: colors.neutral.shade300,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(corners.md),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.enabled ? _showTimePicker : null,
          borderRadius: BorderRadius.circular(corners.md),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: density.horizontalPadding, // Semantic horizontal padding
              vertical: density.verticalPadding,     // Semantic vertical padding
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    displayText,
                    style: TextStyle(
                      color: hasValue
                          ? colors.onSurface
                          : colors.neutral.shade500,
                      fontSize: 14, // Match VInput font size
                      height: 1.2, // Add line height for better text positioning
                    ),
                  ),
                ),
                Icon(
                  Icons.access_time,
                  color: colors.neutral.shade500,
                  size: density.iconSize, // Semantic icon size
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildClearButton(VColors colors, VCorners corners) {
    final density = getDensity();
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => widget.onChanged?.call(null),
        borderRadius: BorderRadius.circular(corners.sm),
        child: Padding(
          padding: EdgeInsets.all(density.smallGap), // Semantic small gap padding
          child: Icon(
            Icons.clear,
            color: colors.neutral.shade500,
            size: density.smallIconSize, // Semantic small icon size
          ),
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    if (widget.dateTimeFormat != null) {
      return widget.dateTimeFormat!(dateTime);
    }

    // Default format: MMM dd, yyyy at h:mm AM/PM (e.g., "Jan 15, 2024 at 2:30 PM")
    final dateStr = _formatDate(dateTime);
    final timeStr = _formatTime(TimeOfDay.fromDateTime(dateTime));
    return '$dateStr at $timeStr';
  }

  String _formatDate(DateTime date) {
    // Default format: MMM dd, yyyy (e.g., "Jan 15, 2024")
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];

    return '${months[date.month - 1]} ${date.day.toString().padLeft(2, '0')}, ${date.year}';
  }

  String _formatTime(TimeOfDay time) {
    if (widget.timeFormat == VTimeFormat.hour24) {
      // 24-hour format: HH:mm (e.g., "14:30")
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    } else {
      // 12-hour format: h:mm AM/PM (e.g., "2:30 PM")
      final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
      final period = time.period == DayPeriod.am ? 'AM' : 'PM';
      return '$hour:${time.minute.toString().padLeft(2, '0')} $period';
    }
  }

  Future<void> _showDateTimePicker() async {
    final colors = getColors();

    // First show date picker
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: widget.value ?? DateTime.now(),
      firstDate: widget.firstDate ?? DateTime(1900),
      lastDate: widget.lastDate ?? DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: colors.orange.primary,
              onPrimary: colors.onPrimary,
              secondary: colors.orange.primary,
              onSecondary: colors.onPrimary,
              surface: colors.surface,
              onSurface: colors.onSurface,
              surfaceTint: Colors.transparent,
              primaryContainer: colors.orange.primary.withValues(alpha: 0.1),
              onPrimaryContainer: colors.onSurface,
            ),
            datePickerTheme: DatePickerThemeData(
              backgroundColor: colors.surface,
              surfaceTintColor: Colors.transparent,
              headerBackgroundColor: colors.surface,
              headerForegroundColor: colors.onSurface,
              weekdayStyle: TextStyle(color: colors.onSurface),
              dayStyle: TextStyle(color: colors.onSurface),
              yearStyle: TextStyle(color: colors.onSurface),
              dayBackgroundColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return colors.orange.primary;
                }
                return null;
              }),
              dayOverlayColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return colors.orange.primary;
                }
                if (states.contains(WidgetState.hovered)) {
                  return colors.orange.primary.withValues(alpha: 0.1);
                }
                return null;
              }),
              dayForegroundColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return colors.onPrimary;
                }
                return colors.onSurface;
              }),
              todayForegroundColor: WidgetStateProperty.all(colors.orange.primary),
              todayBackgroundColor: WidgetStateProperty.all(Colors.transparent),
              todayBorder: BorderSide(color: colors.orange.primary),
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: colors.orange.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate == null) return;
    if (!mounted) return;

    // Then show time picker
    final selectedTime = await showTimePicker(
      context: context,
      initialTime: widget.value != null
          ? TimeOfDay.fromDateTime(widget.value!)
          : TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: colors.orange.primary,
              onPrimary: colors.onPrimary,
              secondary: colors.orange.primary,
              onSecondary: colors.onPrimary,
              surface: colors.surface,
              onSurface: colors.onSurface,
              surfaceTint: Colors.transparent,
              primaryContainer: colors.orange.primary.withValues(alpha: 0.1),
              onPrimaryContainer: colors.onSurface,
            ),
            timePickerTheme: TimePickerThemeData(
              backgroundColor: colors.surface,
              hourMinuteTextColor: colors.onSurface,
              hourMinuteColor: colors.orange.primary.withValues(alpha: 0.1),
              dayPeriodTextColor: colors.onSurface,
              dayPeriodColor: colors.orange.primary.withValues(alpha: 0.1),
              dayPeriodBorderSide: BorderSide(color: colors.orange.primary),
              dialHandColor: colors.orange.primary,
              dialBackgroundColor: colors.orange.primary.withValues(alpha: 0.1),
              dialTextColor: colors.onSurface,
              entryModeIconColor: colors.orange.primary,
              helpTextStyle: TextStyle(
                color: colors.onSurface,
              ),
              hourMinuteTextStyle: TextStyle(
                color: colors.onSurface,
                fontSize: 24,
                fontWeight: FontWeight.w400,
              ),
              dayPeriodTextStyle: TextStyle(
                color: colors.onSurface,
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: colors.orange.primary,
              ),
            ),
          ),
          child: MediaQuery(
            data: MediaQuery.of(context).copyWith(
              alwaysUse24HourFormat: widget.timeFormat == VTimeFormat.hour24,
            ),
            child: child!,
          ),
        );
      },
    );

    if (selectedTime != null) {
      final dateTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        selectedTime.hour,
        selectedTime.minute,
      );
      widget.onChanged?.call(dateTime);
    }
  }

  Future<void> _showDatePicker() async {
    final colors = getColors();

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: widget.value ?? DateTime.now(),
      firstDate: widget.firstDate ?? DateTime(1900),
      lastDate: widget.lastDate ?? DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: colors.orange.primary,
              onPrimary: colors.onPrimary,
              secondary: colors.orange.primary,
              onSecondary: colors.onPrimary,
              surface: colors.surface,
              onSurface: colors.onSurface,
              surfaceTint: Colors.transparent,
              primaryContainer: colors.orange.primary.withValues(alpha: 0.1),
              onPrimaryContainer: colors.onSurface,
            ),
            datePickerTheme: DatePickerThemeData(
              backgroundColor: colors.surface,
              surfaceTintColor: Colors.transparent,
              headerBackgroundColor: colors.surface,
              headerForegroundColor: colors.onSurface,
              weekdayStyle: TextStyle(color: colors.onSurface),
              dayStyle: TextStyle(color: colors.onSurface),
              yearStyle: TextStyle(color: colors.onSurface),
              dayBackgroundColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return colors.orange.primary;
                }
                return null;
              }),
              dayOverlayColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return colors.orange.primary;
                }
                if (states.contains(WidgetState.hovered)) {
                  return colors.orange.primary.withValues(alpha: 0.1);
                }
                return null;
              }),
              dayForegroundColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return colors.onPrimary;
                }
                return colors.onSurface;
              }),
              todayForegroundColor: WidgetStateProperty.all(colors.orange.primary),
              todayBackgroundColor: WidgetStateProperty.all(Colors.transparent),
              todayBorder: BorderSide(color: colors.orange.primary),
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: colors.orange.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null) {
      final currentTime = widget.value != null
          ? TimeOfDay.fromDateTime(widget.value!)
          : TimeOfDay.now();

      final dateTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        currentTime.hour,
        currentTime.minute,
      );
      widget.onChanged?.call(dateTime);
    }
  }

  Future<void> _showTimePicker() async {
    final colors = getColors();

    final selectedTime = await showTimePicker(
      context: context,
      initialTime: widget.value != null
          ? TimeOfDay.fromDateTime(widget.value!)
          : TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: colors.orange.primary,
              onPrimary: colors.onPrimary,
              secondary: colors.orange.primary,
              onSecondary: colors.onPrimary,
              surface: colors.surface,
              onSurface: colors.onSurface,
              surfaceTint: Colors.transparent,
              primaryContainer: colors.orange.primary.withValues(alpha: 0.1),
              onPrimaryContainer: colors.onSurface,
            ),
            timePickerTheme: TimePickerThemeData(
              backgroundColor: colors.surface,
              hourMinuteTextColor: colors.onSurface,
              hourMinuteColor: colors.orange.primary.withValues(alpha: 0.1),
              dayPeriodTextColor: colors.onSurface,
              dayPeriodColor: colors.orange.primary.withValues(alpha: 0.1),
              dayPeriodBorderSide: BorderSide(color: colors.orange.primary),
              dialHandColor: colors.orange.primary,
              dialBackgroundColor: colors.orange.primary.withValues(alpha: 0.1),
              dialTextColor: colors.onSurface,
              entryModeIconColor: colors.orange.primary,
              helpTextStyle: TextStyle(
                color: colors.onSurface,
              ),
              hourMinuteTextStyle: TextStyle(
                color: colors.onSurface,
                fontSize: 24,
                fontWeight: FontWeight.w400,
              ),
              dayPeriodTextStyle: TextStyle(
                color: colors.onSurface,
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: colors.orange.primary,
              ),
            ),
          ),
          child: MediaQuery(
            data: MediaQuery.of(context).copyWith(
              alwaysUse24HourFormat: widget.timeFormat == VTimeFormat.hour24,
            ),
            child: child!,
          ),
        );
      },
    );

    if (selectedTime != null) {
      final currentDate = widget.value ?? DateTime.now();

      final dateTime = DateTime(
        currentDate.year,
        currentDate.month,
        currentDate.day,
        selectedTime.hour,
        selectedTime.minute,
      );
      widget.onChanged?.call(dateTime);
    }
  }
}
