import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import '../theme/v_theme_data.dart';
import '../theme/v_theme.dart';

/// Column definition for VDataTable
class VDataColumn<T> {
  const VDataColumn({
    required this.key,
    required this.label,
    this.sortable = true,
    this.filterable = false,
    this.width,
    this.minWidth,
    this.maxWidth,
    this.alignment = Alignment.centerLeft,
    this.headerAlignment,
    this.onSort,
    this.builder,
    this.getValue,
  });

  /// Unique key for the column
  final String key;

  /// Display label for the column header
  final String label;

  /// Whether this column can be sorted
  final bool sortable;

  /// Whether this column can be filtered
  final bool filterable;

  /// Fixed width for the column
  final double? width;

  /// Minimum width for the column
  final double? minWidth;

  /// Maximum width for the column
  final double? maxWidth;

  /// Alignment for cell content
  final Alignment alignment;

  /// Alignment for header content (defaults to alignment if null)
  final Alignment? headerAlignment;

  /// Callback for sorting this column
  final VoidCallback? onSort;

  /// Custom builder for cell content
  final Widget Function(BuildContext context, T item, int index)? builder;

  /// Function to extract the value for sorting/filtering
  final dynamic Function(T item)? getValue;

  Alignment get effectiveHeaderAlignment => headerAlignment ?? alignment;
}

/// Sort direction for table columns
enum VSortDirection { ascending, descending }

/// Sort state for a column
class VColumnSort {
  const VColumnSort({
    required this.columnKey,
    required this.direction,
  });

  final String columnKey;
  final VSortDirection direction;
}

/// Row selection state
class VRowSelection<T> {
  const VRowSelection({
    this.selectedItems = const [],
    this.isAllSelected = false,
    this.isIndeterminate = false,
  });

  final List<T> selectedItems;
  final bool isAllSelected;
  final bool isIndeterminate;

  VRowSelection<T> copyWith({
    List<T>? selectedItems,
    bool? isAllSelected,
    bool? isIndeterminate,
  }) {
    return VRowSelection<T>(
      selectedItems: selectedItems ?? this.selectedItems,
      isAllSelected: isAllSelected ?? this.isAllSelected,
      isIndeterminate: isIndeterminate ?? this.isIndeterminate,
    );
  }
}

/// A comprehensive data table component with sorting, filtering, pagination, and selection
class VDataTable<T> extends VWidget {
  const VDataTable({
    super.key,
    required this.columns,
    required this.data,
    this.selectable = false,
    this.sortable = true,
    this.filterable = false,
    this.searchQuery,
    this.searchFunction,
    this.paginated = false,
    this.pageSize = 10,
    this.currentPage = 0,
    this.totalItems,
    this.loading = false,
    this.emptyBuilder,
    this.loadingBuilder,
    this.headerHeight,
    this.rowHeight,
    this.horizontalMargin,
    this.columnSpacing,
    this.showBorders = true,
    this.alternateRowColors = true,
    this.currentSort,
    this.selection,
    this.onSort,
    this.onSelectionChanged,
    this.onPageChanged,
    this.onRowTap,
    this.getRowKey,
  });

  /// Column definitions for the table
  final List<VDataColumn<T>> columns;

  /// Data items to display
  final List<T> data;

  /// Whether rows can be selected
  final bool selectable;

  /// Whether columns can be sorted
  final bool sortable;

  /// Whether columns can be filtered
  final bool filterable;

  /// Search query for filtering data
  final String? searchQuery;

  /// Custom search function (if null, uses default search on getValue results)
  final bool Function(T item, String query)? searchFunction;

  /// Whether to show pagination controls
  final bool paginated;

  /// Number of items per page
  final int pageSize;

  /// Current page index (0-based)
  final int currentPage;

  /// Total number of items (for server-side pagination)
  final int? totalItems;

  /// Whether the table is in loading state
  final bool loading;

  /// Builder for empty state
  final Widget Function(BuildContext context)? emptyBuilder;

  /// Builder for loading state
  final Widget Function(BuildContext context)? loadingBuilder;

  /// Height of the header row
  final double? headerHeight;

  /// Height of data rows
  final double? rowHeight;

  /// Horizontal margin around the table
  final double? horizontalMargin;

  /// Spacing between columns
  final double? columnSpacing;

  /// Whether to show table borders
  final bool showBorders;

  /// Whether to alternate row colors
  final bool alternateRowColors;

  /// Current sort state
  final VColumnSort? currentSort;

  /// Current selection state
  final VRowSelection<T>? selection;

  /// Callback when a column is sorted
  final void Function(VColumnSort sort)? onSort;

  /// Callback when selection changes
  final void Function(VRowSelection<T> selection)? onSelectionChanged;

  /// Callback when page changes
  final void Function(int page)? onPageChanged;

  /// Callback when a row is tapped
  final void Function(T item, int index)? onRowTap;

  /// Function to get a unique key for each row
  final String Function(T item)? getRowKey;

  @override
  Widget build(BuildContext context) {
    final theme = getTheme(context);
    final colors = theme.colors;
    final corners = theme.corners;
    final density = theme.componentDensity;
    final motion = theme.motion;
    
    // Apply search filtering
    List<T> filteredData = data;
    if (searchQuery != null && searchQuery!.isNotEmpty) {
      filteredData = data.where((item) {
        if (searchFunction != null) {
          return searchFunction!(item, searchQuery!);
        }
        
        // Default search: check all column values
        return columns.any((column) {
          final value = column.getValue?.call(item);
          return value?.toString().toLowerCase().contains(searchQuery!.toLowerCase()) ?? false;
        });
      }).toList();
    }
    
    if (loading) {
      return _buildLoading(context, colors, corners, density);
    }

    if (filteredData.isEmpty) {
      return _buildEmpty(context, colors, corners, density);
    }

    return _buildTable(context, colors, corners, density, motion, filteredData);
  }

  Widget _buildLoading(BuildContext context, VColors colors, VCorners corners, VComponentDensity density) {
    if (loadingBuilder != null) {
      return loadingBuilder!(context);
    }

    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: colors.surface,
        borderRadius: BorderRadius.circular(corners.md),
        border: showBorders
            ? Border.all(color: colors.onSurface.withValues(alpha: 0.3))
            : null,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(colors.primary),
            ),
            SizedBox(height: density.verticalPadding),
            Text(
              'Loading...',
              style: TextStyle(
                color: colors.onSurface.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmpty(BuildContext context, VColors colors, VCorners corners, VComponentDensity density) {
    if (emptyBuilder != null) {
      return emptyBuilder!(context);
    }

    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: colors.surface,
        borderRadius: BorderRadius.circular(corners.md),
        border: showBorders
            ? Border.all(color: colors.onSurface.withValues(alpha: 0.3))
            : null,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.table_view,
              size: 48,
              color: colors.onSurface.withOpacity(0.3),
            ),
            SizedBox(height: density.verticalPadding),
            Text(
              'No data available',
              style: TextStyle(
                color: colors.onSurface.withOpacity(0.7),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: density.smallGap),
            Text(
              'Add some data to see it displayed here',
              style: TextStyle(
                color: colors.onSurface.withOpacity(0.5),
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTable(BuildContext context, VColors colors, VCorners corners, VComponentDensity density, VMotion motion, List<T> filteredData) {
    return Container(
      decoration: BoxDecoration(
        color: colors.surface,
        borderRadius: BorderRadius.circular(corners.md),
        border: showBorders
            ? Border.all(color: colors.onSurface.withValues(alpha: 0.3))
            : null,
      ),
      child: Column(
        children: [
          _buildHeader(context, colors, corners, density, filteredData),
          Expanded(
            child: _buildBody(context, colors, density, filteredData),
          ),
          if (paginated) _buildPagination(context, colors, corners, density, filteredData),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, VColors colors, VCorners corners, VComponentDensity density, List<T> filteredData) {
    final effectiveHeaderHeight = headerHeight ?? density.height * 1.2;
    
    return Container(
      height: effectiveHeaderHeight,
      decoration: BoxDecoration(
        color: colors.surfaceVariant,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(corners.md),
          topRight: Radius.circular(corners.md),
        ),
        border: showBorders
            ? Border(
                bottom: BorderSide(color: colors.onSurface.withValues(alpha: 0.3)),
              )
            : null,
      ),
      child: Row(
        children: [
          if (selectable) _buildSelectAllCell(context, colors, density, filteredData),
          ...columns.map((column) => _buildHeaderCell(context, colors, density, column)),
        ],
      ),
    );
  }

  Widget _buildSelectAllCell(BuildContext context, VColors colors, VComponentDensity density, List<T> filteredData) {
    final isAllSelected = selection?.isAllSelected ?? false;
    final isIndeterminate = selection?.isIndeterminate ?? false;
    
    return Container(
      width: 60,
      padding: EdgeInsets.symmetric(horizontal: density.horizontalPadding),
      child: Checkbox(
        value: isIndeterminate ? null : isAllSelected,
        tristate: true,
        onChanged: (value) => _handleSelectAll(value, filteredData),
        activeColor: colors.primary,
      ),
    );
  }

  Widget _buildHeaderCell(BuildContext context, VColors colors, VComponentDensity density, VDataColumn<T> column) {
    final isSorted = currentSort?.columnKey == column.key;
    final sortDirection = currentSort?.direction;
    
    return Expanded(
      flex: column.width?.toInt() ?? 1,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: density.horizontalPadding),
        child: InkWell(
          onTap: column.sortable && sortable ? () => _handleSort(column) : null,
          child: Row(
            mainAxisAlignment: _getMainAxisAlignment(column.effectiveHeaderAlignment),
            children: [
              Flexible(
                child: Text(
                  column.label,
                  style: TextStyle(
                    color: colors.onSurface,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (column.sortable && sortable) ...[
                SizedBox(width: density.smallGap),
                Icon(
                  isSorted
                      ? (sortDirection == VSortDirection.ascending
                          ? Icons.arrow_upward
                          : Icons.arrow_downward)
                      : Icons.unfold_more,
                  size: 16,
                  color: isSorted
                      ? colors.primary
                      : colors.onSurface.withOpacity(0.5),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBody(BuildContext context, VColors colors, VComponentDensity density, List<T> filteredData) {
    // Get the data slice for current page if pagination is enabled
    List<T> displayData = filteredData;
    if (paginated) {
      final startIndex = currentPage * pageSize;
      final endIndex = (startIndex + pageSize).clamp(0, filteredData.length);
      displayData = filteredData.sublist(startIndex, endIndex);
    }
    
    return ListView.builder(
      itemCount: displayData.length,
      itemBuilder: (context, index) {
        // Use the filtered data index for row callbacks, but display data for rendering
        final actualIndex = paginated ? (currentPage * pageSize) + index : index;
        return _buildRow(context, colors, density, displayData[index], index, actualIndex, filteredData);
      },
    );
  }

  Widget _buildRow(BuildContext context, VColors colors, VComponentDensity density, T item, int displayIndex, int actualIndex, List<T> filteredData) {
    final effectiveRowHeight = rowHeight ?? density.height;
    final isSelected = selection?.selectedItems.contains(item) ?? false;
    final isEvenRow = displayIndex % 2 == 0;
    
    Color rowColor = colors.surface;
    if (isSelected) {
      rowColor = colors.primary.withOpacity(0.1);
    } else if (alternateRowColors && !isEvenRow) {
      // Use a distinct alternate row color
      rowColor = colors.onSurface.withOpacity(0.06);
    }
    
    return InkWell(
      onTap: () => _handleRowTap(item, actualIndex),
      child: Container(
        height: effectiveRowHeight,
        color: rowColor,
        child: Row(
          children: [
            if (selectable) _buildSelectCell(context, colors, density, item, filteredData),
            ...columns.map((column) => _buildDataCell(context, colors, density, column, item, actualIndex)),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectCell(BuildContext context, VColors colors, VComponentDensity density, T item, List<T> filteredData) {
    final isSelected = selection?.selectedItems.contains(item) ?? false;
    
    return Container(
      width: 60,
      padding: EdgeInsets.symmetric(horizontal: density.horizontalPadding),
      child: Checkbox(
        value: isSelected,
        onChanged: (value) => _handleItemSelect(item, value ?? false, filteredData),
        activeColor: colors.primary,
      ),
    );
  }

  Widget _buildDataCell(BuildContext context, VColors colors, VComponentDensity density, VDataColumn<T> column, T item, int index) {
    Widget content;
    
    if (column.builder != null) {
      content = column.builder!(context, item, index);
    } else {
      final value = column.getValue?.call(item) ?? '';
      content = Text(
        value.toString(),
        style: TextStyle(
          color: colors.onSurface,
          fontSize: 14,
        ),
        overflow: TextOverflow.ellipsis,
      );
    }
    
    return Expanded(
      flex: column.width?.toInt() ?? 1,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: density.horizontalPadding),
        child: Align(
          alignment: column.alignment,
          child: content,
        ),
      ),
    );
  }

  Widget _buildPagination(BuildContext context, VColors colors, VCorners corners, VComponentDensity density, List<T> filteredData) {
    final totalPages = ((totalItems ?? filteredData.length) / pageSize).ceil();
    
    return Container(
      height: 60,
      padding: EdgeInsets.symmetric(horizontal: density.horizontalPadding),
      decoration: BoxDecoration(
        color: colors.surfaceVariant,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(corners.md),
          bottomRight: Radius.circular(corners.md),
        ),
        border: showBorders
            ? Border(
                top: BorderSide(color: colors.onSurface.withValues(alpha: 0.3)),
              )
            : null,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Showing ${currentPage * pageSize + 1}-${((currentPage + 1) * pageSize).clamp(0, totalItems ?? filteredData.length)} of ${totalItems ?? filteredData.length}',
            style: TextStyle(
              color: colors.onSurface.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed: currentPage > 0 ? () => _handlePageChange(currentPage - 1) : null,
                icon: Icon(Icons.chevron_left),
                color: colors.onSurface,
              ),
              Text(
                '${currentPage + 1} of $totalPages',
                style: TextStyle(
                  color: colors.onSurface,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              IconButton(
                onPressed: currentPage < totalPages - 1 ? () => _handlePageChange(currentPage + 1) : null,
                icon: Icon(Icons.chevron_right),
                color: colors.onSurface,
              ),
            ],
          ),
        ],
      ),
    );
  }

  MainAxisAlignment _getMainAxisAlignment(Alignment alignment) {
    if (alignment == Alignment.centerLeft) return MainAxisAlignment.start;
    if (alignment == Alignment.centerRight) return MainAxisAlignment.end;
    return MainAxisAlignment.center;
  }

  void _handleSort(VDataColumn<T> column) {
    if (onSort == null) return;
    
    VSortDirection newDirection = VSortDirection.ascending;
    if (currentSort?.columnKey == column.key) {
      newDirection = currentSort!.direction == VSortDirection.ascending
          ? VSortDirection.descending
          : VSortDirection.ascending;
    }
    
    onSort!(VColumnSort(columnKey: column.key, direction: newDirection));
  }

  void _handleSelectAll(bool? value, List<T> filteredData) {
    if (onSelectionChanged == null) return;
    
    if (value == true) {
      onSelectionChanged!(VRowSelection<T>(
        selectedItems: List.from(filteredData),
        isAllSelected: true,
        isIndeterminate: false,
      ));
    } else {
      onSelectionChanged!(VRowSelection<T>(
        selectedItems: [],
        isAllSelected: false,
        isIndeterminate: false,
      ));
    }
  }

  void _handleItemSelect(T item, bool selected, List<T> filteredData) {
    if (onSelectionChanged == null) return;
    
    final currentSelection = selection?.selectedItems ?? <T>[];
    List<T> newSelection;
    
    if (selectable) {
      // Multi-select behavior when selectable is enabled
      if (selected) {
        newSelection = [...currentSelection, item];
      } else {
        newSelection = currentSelection.where((i) => i != item).toList();
      }
    } else {
      // Selection disabled
      return;
    }
    
    final isAllSelected = newSelection.length == filteredData.length && 
                         filteredData.every((item) => newSelection.contains(item));
    final isIndeterminate = newSelection.isNotEmpty && !isAllSelected;
    
    onSelectionChanged!(VRowSelection<T>(
      selectedItems: newSelection,
      isAllSelected: isAllSelected,
      isIndeterminate: isIndeterminate,
    ));
  }

  void _handleRowTap(T item, int index) {
    onRowTap?.call(item, index);
  }

  void _handlePageChange(int page) {
    onPageChanged?.call(page);
  }
}
