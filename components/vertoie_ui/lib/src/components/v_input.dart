import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../base/v_widget.dart';

/// Input types supported by VInput
enum VInputType {
  text,
  email,
  phone,
  currency,
  number,
}

/// A themeable input widget that follows Vertoie design system.
/// Matches button density and uses brand orange colors.
class VInput extends VWidget {
  const VInput({
    super.key,
    this.controller,
    this.placeholder,
    this.onChanged,
    this.validator,
    this.enabled = true,
    this.type = VInputType.text,
  });

  final TextEditingController? controller;
  final String? placeholder;
  final ValueChanged<String>? onChanged;
  final String? Function(String?)? validator;
  final bool enabled;
  final VInputType type;

  /// Get keyboard type based on input type
  TextInputType get _keyboardType {
    switch (type) {
      case VInputType.email:
        return TextInputType.emailAddress;
      case VInputType.phone:
        return TextInputType.phone;
      case VInputType.number:
      case VInputType.currency:
        return TextInputType.number;
      case VInputType.text:
      default:
        return TextInputType.text;
    }
  }

  /// Get input formatters based on input type
  List<TextInputFormatter> get _inputFormatters {
    switch (type) {
      case VInputType.phone:
        return [FilteringTextInputFormatter.digitsOnly];
      case VInputType.currency:
        return [FilteringTextInputFormatter.allow(RegExp('[0-9.]'))];
      case VInputType.number:
        return [FilteringTextInputFormatter.allow(RegExp('[0-9.-]'))];
      case VInputType.email:
      case VInputType.text:
        return [];
    }
  }

  /// Get built-in validator based on input type
  String? Function(String?)? get _builtInValidator {
    switch (type) {
      case VInputType.email:
        return (value) {
          if (value == null || value.isEmpty) return null;
          final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
          if (!emailRegex.hasMatch(value)) {
            return 'Please enter a valid email address';
          }
          return null;
        };
      case VInputType.phone:
        return (value) {
          if (value == null || value.isEmpty) return null;
          if (value.length < 10) {
            return 'Please enter a valid phone number';
          }
          return null;
        };
      case VInputType.number:
        return (value) {
          if (value == null || value.isEmpty) return null;
          if (double.tryParse(value) == null) {
            return 'Please enter a valid number';
          }
          return null;
        };
      case VInputType.currency:
        return (value) {
          if (value == null || value.isEmpty) return null;
          if (double.tryParse(value) == null) {
            return 'Please enter a valid amount';
          }
          return null;
        };
      case VInputType.text:
        return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final colors = getColors(context);
    final corners = getCorners(context);
    final density = getDensity(context);

    // Calculate height and padding to match medium button density
    final inputHeight = density.height * 1.05; // Same as medium button
    final horizontalPadding = density.horizontalPadding;
    final verticalPadding = density.verticalPadding;

    // Calculate content padding to achieve target height
    final contentPadding = EdgeInsets.symmetric(
      horizontal: horizontalPadding,
      vertical: verticalPadding,
    );

    // Define border colors with proper dark mode support
    final borderColor = colors.onSurface.withValues(alpha: 0.3);
    final focusedBorderColor = colors.primary; 
    final disabledBorderColor = colors.onSurface.withValues(alpha: 0.1);
    final errorBorderColor = colors.error;

    // Define text colors for dark mode support
    final textColor = colors.onSurface;
    final hintColor = colors.onSurface.withValues(alpha: 0.6);
    final disabledTextColor = colors.onSurface.withValues(alpha: 0.4);

    return SizedBox(
      height: inputHeight,
      child: TextFormField(
        controller: controller,
        onChanged: onChanged,
        validator: validator ?? _builtInValidator,
        enabled: enabled,
        keyboardType: _keyboardType,
        inputFormatters: _inputFormatters,
        style: TextStyle(
          color: enabled ? textColor : disabledTextColor,
          fontSize: 14,
        ),
        decoration: InputDecoration(
          hintText: placeholder,
          hintStyle: TextStyle(
            color: hintColor,
            fontSize: 14,
          ),
          filled: true,
          fillColor: enabled ? colors.surface : colors.surfaceVariant,
          contentPadding: contentPadding,

          // Default border (unfocused)
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(corners.md),
            borderSide: BorderSide(
              color: borderColor,
              width: 1,
            ),
          ),

          // Focused border (orange)
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(corners.md),
            borderSide: BorderSide(
              color: focusedBorderColor,
              width: 2,
            ),
          ),

          // Disabled border
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(corners.md),
            borderSide: BorderSide(
              color: disabledBorderColor,
              width: 1,
            ),
          ),

          // Error border
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(corners.md),
            borderSide: BorderSide(
              color: errorBorderColor,
              width: 1,
            ),
          ),

          // Focused error border
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(corners.md),
            borderSide: BorderSide(
              color: errorBorderColor,
              width: 2,
            ),
          ),
        ),
      ),
    );
  }
}
