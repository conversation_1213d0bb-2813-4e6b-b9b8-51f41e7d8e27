import 'package:flutter/material.dart';
import '../base/v_widget.dart';

/// A themeable card widget that follows Vertoie design system.
/// This is a placeholder implementation.
class VCard extends VWidget {
  const VCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
  });

  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    final colors = getColors(context);
    final density = getDensity(context);
    final corners = getCorners(context);
    final elevation = getElevation(context);
    
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: colors.surface,
        borderRadius: BorderRadius.circular(corners.md),
        boxShadow: elevation.boxShadow,
      ),
      child: Padding(
        padding: padding ?? EdgeInsets.all(density.horizontalPadding),
        child: child,
      ),
    );
  }
}
