import 'package:flutter/material.dart';
import '../base/v_widget.dart';

/// A themeable container widget that follows Vertoie design system.
/// This is a placeholder implementation.
class VContainer extends VWidget {
  const VContainer({
    super.key,
    this.child,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.color,
  });

  final Widget? child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    final colors = getColors(context);
    final density = getDensity(context);
    
    return Container(
      width: width,
      height: height,
      padding: padding ?? EdgeInsets.all(density.horizontalPadding),
      margin: margin,
      decoration: BoxDecoration(
        color: color ?? colors.surface,
      ),
      child: child,
    );
  }
}
