import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import '../theme/v_theme.dart';
import '../theme/v_theme_data.dart';
import 'v_button.dart';
import 'v_input.dart';
import 'v_select.dart';
import 'v_checkbox.dart';
import 'v_date_picker.dart';
import 'v_time_picker.dart';
import 'v_text_area.dart';
import 'v_calendar.dart';

/// Event management operations
enum VEventOperation {
  create,
  update,
  delete,
  duplicate,
}

/// Result from event modal operations
class VEventModalResult {
  const VEventModalResult({
    required this.operation,
    this.event,
    this.originalEvent,
  });

  final VEventOperation operation;
  final VCalendarEvent? event;
  final VCalendarEvent? originalEvent;
}

/// Simple event modal for creating and editing calendar events
class VEventModal extends VStatefulWidget {
  const VEventModal({
    super.key,
    this.event,
    this.initialDate,
    this.availableClients = const [],
    this.onSave,
    this.onDelete,
  });

  /// Event to edit (null for creating new event)
  final VCalendarEvent? event;
  
  /// Initial date for new events
  final DateTime? initialDate;
  
  /// Available client options
  final List<String> availableClients;
  
  /// Called when event is saved or deleted
  final void Function(VEventModalResult)? onSave;
  
  /// Called when event is deleted
  final void Function(VEventModalResult)? onDelete;

  @override
  State<VEventModal> createState() => _VEventModalState();
}

class _VEventModalState extends VState<VEventModal> {
  late final TextEditingController _titleController;
  late final TextEditingController _descriptionController;
  late DateTime _selectedDate;
  TimeOfDay? _startTime;
  TimeOfDay? _endTime;
  bool _isAllDay = false;
  VEventStatus _status = VEventStatus.confirmed;
  VEventCategory _category = VEventCategory.appointment;
  String? _selectedClient;
  Color? _selectedColor;
  
  bool get _isEditing => widget.event != null;
  
  @override
  void initState() {
    super.initState();
    
    // Initialize form with event data or defaults
    final event = widget.event;
    _titleController = TextEditingController(text: event?.title ?? '');
    _descriptionController = TextEditingController(text: event?.description ?? '');
    _selectedDate = event?.date ?? widget.initialDate ?? DateTime.now();
    _startTime = event?.startTime;
    _endTime = event?.endTime;
    _isAllDay = event?.isAllDay ?? false;
    _status = event?.status ?? VEventStatus.confirmed;
    _category = event?.category ?? VEventCategory.appointment;
    _selectedClient = event?.clientId;
    _selectedColor = event?.color;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = getTheme();
    final colors = theme.colors;
    final corners = theme.corners;
    final density = theme.componentDensity;
    
    return Dialog(
      backgroundColor: colors.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(corners.lg),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 800),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(colors, corners, density),
            Flexible(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(density.lg),
                child: _buildForm(colors, corners, density),
              ),
            ),
            _buildFooter(colors, corners, density),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(VColors colors, VCorners corners, VComponentDensity density) {
    return Container(
      padding: EdgeInsets.all(density.lg),
      decoration: BoxDecoration(
        color: colors.neutral.shade50,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(corners.lg),
          topRight: Radius.circular(corners.lg),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _isEditing ? Icons.edit_calendar : Icons.add_circle_outline,
            color: colors.primary,
            size: 24,
          ),
          SizedBox(width: density.sm),
          Expanded(
            child: Text(
              _isEditing ? 'Edit Event' : 'Create Event',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: colors.onSurface,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(Icons.close, color: colors.onSurface),
          ),
        ],
      ),
    );
  }

  Widget _buildForm(VColors colors, VCorners corners, VComponentDensity density) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title field
        VInput(
          controller: _titleController,
          placeholder: 'Event title',
        ),
        SizedBox(height: density.md as double),

        // Description field
        VTextArea(
          controller: _descriptionController,
          placeholder: 'Event description',
          minLines: 3,
          maxLines: 5,
        ),
        SizedBox(height: density.md as double),

        // Date and time section
        _buildDateTimeSection(colors, corners, density),
        SizedBox(height: density.md as double),

        // Category and status
        Row(
          children: [
            Expanded(
              child: VSelect<VEventCategory>(
                value: _category,
                hint: 'Category',
                items: VEventCategory.values.map((category) => VSelectItem(
                  value: category,
                  label: _getCategoryLabel(category),
                )).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _category = value;
                    });
                  }
                },
              ),
            ),
            SizedBox(width: density.md as double),
            Expanded(
              child: VSelect<VEventStatus>(
                value: _status,
                hint: 'Status',
                items: VEventStatus.values.map((status) => VSelectItem(
                  value: status,
                  label: _getStatusLabel(status),
                )).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _status = value;
                    });
                  }
                },
              ),
            ),
          ],
        ),
        SizedBox(height: density.md as double),

        // Client selection
        if (widget.availableClients.isNotEmpty) ...[
          VSelect<String>(
            value: _selectedClient,
            hint: 'Select client',
            items: widget.availableClients.map((client) => VSelectItem(
              value: client,
              label: client,
            )).toList(),
            onChanged: (value) {
              setState(() {
                _selectedClient = value;
              });
            },
          ),
          SizedBox(height: density.md as double),
        ],

        // Color selection
        _buildColorSelection(colors, corners, density),
      ],
    );
  }

  Widget _buildDateTimeSection(dynamic colors, dynamic corners, dynamic density) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // All-day toggle
        Row(
          children: [
            VCheckbox(
              value: _isAllDay,
              onChanged: (value) {
                setState(() {
                  _isAllDay = value ?? false;
                  if (_isAllDay) {
                    _startTime = null;
                    _endTime = null;
                  }
                });
              },
            ),
            SizedBox(width: density.sm as double),
            Text(
              'All day event',
              style: TextStyle(
                fontSize: 14,
                color: colors.onSurface as Color,
              ),
            ),
          ],
        ),
        SizedBox(height: density.sm as double),

        // Date selection
        VDatePicker(
          value: _selectedDate,
          onChanged: (date) {
            if (date != null) {
              setState(() {
                _selectedDate = date;
              });
            }
          },
        ),

        // Time selection (if not all-day)
        if (!_isAllDay) ...[
          SizedBox(height: density.sm as double),
          Row(
            children: [
              Expanded(
                child: VTimePicker(
                  value: _startTime,
                  onChanged: (time) {
                    setState(() {
                      _startTime = time;
                    });
                  },
                ),
              ),
              SizedBox(width: density.md as double),
              Expanded(
                child: VTimePicker(
                  value: _endTime,
                  onChanged: (time) {
                    setState(() {
                      _endTime = time;
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildColorSelection(dynamic colors, dynamic corners, dynamic density) {
    final colorOptions = [
      colors.primary as Color,
      colors.orange.primary as Color,
      colors.amber.primary as Color,
      colors.error as Color,
      colors.success as Color,
      colors.info as Color,
      const Color(0xFF8B5CF6), // Purple
      const Color(0xFFEC4899), // Pink
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Event Color',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: colors.onSurface as Color,
          ),
        ),
        SizedBox(height: density.xs as double),
        Wrap(
          spacing: density.sm as double,
          children: colorOptions.map((color) {
            final isSelected = _selectedColor == color;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedColor = isSelected ? null : color;
                });
              },
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: isSelected ? Border.all(
                    color: colors.onSurface as Color,
                    width: 3,
                  ) : null,
                ),
                child: isSelected ? const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 16,
                ) : null,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildFooter(VColors colors, VCorners corners, VComponentDensity density) {
    return Container(
      padding: EdgeInsets.all(density.lg as double),
      decoration: BoxDecoration(
        color: colors.neutral.shade50 as Color,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(corners.lg as double),
          bottomRight: Radius.circular(corners.lg as double),
        ),
      ),
      child: Row(
        children: [
          // Delete button (for editing)
          if (_isEditing) ...[
            VButton(
              onPressed: _handleDelete,
              variant: VButtonVariant.outlined,
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.delete_outline, size: 16),
                  SizedBox(width: 4),
                  Text('Delete'),
                ],
              ),
            ),
            SizedBox(width: density.sm as double),
          ],

          const Spacer(),

          // Cancel button
          VButton(
            onPressed: () => Navigator.of(context).pop(),
            variant: VButtonVariant.text,
            child: const Text('Cancel'),
          ),
          SizedBox(width: density.sm as double),

          // Save button
          VButton(
            onPressed: _isFormValid() ? _handleSave : null,
            variant: VButtonVariant.primary,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(_isEditing ? Icons.save : Icons.add, size: 16),
                const SizedBox(width: 4),
                Text(_isEditing ? 'Update' : 'Create'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  bool _isFormValid() {
    return _titleController.text.trim().isNotEmpty;
  }

  void _handleSave() {
    final event = VCalendarEvent(
      id: widget.event?.id ?? 'event_${DateTime.now().millisecondsSinceEpoch}',
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim().isEmpty ? null : _descriptionController.text.trim(),
      date: _selectedDate,
      isAllDay: _isAllDay,
      startTime: _startTime,
      endTime: _endTime,
      status: _status,
      category: _category,
      clientId: _selectedClient,
      color: _selectedColor,
    );

    final result = VEventModalResult(
      operation: _isEditing ? VEventOperation.update : VEventOperation.create,
      event: event,
      originalEvent: widget.event,
    );

    widget.onSave?.call(result);
    Navigator.of(context).pop(result);
  }

  void _handleDelete() {
    final result = VEventModalResult(
      operation: VEventOperation.delete,
      originalEvent: widget.event,
    );

    widget.onDelete?.call(result);
    Navigator.of(context).pop(result);
  }

  String _getCategoryLabel(VEventCategory category) {
    switch (category) {
      case VEventCategory.appointment:
        return 'Appointment';
      case VEventCategory.meeting:
        return 'Meeting';
      case VEventCategory.personal:
        return 'Personal';
      case VEventCategory.work:
        return 'Work';
      case VEventCategory.holiday:
        return 'Holiday';
      case VEventCategory.travel:
        return 'Travel';
      case VEventCategory.custom:
        return 'Custom';
    }
  }

  String _getStatusLabel(VEventStatus status) {
    switch (status) {
      case VEventStatus.confirmed:
        return 'Confirmed';
      case VEventStatus.tentative:
        return 'Tentative';
      case VEventStatus.cancelled:
        return 'Cancelled';
      case VEventStatus.completed:
        return 'Completed';
      case VEventStatus.inProgress:
        return 'In Progress';
      case VEventStatus.overdue:
        return 'Overdue';
    }
  }
}
