import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import 'v_checkbox.dart';

/// A checkbox option for use with VCheckboxGroup
class VCheckboxOption<T> {
  const VCheckboxOption({
    required this.value,
    required this.label,
    this.enabled = true,
  });

  final T value;
  final String label;
  final bool enabled;
}

/// Layout direction for VCheckboxGroup
enum VCheckboxGroupDirection {
  vertical,
  horizontal,
}

/// A themeable checkbox group widget that manages multiple checkboxes.
/// Supports indeterminate state when partially selected.
class VCheckboxGroup<T> extends VWidget {
  const VCheckboxGroup({
    super.key,
    required this.options,
    required this.onChanged,
    this.selectedValues = const [],
    this.direction = VCheckboxGroupDirection.vertical,
    this.enabled = true,
    this.label,
    this.showSelectAll = false,
    this.selectAllLabel = 'Select All',
  });

  /// List of checkbox options to display
  final List<VCheckboxOption<T>> options;

  /// Called when the selection changes
  final ValueChanged<List<T>> onChanged;

  /// Currently selected values
  final List<T> selectedValues;

  /// Layout direction for the checkboxes
  final VCheckboxGroupDirection direction;

  /// Whether the entire group is enabled
  final bool enabled;

  /// Optional label for the group
  final String? label;

  /// Whether to show a "Select All" checkbox
  final bool showSelectAll;

  /// Label for the "Select All" checkbox
  final String selectAllLabel;

  /// Gets the state of the "Select All" checkbox
  bool? get _selectAllState {
    if (!showSelectAll) return null;
    
    final enabledOptions = options.where((option) => option.enabled).toList();
    final selectedEnabledValues = selectedValues
        .where((value) => enabledOptions.any((option) => option.value == value))
        .toList();

    if (selectedEnabledValues.isEmpty) {
      return false; // None selected
    } else if (selectedEnabledValues.length == enabledOptions.length) {
      return true; // All selected
    } else {
      return null; // Partially selected (indeterminate)
    }
  }

  @override
  Widget build(BuildContext context) {
    final density = getDensity(context);
    final colors = getColors(context);

    final children = <Widget>[];

    // Add group label if provided
    if (label != null) {
      children.add(
        Padding(
          padding: EdgeInsets.only(bottom: density.verticalPadding),
          child: Text(
            label!,
            style: TextStyle(
              color: colors.onSurface,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      );
    }

    // Add "Select All" checkbox if enabled
    if (showSelectAll) {
      children.add(
        VCheckbox(
          value: _selectAllState,
          tristate: true,
          enabled: enabled,
          label: selectAllLabel,
          onChanged: _handleSelectAllChanged,
        ),
      );
      
      if (direction == VCheckboxGroupDirection.vertical) {
        children.add(SizedBox(height: density.verticalPadding));
      } else {
        children.add(SizedBox(width: density.horizontalPadding));
      }
    }

    // Add individual checkboxes
    final checkboxWidgets = options.map((option) {
      return VCheckbox(
        value: selectedValues.contains(option.value),
        enabled: enabled && option.enabled,
        label: option.label,
        onChanged: (value) => _handleOptionChanged(option.value, value),
      );
    }).toList();

    if (direction == VCheckboxGroupDirection.vertical) {
      // Add checkboxes vertically with spacing
      for (int i = 0; i < checkboxWidgets.length; i++) {
        children.add(checkboxWidgets[i]);
        if (i < checkboxWidgets.length - 1) {
          children.add(SizedBox(height: density.verticalPadding));
        }
      }
      
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      );
    } else {
      // Add checkboxes horizontally with spacing
      final horizontalChildren = <Widget>[];
      for (int i = 0; i < checkboxWidgets.length; i++) {
        horizontalChildren.add(checkboxWidgets[i]);
        if (i < checkboxWidgets.length - 1) {
          horizontalChildren.add(SizedBox(width: density.horizontalPadding));
        }
      }
      
      children.add(
        Wrap(
          spacing: density.horizontalPadding,
          runSpacing: density.verticalPadding,
          children: horizontalChildren,
        ),
      );
      
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      );
    }
  }

  /// Handles changes to individual checkbox options
  void _handleOptionChanged(T value, bool? isSelected) {
    if (!enabled) return;

    final newSelectedValues = List<T>.from(selectedValues);
    
    if (isSelected == true) {
      if (!newSelectedValues.contains(value)) {
        newSelectedValues.add(value);
      }
    } else {
      newSelectedValues.remove(value);
    }
    
    onChanged(newSelectedValues);
  }

  /// Handles changes to the "Select All" checkbox
  void _handleSelectAllChanged(bool? isSelected) {
    if (!enabled || !showSelectAll) return;

    final enabledOptions = options.where((option) => option.enabled).toList();
    
    if (isSelected == true) {
      // Select all enabled options
      final allValues = enabledOptions.map((option) => option.value).toList();
      final newSelectedValues = List<T>.from(selectedValues);
      
      for (final value in allValues) {
        if (!newSelectedValues.contains(value)) {
          newSelectedValues.add(value);
        }
      }
      
      onChanged(newSelectedValues);
    } else {
      // Deselect all enabled options
      final enabledValues = enabledOptions.map((option) => option.value).toSet();
      final newSelectedValues = selectedValues
          .where((value) => !enabledValues.contains(value))
          .toList();
      
      onChanged(newSelectedValues);
    }
  }
}
