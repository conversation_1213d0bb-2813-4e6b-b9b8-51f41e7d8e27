import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../base/v_widget.dart';
import '../theme/v_theme_data.dart';

/// A themeable switch widget that follows Vertoie design system.
/// Supports iOS/Material adaptive styling and uses brand orange colors.
class VSwitch extends VWidget {
  const VSwitch({
    super.key,
    required this.value,
    required this.onChanged,
    this.label,
    this.enabled = true,
    this.adaptive = true,
  });

  /// Current switch state
  final bool value;

  /// Callback when switch state changes
  final ValueChanged<bool>? onChanged;

  /// Optional label text
  final String? label;

  /// Whether the switch is enabled
  final bool enabled;

  /// Whether to use platform-adaptive styling
  final bool adaptive;

  void _handleChanged(bool newValue) {
    if (enabled && onChanged != null) {
      onChanged!(newValue);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = getTheme(context);
    final colors = theme.colors;
    final density = theme.componentDensity;
    final motion = theme.motion;

    // Determine if we should use Cupertino style
    final useCupertino = adaptive && Theme.of(context).platform == TargetPlatform.iOS;

    Widget switchWidget;

    if (useCupertino) {
      switchWidget = CupertinoSwitch(
        value: value,
        onChanged: enabled ? _handleChanged : null,
        activeColor: colors.primary,
        trackColor: colors.onSurface.withValues(alpha: 0.3),
      );
    } else {
      switchWidget = Switch(
        value: value,
        onChanged: enabled ? _handleChanged : null,
        activeColor: colors.primary,
        activeTrackColor: colors.primary.withOpacity(0.3),
        inactiveThumbColor: colors.onSurface.withValues(alpha: 0.5),
        inactiveTrackColor: colors.onSurface.withValues(alpha: 0.3),
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      );
    }

    // Wrap with animation
    switchWidget = AnimatedContainer(
      duration: motion.duration,
      curve: motion.curve,
      child: switchWidget,
    );

    // If no label, return just the switch
    if (label == null) {
      return Opacity(
        opacity: enabled ? 1.0 : 0.6,
        child: switchWidget,
      );
    }

    // Return switch with label
    return Opacity(
      opacity: enabled ? 1.0 : 0.6,
      child: GestureDetector(
        onTap: enabled ? () => _handleChanged(!value) : null,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: Text(
                label!,
                style: TextStyle(
                  color: colors.onSurface,
                  fontSize: 14,
                ),
              ),
            ),
            SizedBox(width: density.horizontalPadding),
            switchWidget,
          ],
        ),
      ),
    );
  }
}
