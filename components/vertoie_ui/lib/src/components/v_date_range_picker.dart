import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import '../theme/v_theme_data.dart';
import '../theme/v_theme.dart';

/// Represents a date range with start and end dates
class VDateRange {
  const VDateRange({
    required this.start,
    required this.end,
  });

  final DateTime start;
  final DateTime end;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VDateRange &&
          runtimeType == other.runtimeType &&
          start == other.start &&
          end == other.end;

  @override
  int get hashCode => start.hashCode ^ end.hashCode;

  @override
  String toString() => 'VDateRange(start: $start, end: $end)';
}

/// A themeable date range picker widget that follows Vertoie design system.
/// Provides start/end date selection with range highlighting.
class VDateRangePicker extends VStatefulWidget {
  const VDateRangePicker({
    super.key,
    this.value,
    this.onChanged,
    this.firstDate,
    this.lastDate,
    this.startPlaceholder = 'Start date',
    this.endPlaceholder = 'End date',
    this.label,
    this.enabled = true,
    this.showClearButton = true,
    this.dateFormat,
  });

  /// The currently selected date range
  final VDateRange? value;

  /// Called when the date range selection changes
  final ValueChanged<VDateRange?>? onChanged;

  /// The earliest allowable date
  final DateTime? firstDate;

  /// The latest allowable date  
  final DateTime? lastDate;

  /// Placeholder text for start date when no date is selected
  final String startPlaceholder;

  /// Placeholder text for end date when no date is selected
  final String endPlaceholder;

  /// Optional label for the date range picker
  final String? label;

  /// Whether the date range picker is enabled
  final bool enabled;

  /// Whether to show a clear button when a range is selected
  final bool showClearButton;

  /// Custom date format function. If null, uses default format.
  final String Function(DateTime)? dateFormat;

  @override
  VState<VDateRangePicker> createState() => _VDateRangePickerState();
}

class _VDateRangePickerState extends VState<VDateRangePicker> {
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colors = getColors();
    final corners = getCorners();
    final density = getDensity();
    final motion = getMotion();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: TextStyle(
              color: colors.onSurface,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: density.smallGap), // Semantic small gap for label spacing
        ],
        _buildDateRangeInput(colors, corners, density, motion),
      ],
    );
  }

  Widget _buildDateRangeInput(VColors colors, VCorners corners, VComponentDensity density, VMotion motion) {
    final hasValue = widget.value != null;
    final displayText = hasValue 
        ? '${_formatDate(widget.value!.start)} - ${_formatDate(widget.value!.end)}'
        : '${widget.startPlaceholder} - ${widget.endPlaceholder}';

    return AnimatedContainer(
      duration: motion.duration,
      curve: motion.curve,
      height: density.height, // Use semantic height directly
      decoration: BoxDecoration(
        color: widget.enabled ? colors.surface : colors.neutral.shade100,
        border: Border.all(
          color: _focusNode.hasFocus
              ? colors.orange.primary
              : colors.neutral.shade300,
          width: _focusNode.hasFocus ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(corners.md),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.enabled ? _showDateRangePicker : null,
          borderRadius: BorderRadius.circular(corners.md),
          focusNode: _focusNode,
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: density.horizontalPadding, // Semantic horizontal padding
              vertical: density.verticalPadding,     // Semantic vertical padding
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    displayText,
                    style: TextStyle(
                      color: hasValue
                          ? colors.onSurface
                          : colors.neutral.shade500,
                      fontSize: 14, // Match VInput font size
                      height: 1.2, // Add line height for better text positioning
                    ),
                  ),
                ),
                if (hasValue && widget.showClearButton && widget.enabled) ...[
                  SizedBox(width: density.gap), // Semantic gap
                  _buildClearButton(colors, corners),
                  SizedBox(width: density.smallGap), // Semantic small gap
                ],
                Icon(
                  Icons.date_range,
                  color: colors.neutral.shade500,
                  size: density.iconSize, // Semantic icon size
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildClearButton(VColors colors, VCorners corners) {
    final density = getDensity();
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => widget.onChanged?.call(null),
        borderRadius: BorderRadius.circular(corners.sm),
        child: Padding(
          padding: EdgeInsets.all(density.smallGap), // Semantic small gap padding
          child: Icon(
            Icons.clear,
            color: colors.neutral.shade500,
            size: density.smallIconSize, // Semantic small icon size
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    if (widget.dateFormat != null) {
      return widget.dateFormat!(date);
    }
    
    // Default format: MMM dd (e.g., "Jan 15")
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    return '${months[date.month - 1]} ${date.day}';
  }

  Future<void> _showDateRangePicker() async {
    final colors = getColors();

    final selectedRange = await showDateRangePicker(
      context: context,
      firstDate: widget.firstDate ?? DateTime(1900),
      lastDate: widget.lastDate ?? DateTime(2100),
      initialDateRange: widget.value != null
          ? DateTimeRange(start: widget.value!.start, end: widget.value!.end)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: colors.orange.primary,
              onPrimary: colors.onPrimary,
              secondary: colors.orange.primary,
              onSecondary: colors.onPrimary,
              surface: colors.surface,
              onSurface: colors.onSurface,
              surfaceTint: Colors.transparent,
              primaryContainer: colors.orange.primary.withValues(alpha: 0.1),
              onPrimaryContainer: colors.onSurface,
              outline: colors.orange.primary,
              outlineVariant: colors.orange.primary,
            ),
          ),
          child: MediaQuery(
            data: MediaQuery.of(context).copyWith(
              size: Size(
                MediaQuery.of(context).size.width * 0.65, // 65% of screen width (more compact)
                MediaQuery.of(context).size.height * 0.7, // 70% of screen height
              ),
            ),
            child: Theme(
              data: Theme.of(context).copyWith(
                datePickerTheme: DatePickerThemeData(
                  backgroundColor: colors.surface,
                  surfaceTintColor: Colors.transparent,
                  headerBackgroundColor: colors.surface,
                  headerForegroundColor: colors.onSurface,
                  weekdayStyle: TextStyle(color: colors.onSurface),
                  dayStyle: TextStyle(color: colors.onSurface),
                  yearStyle: TextStyle(color: colors.onSurface),

                  // Range picker specific properties
                  rangePickerBackgroundColor: colors.surface,
                  rangePickerSurfaceTintColor: Colors.transparent,
                  rangePickerHeaderBackgroundColor: colors.surface,
                  rangePickerHeaderForegroundColor: colors.onSurface,
                  rangeSelectionBackgroundColor: colors.orange.primary.withValues(alpha: 0.1),
                  rangeSelectionOverlayColor: WidgetStateProperty.all(colors.orange.primary.withValues(alpha: 0.1)),

                  dayBackgroundColor: WidgetStateProperty.resolveWith((states) {
                    if (states.contains(WidgetState.selected)) {
                      return colors.orange.primary;
                    }
                    return null;
                  }),
                  dayOverlayColor: WidgetStateProperty.resolveWith((states) {
                    if (states.contains(WidgetState.selected)) {
                      return colors.orange.primary;
                    }
                    if (states.contains(WidgetState.hovered)) {
                      return colors.orange.primary.withValues(alpha: 0.1);
                    }
                    return null;
                  }),
                  dayForegroundColor: WidgetStateProperty.resolveWith((states) {
                    if (states.contains(WidgetState.selected)) {
                      return colors.onPrimary;
                    }
                    return colors.onSurface;
                  }),
                  todayForegroundColor: WidgetStateProperty.all(colors.orange.primary),
                  todayBackgroundColor: WidgetStateProperty.all(Colors.transparent),
                  todayBorder: BorderSide(color: colors.orange.primary),
                ),
                textButtonTheme: TextButtonThemeData(
                  style: TextButton.styleFrom(
                    foregroundColor: colors.orange.primary,
                  ),
                ),
              ),
              child: child!,
            ),
          ),
        );
      },
    );

    if (selectedRange != null) {
      widget.onChanged?.call(VDateRange(
        start: selectedRange.start,
        end: selectedRange.end,
      ));
    }
  }
}
