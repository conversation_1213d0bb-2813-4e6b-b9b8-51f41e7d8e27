import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import '../theme/v_theme_data.dart';

/// A themeable radio button widget that follows Vertoie design system.
/// Uses brand orange colors for selected state.
class VRadio<T> extends VWidget {
  const VRadio({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    this.label,
    this.enabled = true,
  });

  /// The value represented by this radio button
  final T value;

  /// The currently selected value in the radio group
  final T? groupValue;

  /// Called when this radio button is selected
  final ValueChanged<T?>? onChanged;

  /// Optional label text to display next to the radio button
  final String? label;

  /// Whether the radio button is enabled
  final bool enabled;

  /// Whether this radio button is currently selected
  bool get isSelected => value == groupValue;

  @override
  Widget build(BuildContext context) {
    final colors = getColors(context);
    final density = getDensity(context);
    final motion = getMotion(context);

    // Calculate colors based on selection state and theme
    final borderColor = _getBorderColor(colors);
    final fillColor = _getFillColor(colors);
    final dotColor = _getDotColor(colors);

    final Widget radio = GestureDetector(
      onTap: enabled ? _handleTap : null,
      child: AnimatedContainer(
        duration: motion.duration,
        curve: motion.curve,
        width: density.height * 0.5, // Match button density proportionally
        height: density.height * 0.5,
        decoration: BoxDecoration(
          color: fillColor,
          border: Border.all(
            color: borderColor,
            width: 2,
          ),
          shape: BoxShape.circle,
        ),
        child: isSelected
            ? Center(
                child: AnimatedContainer(
                  duration: motion.duration,
                  curve: motion.curve,
                  width: (density.height * 0.5) * 0.4, // Inner dot size
                  height: (density.height * 0.5) * 0.4,
                  decoration: BoxDecoration(
                    color: dotColor,
                    shape: BoxShape.circle,
                  ),
                ),
              )
            : null,
      ),
    );

    // If no label, return just the radio button
    if (label == null) {
      return Opacity(
        opacity: enabled ? 1.0 : 0.6,
        child: radio,
      );
    }

    // Return radio button with label
    return Opacity(
      opacity: enabled ? 1.0 : 0.6,
      child: GestureDetector(
        onTap: enabled ? _handleTap : null,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            radio,
            SizedBox(width: density.horizontalPadding),
            Flexible(
              child: Text(
                label!,
                style: TextStyle(
                  color: colors.onSurface,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Gets the border color for the radio button
  Color _getBorderColor(VColors colors) {
    if (!enabled) {
      return colors.neutral.shade300;
    }
    return isSelected ? colors.primary : colors.neutral.shade400;
  }

  /// Gets the background fill color for the radio button
  Color _getFillColor(VColors colors) {
    if (!enabled) {
      return isSelected ? colors.neutral.shade100 : Colors.transparent;
    }
    return Colors.transparent; // Always transparent background
  }

  /// Gets the dot color for the selected state
  Color _getDotColor(VColors colors) {
    if (!enabled) {
      return colors.neutral.shade400;
    }
    return colors.primary;
  }

  /// Handles tap events and notifies of selection
  void _handleTap() {
    if (!enabled || onChanged == null) return;
    
    // Only call onChanged if this radio button is not already selected
    if (!isSelected) {
      onChanged!(value);
    }
  }
}
