import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import '../theme/v_theme_data.dart';
import '../theme/v_theme.dart';

/// Time format options for VTimePicker
enum VTimeFormat {
  /// 12-hour format with AM/PM
  hour12,
  /// 24-hour format
  hour24,
}

/// A themeable time picker widget that follows Vertoie design system.
/// Supports 12/24 hour formats with wheel/dropdown selection.
class VTimePicker extends VStatefulWidget {
  const VTimePicker({
    super.key,
    this.value,
    this.onChanged,
    this.placeholder = 'Select time',
    this.label,
    this.enabled = true,
    this.showClearButton = true,
    this.format = VTimeFormat.hour12,
    this.timeFormat,
  });

  /// The currently selected time
  final TimeOfDay? value;

  /// Called when the time selection changes
  final ValueChanged<TimeOfDay?>? onChanged;

  /// Placeholder text when no time is selected
  final String placeholder;

  /// Optional label for the time picker
  final String? label;

  /// Whether the time picker is enabled
  final bool enabled;

  /// Whether to show a clear button when a time is selected
  final bool showClearButton;

  /// Time format (12-hour or 24-hour)
  final VTimeFormat format;

  /// Custom time format function. If null, uses default format.
  final String Function(TimeOfDay)? timeFormat;

  @override
  VState<VTimePicker> createState() => _VTimePickerState();
}

class _VTimePickerState extends VState<VTimePicker> {
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colors = getColors();
    final corners = getCorners();
    final density = getDensity();
    final motion = getMotion();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: TextStyle(
              color: colors.onSurface,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: density.smallGap), // Semantic small gap for label spacing
        ],
        _buildTimeInput(colors, corners, density, motion),
      ],
    );
  }

  Widget _buildTimeInput(VColors colors, VCorners corners, VComponentDensity density, VMotion motion) {
    final hasValue = widget.value != null;
    final displayText = hasValue 
        ? _formatTime(widget.value!)
        : widget.placeholder;

    return AnimatedContainer(
      duration: motion.duration,
      curve: motion.curve,
      height: density.height, // Use semantic height directly
      decoration: BoxDecoration(
        color: widget.enabled ? colors.surface : colors.surfaceVariant,
        border: Border.all(
          color: _focusNode.hasFocus
              ? colors.primary
              : colors.onSurface.withValues(alpha: 0.3),
          width: _focusNode.hasFocus ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(corners.md),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.enabled ? _showTimePicker : null,
          borderRadius: BorderRadius.circular(corners.md),
          focusNode: _focusNode,
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: density.horizontalPadding, // Semantic horizontal padding
              vertical: density.verticalPadding,     // Semantic vertical padding
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    displayText,
                    style: TextStyle(
                      color: hasValue
                          ? colors.onSurface
                          : colors.onSurface.withValues(alpha: 0.6),
                      fontSize: 14, // Match VInput font size
                      height: 1.2, // Add line height for better text positioning
                    ),
                  ),
                ),
                if (hasValue && widget.showClearButton && widget.enabled) ...[
                  SizedBox(width: density.gap), // Semantic gap
                  _buildClearButton(colors, corners),
                  SizedBox(width: density.smallGap), // Semantic small gap
                ],
                Icon(
                  Icons.access_time,
                  color: colors.onSurface.withValues(alpha: 0.6),
                  size: density.iconSize, // Semantic icon size
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildClearButton(VColors colors, VCorners corners) {
    final density = getDensity();
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => widget.onChanged?.call(null),
        borderRadius: BorderRadius.circular(corners.sm),
        child: Padding(
          padding: EdgeInsets.all(density.smallGap), // Semantic small gap padding
          child: Icon(
            Icons.clear,
            color: colors.onSurface.withValues(alpha: 0.6),
            size: density.smallIconSize, // Semantic small icon size
          ),
        ),
      ),
    );
  }

  String _formatTime(TimeOfDay time) {
    if (widget.timeFormat != null) {
      return widget.timeFormat!(time);
    }
    
    if (widget.format == VTimeFormat.hour24) {
      // 24-hour format: HH:mm (e.g., "14:30")
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    } else {
      // 12-hour format: h:mm AM/PM (e.g., "2:30 PM")
      final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
      final period = time.period == DayPeriod.am ? 'AM' : 'PM';
      return '$hour:${time.minute.toString().padLeft(2, '0')} $period';
    }
  }

  Future<void> _showTimePicker() async {
    final colors = getColors();
    
    final selectedTime = await showTimePicker(
      context: context,
      initialTime: widget.value ?? TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: colors.orange.primary,
              onPrimary: colors.onPrimary,
              secondary: colors.orange.primary,
              onSecondary: colors.onPrimary,
              surface: colors.surface,
              onSurface: colors.onSurface,
              surfaceTint: Colors.transparent,
              primaryContainer: colors.orange.primary.withValues(alpha: 0.1),
              onPrimaryContainer: colors.onSurface,
            ),
            timePickerTheme: TimePickerThemeData(
              backgroundColor: colors.surface,
              hourMinuteTextColor: colors.onSurface,
              hourMinuteColor: colors.orange.primary.withValues(alpha: 0.1),
              dayPeriodTextColor: colors.onSurface,
              dayPeriodColor: colors.orange.primary.withValues(alpha: 0.1),
              dayPeriodBorderSide: BorderSide(color: colors.orange.primary),
              dialHandColor: colors.orange.primary,
              dialBackgroundColor: colors.orange.primary.withValues(alpha: 0.1),
              dialTextColor: colors.onSurface,
              entryModeIconColor: colors.orange.primary,
              helpTextStyle: TextStyle(
                color: colors.onSurface,
              ),
              hourMinuteTextStyle: TextStyle(
                color: colors.onSurface,
                fontSize: 24,
                fontWeight: FontWeight.w400,
              ),
              dayPeriodTextStyle: TextStyle(
                color: colors.onSurface,
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: colors.orange.primary,
              ),
            ),
          ),
          child: MediaQuery(
            data: MediaQuery.of(context).copyWith(
              alwaysUse24HourFormat: widget.format == VTimeFormat.hour24,
            ),
            child: child!,
          ),
        );
      },
    );

    if (selectedTime != null) {
      widget.onChanged?.call(selectedTime);
    }
  }
}
