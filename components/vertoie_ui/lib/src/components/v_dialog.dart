import 'package:flutter/material.dart';
import '../base/v_widget.dart';
import '../theme/v_theme_data.dart';
import '../theme/v_theme.dart';
import '../theme/v_theme_provider.dart';

/// Dialog size presets
enum VDialogSize {
  small,   // 320px max width
  medium,  // 480px max width  
  large,   // 640px max width
  fullWidth, // Full width with margins
}

/// Dialog action button
class VDialogAction {
  const VDialogAction({
    required this.label,
    required this.onPressed,
    this.isDestructive = false,
    this.isPrimary = false,
  });

  final String label;
  final VoidCallback? onPressed;
  final bool isDestructive;
  final bool isPrimary;
}

/// A modal dialog component with customizable content and actions
class VDialog extends VWidget {
  const VDialog({
    super.key,
    this.title,
    this.content,
    this.child,
    this.actions = const [],
    this.size = VDialogSize.medium,
    this.dismissible = true,
    this.showCloseButton = true,
    this.onDismissed,
  }) : assert(
         content != null || child != null,
         'Either content or child must be provided',
       );

  /// Optional title text
  final String? title;

  /// Simple text content (alternative to child)
  final String? content;

  /// Custom widget content (alternative to content)
  final Widget? child;

  /// Action buttons for the dialog
  final List<VDialogAction> actions;

  /// Dialog size preset
  final VDialogSize size;

  /// Whether the dialog can be dismissed by tapping outside
  final bool dismissible;

  /// Whether to show the close button in header
  final bool showCloseButton;

  /// Callback when dialog is dismissed
  final VoidCallback? onDismissed;

  @override
  Widget build(BuildContext context) {
    final theme = getTheme(context);
    final colors = theme.colors;
    final corners = theme.corners;
    final density = theme.componentDensity;

    return AlertDialog(
      backgroundColor: colors.surface,
      surfaceTintColor: colors.surface,
      shadowColor: colors.onSurface.withOpacity(0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(corners.lg),
      ),
      contentPadding: EdgeInsets.zero,
      titlePadding: EdgeInsets.zero,
      actionsPadding: EdgeInsets.zero,
      insetPadding: EdgeInsets.all(_getHorizontalPadding()),
      content: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: _getMaxWidth(),
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            if (title != null || showCloseButton)
              _buildHeader(colors, density),
            
            // Content
            Flexible(
              child: _buildContent(colors, density),
            ),
            
            // Actions
            if (actions.isNotEmpty)
              _buildActions(colors, density),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(VColors colors, VComponentDensity density) {
    return Container(
      padding: EdgeInsets.all(density.largeGap),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: colors.onSurface.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          if (title != null)
            Expanded(
              child: Text(
                title!,
                style: TextStyle(
                  color: colors.onSurface,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          if (showCloseButton)
            IconButton(
              onPressed: onDismissed,
              icon: Icon(
                Icons.close,
                color: colors.onSurface.withOpacity(0.7),
                size: density.iconSize,
              ),
              constraints: BoxConstraints(
                minWidth: density.iconSize * 1.5,
                minHeight: density.iconSize * 1.5,
              ),
              padding: EdgeInsets.zero,
            ),
        ],
      ),
    );
  }

  Widget _buildContent(VColors colors, VComponentDensity density) {
    Widget contentWidget;
    
    if (child != null) {
      contentWidget = child!;
    } else if (content != null) {
      contentWidget = Text(
        content!,
        style: TextStyle(
          color: colors.onSurface.withOpacity(0.8),
          fontSize: 14,
          height: 1.5,
        ),
      );
    } else {
      contentWidget = const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(density.largeGap),
      child: contentWidget,
    );
  }

  Widget _buildActions(VColors colors, VComponentDensity density) {
    return Container(
      padding: EdgeInsets.all(density.largeGap),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: colors.onSurface.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: actions.asMap().entries.map((entry) {
          final index = entry.key;
          final action = entry.value;
          
          return Padding(
            padding: EdgeInsets.only(
              left: index > 0 ? density.gap : 0,
            ),
            child: _buildActionButton(action, colors, density),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildActionButton(VDialogAction action, VColors colors, VComponentDensity density) {
    final buttonStyle = action.isPrimary
        ? ElevatedButton.styleFrom(
            backgroundColor: action.isDestructive ? colors.error : colors.primary,
            foregroundColor: colors.surface,
            elevation: 0,
            padding: EdgeInsets.symmetric(
              horizontal: density.horizontalPadding * 1.5,
              vertical: density.verticalPadding,
            ),
          )
        : TextButton.styleFrom(
            foregroundColor: action.isDestructive ? colors.error : colors.primary,
            padding: EdgeInsets.symmetric(
              horizontal: density.horizontalPadding * 1.5,
              vertical: density.verticalPadding,
            ),
          );

    final buttonChild = Text(
      action.label,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
      ),
    );

    return action.isPrimary
        ? ElevatedButton(
            onPressed: action.onPressed,
            style: buttonStyle,
            child: buttonChild,
          )
        : TextButton(
            onPressed: action.onPressed,
            style: buttonStyle,
            child: buttonChild,
          );
  }

  double _getMaxWidth() {
    switch (size) {
      case VDialogSize.small:
        return 320;
      case VDialogSize.medium:
        return 480;
      case VDialogSize.large:
        return 640;
      case VDialogSize.fullWidth:
        return double.infinity;
    }
  }

  double _getHorizontalPadding() {
    switch (size) {
      case VDialogSize.small:
      case VDialogSize.medium:
      case VDialogSize.large:
        return 24;
      case VDialogSize.fullWidth:
        return 16;
    }
  }

  /// Convenience method to show a confirmation dialog
  static Future<bool?> showConfirmation(
    BuildContext context, {
    required String title,
    required String message,
    String confirmLabel = 'Confirm',
    String cancelLabel = 'Cancel',
    bool isDestructive = false,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => VDialog(
        title: title,
        content: message,
        actions: [
          VDialogAction(
            label: cancelLabel,
            onPressed: () => Navigator.of(context).pop(false),
          ),
          VDialogAction(
            label: confirmLabel,
            onPressed: () => Navigator.of(context).pop(true),
            isPrimary: true,
            isDestructive: isDestructive,
          ),
        ],
      ),
    );
  }

  /// Convenience method to show an alert dialog
  static Future<void> showAlert(
    BuildContext context, {
    required String title,
    required String message,
    String buttonLabel = 'OK',
  }) {
    return showDialog<void>(
      context: context,
      builder: (context) => VDialog(
        title: title,
        content: message,
        actions: [
          VDialogAction(
            label: buttonLabel,
            onPressed: () => Navigator.of(context).pop(),
            isPrimary: true,
          ),
        ],
      ),
    );
  }

  /// Show a dialog with proper theme inheritance
  static Future<T?> show<T>(BuildContext context, VDialog dialog) {
    final theme = VThemeProvider.of(context);
    return showDialog<T>(
      context: context,
      builder: (_) => VThemeProvider(
        theme: theme,
        child: dialog,
      ),
    );
  }
}
