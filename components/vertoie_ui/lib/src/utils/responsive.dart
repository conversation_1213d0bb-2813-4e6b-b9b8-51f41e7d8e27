import 'package:flutter/material.dart';

/// Responsive breakpoints for the Vertoie UI library
class VBreakpoints {
  static const double mobile = 768;
  static const double tablet = 1024;
  static const double desktop = 1440;
}

/// Utility class for responsive design helpers
class VResponsive {
  /// Returns true if the current screen width is mobile size
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < VBreakpoints.mobile;
  }

  /// Returns true if the current screen width is tablet size
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= VBreakpoints.mobile && width < VBreakpoints.tablet;
  }

  /// Returns true if the current screen width is desktop size
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= VBreakpoints.desktop;
  }

  /// Returns the current screen type
  static VScreenType getScreenType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < VBreakpoints.mobile) return VScreenType.mobile;
    if (width < VBreakpoints.tablet) return VScreenType.tablet;
    return VScreenType.desktop;
  }

  /// Returns a value based on the current screen size
  static T responsive<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    final screenType = getScreenType(context);
    switch (screenType) {
      case VScreenType.mobile:
        return mobile;
      case VScreenType.tablet:
        return tablet ?? mobile;
      case VScreenType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }
}

/// Screen type enumeration
enum VScreenType {
  mobile,
  tablet,
  desktop,
}
