import 'package:flutter/material.dart';
import 'v_theme.dart';
import 'v_brand_colors.dart';

/// The main theme data class that holds all design tokens and configuration
/// for the Vertoie UI component library.
class VThemeData {
  const VThemeData({
    this.cornerStyle = VCornerStyle.subtle,
    this.elevationLevel = VElevationLevel.subtle,
    this.density = VDensity.comfortable,
    this.motionStyle = VMotionStyle.subtle,
    this.isDark = false,
  });

  // Individual theme selections
  final VCornerStyle cornerStyle;
  final VElevationLevel elevationLevel;
  final VDensity density;
  final VMotionStyle motionStyle;
  final bool isDark;

  // Computed properties based on selections
  VCorners get corners => _getCornersForStyle(cornerStyle);
  VElevation get elevation => _getElevationForLevel(elevationLevel);
  VComponentDensity get componentDensity => _getDensityForLevel(density);
  VMotion get motion => _getMotionForStyle(motionStyle);
  VColors get colors => isDark ? VColors.dark() : VColors.defaultColors();

  /// Creates a default light theme
  factory VThemeData.defaultTheme() {
    return const VThemeData();
  }

  /// Creates a dark theme
  factory VThemeData.dark() {
    return const VThemeData(isDark: true);
  }

  // Helper methods to get instances from enums
  VCorners _getCornersForStyle(VCornerStyle style) {
    switch (style) {
      case VCornerStyle.sharp:
        return VCorners.sharp;
      case VCornerStyle.subtle:
        return VCorners.subtle;
      case VCornerStyle.rounded:
        return VCorners.rounded;
    }
  }

  VElevation _getElevationForLevel(VElevationLevel level) {
    switch (level) {
      case VElevationLevel.flat:
        return VElevation.flat;
      case VElevationLevel.subtle:
        return VElevation.subtle;
      case VElevationLevel.elevated:
        return VElevation.elevated;
      case VElevationLevel.floating:
        return VElevation.floating;
    }
  }

  VComponentDensity _getDensityForLevel(VDensity densityLevel) {
    switch (densityLevel) {
      case VDensity.compact:
        return VComponentDensity.compact;
      case VDensity.comfortable:
        return VComponentDensity.comfortable;
      case VDensity.spacious:
        return VComponentDensity.spacious;
    }
  }

  VMotion _getMotionForStyle(VMotionStyle style) {
    switch (style) {
      case VMotionStyle.none:
        return VMotion.none;
      case VMotionStyle.subtle:
        return VMotion.subtle;
      case VMotionStyle.expressive:
        return VMotion.expressive;
    }
  }



  /// Creates a copy of this theme with the given fields replaced
  VThemeData copyWith({
    VCornerStyle? cornerStyle,
    VElevationLevel? elevationLevel,
    VDensity? density,
    VMotionStyle? motionStyle,
    bool? isDark,
  }) {
    return VThemeData(
      cornerStyle: cornerStyle ?? this.cornerStyle,
      elevationLevel: elevationLevel ?? this.elevationLevel,
      density: density ?? this.density,
      motionStyle: motionStyle ?? this.motionStyle,
      isDark: isDark ?? this.isDark,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VThemeData &&
        other.cornerStyle == cornerStyle &&
        other.elevationLevel == elevationLevel &&
        other.density == density &&
        other.motionStyle == motionStyle &&
        other.isDark == isDark;
  }

  @override
  int get hashCode {
    return Object.hash(
      cornerStyle,
      elevationLevel,
      density,
      motionStyle,
      isDark,
    );
  }
}

/// Color system for the Vertoie UI theme
class VColors {
  const VColors({
    required this.primary,
    required this.primaryVariant,
    required this.secondary,
    required this.secondaryVariant,
    required this.surface,
    required this.surfaceVariant,
    required this.background,
    required this.backgroundVariant,
    required this.error,
    required this.errorVariant,
    required this.warning,
    required this.warningVariant,
    required this.success,
    required this.successVariant,
    required this.info,
    required this.infoVariant,
    required this.onPrimary,
    required this.onSecondary,
    required this.onSurface,
    required this.onBackground,
    required this.onError,
    required this.onWarning,
    required this.onSuccess,
    required this.onInfo,
    // Brand color scales
    required this.orange,
    required this.amber,
    required this.neutral,
  });

  // Base colors
  final Color primary;
  final Color primaryVariant;
  final Color secondary;
  final Color secondaryVariant;
  final Color surface;
  final Color surfaceVariant;
  final Color background;
  final Color backgroundVariant;
  final Color error;
  final Color errorVariant;
  final Color warning;
  final Color warningVariant;
  final Color success;
  final Color successVariant;
  final Color info;
  final Color infoVariant;

  // On colors (text/icons on colored backgrounds)
  final Color onPrimary;
  final Color onSecondary;
  final Color onSurface;
  final Color onBackground;
  final Color onError;
  final Color onWarning;
  final Color onSuccess;
  final Color onInfo;

  // Brand color scales
  final VColorScale orange;
  final VColorScale amber;
  final VColorScale neutral;

  /// Creates a default color scheme using Vertoie brand colors
  factory VColors.defaultColors() {
    return const VColors(
      // Primary colors using orange brand scale (darker for better contrast)
      primary: Color(0xFFCC4700),        // Orange 700 (better contrast with white)
      primaryVariant: Color(0xFFB8400A),  // Orange 800

      // Secondary colors using amber scale (darker for better contrast)
      secondary: Color(0xFFB45309),       // Amber 700 (better contrast with white)
      secondaryVariant: Color(0xFF92400E), // Amber 800

      // Surface colors using neutral scale
      surface: Color(0xFFFFFFFF),         // Pure white
      surfaceVariant: Color(0xFFFAFAFA),  // Gray 50

      // Background colors
      background: Color(0xFFFFF7F0),      // Warm white
      backgroundVariant: Color(0xFFFAFAFA), // Gray 50

      // Semantic colors
      error: Color(0xFFDC2626),           // Error red (darker for better contrast)
      errorVariant: Color(0xFFB91C1C),    // Error dark
      warning: Color(0xFFF59E0B),         // Warning amber
      warningVariant: Color(0xFFD97706),  // Warning dark
      success: Color(0xFF22C55E),         // Success green
      successVariant: Color(0xFF16A34A),  // Success dark
      info: Color(0xFF3B82F6),            // Info blue
      infoVariant: Color(0xFF2563EB),     // Info dark

      // On colors (text/icons on colored backgrounds)
      onPrimary: Color(0xFFFFFFFF),       // White on orange
      onSecondary: Color(0xFFFFFFFF),     // White on amber
      onSurface: Color(0xFF1F2937),       // Gray 900 on white
      onBackground: Color(0xFF1F2937),    // Gray 900 on warm white
      onError: Color(0xFFFFFFFF),         // White on red
      onWarning: Color(0xFFFFFFFF),       // White on amber
      onSuccess: Color(0xFFFFFFFF),       // White on green
      onInfo: Color(0xFFFFFFFF),          // White on blue

      // Brand color scales
      orange: VBrandColors.orange,
      amber: VBrandColors.amber,
      neutral: VBrandColors.neutral,
    );
  }



  /// Creates a dark theme for low-light environments
  factory VColors.dark() {
    return const VColors(
      // Dark theme primary colors (same as light theme for consistency)
      primary: Color(0xFFCC4700),        // Orange 700 (darker for better contrast)
      primaryVariant: Color(0xFFB8400A),  // Orange 800

      // Dark theme secondary colors
      secondary: Color(0xFFB45309),       // Amber 700 (darker for better contrast)
      secondaryVariant: Color(0xFF92400E), // Amber 800

      // Dark theme surface colors
      surface: Color(0xFF1F2937),         // Dark surface
      surfaceVariant: Color(0xFF374151),  // Darker surface

      // Dark theme background colors
      background: Color(0xFF111827),      // Dark background
      backgroundVariant: Color(0xFF1F2937), // Dark surface

      // Dark theme semantic colors
      error: Color(0xFFDC2626),           // Error red (darker for better contrast)
      errorVariant: Color(0xFFB91C1C),    // Error dark
      warning: Color(0xFFF59E0B),         // Warning amber
      warningVariant: Color(0xFFD97706),  // Warning dark
      success: Color(0xFF22C55E),         // Success green
      successVariant: Color(0xFF16A34A),  // Success dark
      info: Color(0xFF3B82F6),            // Info blue
      infoVariant: Color(0xFF2563EB),     // Info dark

      // Dark theme on colors
      onPrimary: Color(0xFFFFFFFF),       // White on orange
      onSecondary: Color(0xFFFFFFFF),     // White on amber
      onSurface: Color(0xFFE5E7EB),       // Light text on dark surface
      onBackground: Color(0xFFE5E7EB),    // Light text on dark background
      onError: Color(0xFFFFFFFF),         // White on red
      onWarning: Color(0xFFFFFFFF),       // White on amber
      onSuccess: Color(0xFFFFFFFF),       // White on green
      onInfo: Color(0xFFFFFFFF),          // White on blue

      // Brand color scales (use dark neutral for dark mode)
      orange: VBrandColors.orange,
      amber: VBrandColors.amber,
      neutral: VBrandColors.darkNeutral,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VColors &&
        other.primary == primary &&
        other.primaryVariant == primaryVariant &&
        other.secondary == secondary &&
        other.secondaryVariant == secondaryVariant &&
        other.surface == surface &&
        other.surfaceVariant == surfaceVariant &&
        other.background == background &&
        other.backgroundVariant == backgroundVariant &&
        other.error == error &&
        other.errorVariant == errorVariant &&
        other.warning == warning &&
        other.warningVariant == warningVariant &&
        other.success == success &&
        other.successVariant == successVariant &&
        other.info == info &&
        other.infoVariant == infoVariant &&
        other.onPrimary == onPrimary &&
        other.onSecondary == onSecondary &&
        other.onSurface == onSurface &&
        other.onBackground == onBackground &&
        other.onError == onError &&
        other.onWarning == onWarning &&
        other.onSuccess == onSuccess &&
        other.onInfo == onInfo &&
        other.orange == orange &&
        other.amber == amber &&
        other.neutral == neutral;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      primary,
      primaryVariant,
      secondary,
      secondaryVariant,
      surface,
      surfaceVariant,
      background,
      backgroundVariant,
      error,
      errorVariant,
      warning,
      warningVariant,
      success,
      successVariant,
      info,
      infoVariant,
      onPrimary,
      onSecondary,
      onSurface,
      onBackground,
      onError,
      onWarning,
      onSuccess,
      onInfo,
      orange,
      amber,
      neutral,
    ]);
  }
}
