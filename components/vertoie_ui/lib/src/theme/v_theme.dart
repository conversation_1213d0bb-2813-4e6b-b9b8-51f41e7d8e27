import 'package:flutter/material.dart';

/// Design tokens for corner radius
enum VCornerStyle {
  sharp,
  subtle,
  rounded,
}

/// Design tokens for elevation/shadows
enum VElevationLevel {
  flat,
  subtle,
  elevated,
  floating,
}

/// Design tokens for component density
enum VDensity {
  compact,
  comfortable,
  spacious,
}

/// Design tokens for animation presets
enum VMotionStyle {
  none,
  subtle,
  expressive,
}



/// Corner radius values for different styles
class VCorners {
  const VCorners({
    required this.sm,
    required this.md,
    required this.lg,
  });

  final double sm;
  final double md;
  final double lg;

  static const VCorners sharp = VCorners(sm: 0, md: 2, lg: 4);
  static const VCorners subtle = VCorners(sm: 4, md: 8, lg: 12);
  static const VCorners rounded = VCorners(sm: 8, md: 12, lg: 16);
}

/// Elevation/shadow definitions
class VElevation {
  const VElevation({
    required this.boxShadow,
  });

  final List<BoxShadow> boxShadow;

  static const VElevation flat = VElevation(boxShadow: []);

  static const VElevation subtle = VElevation(
    boxShadow: [
      BoxShadow(
        offset: Offset(0, 1),
        blurRadius: 3,
        color: Color.fromRGBO(0, 0, 0, 0.12),
      ),
    ],
  );

  static const VElevation elevated = VElevation(
    boxShadow: [
      BoxShadow(
        offset: Offset(0, 4),
        blurRadius: 6,
        color: Color.fromRGBO(0, 0, 0, 0.15),
      ),
    ],
  );

  static const VElevation floating = VElevation(
    boxShadow: [
      BoxShadow(
        offset: Offset(0, 10),
        blurRadius: 20,
        color: Color.fromRGBO(0, 0, 0, 0.15),
      ),
    ],
  );
}

/// Component density configuration
class VComponentDensity {
  const VComponentDensity({
    required this.height,
    required this.horizontalPadding,
    required this.verticalPadding,
    required this.iconSize,
    required this.smallIconSize,
    required this.gap,
    required this.smallGap,
    required this.largeGap,
    // Keep the size scale for special cases
    required this.xs,
    required this.sm,
    required this.md,
    required this.lg,
    required this.xl,
  });

  // Semantic properties that components should use
  final double height;
  final double horizontalPadding;  // Main horizontal padding for inputs/buttons
  final double verticalPadding;    // Main vertical padding for inputs/buttons
  final double iconSize;           // Standard icon size
  final double smallIconSize;      // Small icon size (clear buttons, etc.)
  final double gap;                // Standard gap between elements
  final double smallGap;           // Small gap between elements
  final double largeGap;           // Large gap between sections

  // Size scale values for special cases where semantic properties don't apply
  final double xs;
  final double sm;
  final double md;
  final double lg;
  final double xl;

  static const VComponentDensity compact = VComponentDensity(
    height: 36,
    horizontalPadding: 12,
    verticalPadding: 8,
    iconSize: 16,
    smallIconSize: 12,
    gap: 8,
    smallGap: 4,
    largeGap: 16,
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
  );

  static const VComponentDensity comfortable = VComponentDensity(
    height: 44,
    horizontalPadding: 16,
    verticalPadding: 12,
    iconSize: 20,
    smallIconSize: 16,
    gap: 12,
    smallGap: 6,
    largeGap: 24,
    xs: 8,
    sm: 16,
    md: 24,
    lg: 32,
    xl: 40,
  );

  static const VComponentDensity spacious = VComponentDensity(
    height: 52,
    horizontalPadding: 20,
    verticalPadding: 16,
    iconSize: 24,
    smallIconSize: 20,
    gap: 16,
    smallGap: 8,
    largeGap: 32,
    xs: 12,
    sm: 24,
    md: 36,
    lg: 48,
    xl: 60,
  );
}

/// Animation configuration
class VMotion {
  const VMotion({
    required this.duration,
    required this.curve,
  });

  final Duration duration;
  final Curve curve;

  static const VMotion none = VMotion(
    duration: Duration.zero,
    curve: Curves.linear,
  );

  static const VMotion subtle = VMotion(
    duration: Duration(milliseconds: 150),
    curve: Curves.easeInOut,
  );

  static const VMotion expressive = VMotion(
    duration: Duration(milliseconds: 300),
    curve: Curves.elasticOut,
  );
}