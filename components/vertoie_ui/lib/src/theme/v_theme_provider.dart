import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'v_theme_data.dart';

/// A widget that provides VThemeData to its descendants using the Provider pattern.
/// This enables any widget in the widget tree to access and use the theme.
class VThemeProvider extends StatelessWidget {
  const VThemeProvider({
    super.key,
    required this.theme,
    required this.child,
  });

  /// The theme data to provide to descendants
  final VThemeData theme;

  /// The widget below this widget in the tree
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Provider<VThemeData>.value(
      value: theme,
      child: child,
    );
  }

  /// Retrieves the VThemeData from the nearest VThemeProvider ancestor.
  /// 
  /// If no VThemeProvider is found, returns a default theme.
  static VThemeData of(BuildContext context) {
    try {
      return Provider.of<VThemeData>(context, listen: false);
    } catch (e) {
      // Fallback to default theme if no provider found
      return VThemeData.defaultTheme();
    }
  }

  /// Retrieves the VThemeData from the nearest VThemeProvider ancestor
  /// and registers the calling widget to rebuild when the theme changes.
  static VThemeData watch(BuildContext context) {
    try {
      return Provider.of<VThemeData>(context, listen: true);
    } catch (e) {
      // Fallback to default theme if no provider found
      return VThemeData.defaultTheme();
    }
  }

  /// Updates the theme data by replacing it with a new theme.
  /// This is useful for runtime theme switching.
  static void updateTheme(BuildContext context, VThemeData newTheme) {
    // This method would work with a ChangeNotifierProvider for dynamic themes
    // For now, it's a placeholder for future theme switching functionality
    debugPrint('Theme update requested: ${newTheme.runtimeType}');
  }
}

/// A convenience widget that wraps VThemeProvider and provides commonly
/// used theme data directly to child widgets.
class VTheme extends InheritedWidget {
  const VTheme({
    super.key,
    required this.data,
    required super.child,
  });

  /// The theme data
  final VThemeData data;

  /// Retrieves the VThemeData from the nearest VTheme ancestor.
  static VThemeData? maybeOf(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<VTheme>()?.data;
  }

  /// Retrieves the VThemeData from the nearest VTheme ancestor.
  /// 
  /// Throws if no VTheme ancestor is found.
  static VThemeData of(BuildContext context) {
    final VThemeData? result = maybeOf(context);
    assert(result != null, 'No VTheme found in context');
    return result!;
  }

  @override
  bool updateShouldNotify(VTheme oldWidget) {
    return data != oldWidget.data;
  }

  /// Creates a VTheme with default theme data
  static Widget defaultTheme({required Widget child}) {
    return VTheme(
      data: VThemeData.defaultTheme(),
      child: child,
    );
  }


}
