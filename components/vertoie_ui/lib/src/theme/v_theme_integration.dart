import 'package:flutter/material.dart';
import 'v_theme_data.dart';
import 'v_brand_colors.dart';

/// Integration utilities to convert VThemeData to Flutter's ThemeData
/// This ensures all Material Design colors are properly overridden with Vertoie colors
class VThemeIntegration {
  VThemeIntegration._();

  /// Converts VColors to Flutter's ColorScheme with all colors properly mapped
  static ColorScheme toColorScheme(VColors colors) {
    final bool isDark = colors.neutral == VBrandColors.darkNeutral;

    return ColorScheme(
      brightness: isDark ? Brightness.dark : Brightness.light,
      
      // Primary color family
      primary: colors.primary,
      onPrimary: colors.onPrimary,
      primaryContainer: colors.orange.shade200,
      onPrimaryContainer: colors.neutral.shade800,
      primaryFixed: colors.orange.shade300,
      primaryFixedDim: colors.orange.shade400,
      onPrimaryFixed: colors.neutral.shade900,
      onPrimaryFixedVariant: colors.neutral.shade700,
      
      // Secondary color family
      secondary: colors.secondary,
      onSecondary: colors.onSecondary,
      secondaryContainer: colors.amber.shade200,
      onSecondaryContainer: colors.neutral.shade800,
      secondaryFixed: colors.amber.shade300,
      secondaryFixedDim: colors.amber.shade400,
      onSecondaryFixed: colors.neutral.shade900,
      onSecondaryFixedVariant: colors.neutral.shade700,
      
      // Tertiary color family (using orange variants)
      tertiary: colors.orange.shade600,
      onTertiary: Colors.white,
      tertiaryContainer: colors.orange.shade100,
      onTertiaryContainer: colors.neutral.shade800,
      tertiaryFixed: colors.orange.shade200,
      tertiaryFixedDim: colors.orange.shade300,
      onTertiaryFixed: colors.neutral.shade900,
      onTertiaryFixedVariant: colors.neutral.shade700,
      
      // Error color family
      error: colors.error,
      onError: colors.onError,
      errorContainer: isDark 
          ? const Color(0xFF5F2126) 
          : const Color(0xFFFFDAD6),
      onErrorContainer: isDark 
          ? const Color(0xFFFFB4AB) 
          : const Color(0xFF410002),
      
      // Surface color family
      surface: colors.surface,
      onSurface: colors.onSurface,
      surfaceVariant: colors.surfaceVariant,
      onSurfaceVariant: colors.neutral.shade600,
      surfaceDim: isDark 
          ? colors.neutral.shade100 
          : colors.neutral.shade200,
      surfaceBright: isDark 
          ? colors.neutral.shade300 
          : colors.neutral.shade50,
      surfaceContainerLowest: isDark 
          ? colors.neutral.shade50 
          : colors.neutral.shade50,
      surfaceContainerLow: isDark 
          ? colors.neutral.shade100 
          : colors.neutral.shade100,
      surfaceContainer: isDark 
          ? colors.neutral.shade200 
          : colors.neutral.shade200,
      surfaceContainerHigh: isDark 
          ? colors.neutral.shade300 
          : colors.neutral.shade300,
      surfaceContainerHighest: isDark 
          ? colors.neutral.shade400 
          : colors.neutral.shade400,
      
      // Inverse colors
      inverseSurface: isDark 
          ? colors.neutral.shade800 
          : colors.neutral.shade100,
      onInverseSurface: isDark 
          ? colors.neutral.shade100 
          : colors.neutral.shade800,
      inversePrimary: isDark 
          ? colors.orange.shade600 
          : colors.orange.shade200,
      
      // Outline colors
      outline: colors.neutral.shade500,
      outlineVariant: colors.neutral.shade300,
      
      // Shadow and scrim
      shadow: Colors.black,
      scrim: Colors.black,
      
      // Surface tint
      surfaceTint: colors.primary,
      
      // Background (deprecated but still used by some components)
      background: colors.background,
      onBackground: colors.onBackground,
    );
  }

  /// Creates a complete ThemeData from VThemeData
  static ThemeData toThemeData(VThemeData vTheme) {
    final ColorScheme colorScheme = toColorScheme(vTheme.colors);
    
    return ThemeData(
      colorScheme: colorScheme,
      useMaterial3: true,
      brightness: vTheme.isDark ? Brightness.dark : Brightness.light,
      
      // Override specific component themes to ensure consistency
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: TextStyle(
          color: colorScheme.onSurface,
          fontSize: 20,
          fontWeight: FontWeight.w500,
        ),
        iconTheme: IconThemeData(color: colorScheme.onSurface),
        actionsIconTheme: IconThemeData(color: colorScheme.onSurface),
      ),
      
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          elevation: vTheme.elevation.boxShadow.isEmpty ? 0 : 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(vTheme.corners.md),
          ),
        ),
      ),
      
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: colorScheme.primary,
          side: BorderSide(color: colorScheme.outline),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(vTheme.corners.md),
          ),
        ),
      ),
      
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: colorScheme.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(vTheme.corners.md),
          ),
        ),
      ),
      
      cardTheme: CardThemeData(
        color: colorScheme.surface,
        elevation: vTheme.elevation.boxShadow.isEmpty ? 0 : 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.lg),
        ),
      ),
      
      inputDecorationTheme: InputDecorationTheme(
        fillColor: colorScheme.surfaceVariant,
        filled: true,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.md),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.md),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.md),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.md),
          borderSide: BorderSide(color: colorScheme.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.md),
          borderSide: BorderSide(color: colorScheme.error, width: 2),
        ),
        labelStyle: TextStyle(color: colorScheme.onSurfaceVariant),
        hintStyle: TextStyle(color: colorScheme.onSurfaceVariant),
      ),
      
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          return null;
        }),
        checkColor: WidgetStateProperty.all(colorScheme.onPrimary),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.sm),
        ),
      ),
      
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          return colorScheme.onSurface;
        }),
      ),
      
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.onPrimary;
          }
          return colorScheme.outline;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          return colorScheme.surfaceVariant;
        }),
      ),
      
      sliderTheme: SliderThemeData(
        activeTrackColor: colorScheme.primary,
        inactiveTrackColor: colorScheme.surfaceVariant,
        thumbColor: colorScheme.primary,
        overlayColor: colorScheme.primary.withValues(alpha: 0.12),
        valueIndicatorColor: colorScheme.primary,
        valueIndicatorTextStyle: TextStyle(
          color: colorScheme.onPrimary,
        ),
      ),
      
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: colorScheme.primary,
        linearTrackColor: colorScheme.surfaceVariant,
        circularTrackColor: colorScheme.surfaceVariant,
      ),
      
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: colorScheme.primaryContainer,
        foregroundColor: colorScheme.onPrimaryContainer,
        elevation: vTheme.elevation.boxShadow.isEmpty ? 0 : 6,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.lg),
        ),
      ),
      
      chipTheme: ChipThemeData(
        backgroundColor: colorScheme.surfaceVariant,
        selectedColor: colorScheme.secondaryContainer,
        labelStyle: TextStyle(color: colorScheme.onSurfaceVariant),
        secondaryLabelStyle: TextStyle(color: colorScheme.onSecondaryContainer),
        padding: EdgeInsets.symmetric(
          horizontal: vTheme.componentDensity.horizontalPadding,
          vertical: vTheme.componentDensity.verticalPadding,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.sm),
        ),
      ),
      
      dividerTheme: DividerThemeData(
        color: colorScheme.outlineVariant,
        thickness: 1,
      ),
      
      listTileTheme: ListTileThemeData(
        tileColor: colorScheme.surface,
        selectedTileColor: colorScheme.secondaryContainer,
        textColor: colorScheme.onSurface,
        iconColor: colorScheme.onSurfaceVariant,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.md),
        ),
      ),
      
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: colorScheme.surface,
        selectedItemColor: colorScheme.primary,
        unselectedItemColor: colorScheme.onSurfaceVariant,
        type: BottomNavigationBarType.fixed,
      ),
      
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: colorScheme.surface,
        indicatorColor: colorScheme.secondaryContainer,
        labelTextStyle: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return TextStyle(color: colorScheme.onSurface);
          }
          return TextStyle(color: colorScheme.onSurfaceVariant);
        }),
        iconTheme: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return IconThemeData(color: colorScheme.onSecondaryContainer);
          }
          return IconThemeData(color: colorScheme.onSurfaceVariant);
        }),
      ),
      
      navigationRailTheme: NavigationRailThemeData(
        backgroundColor: colorScheme.surface,
        selectedIconTheme: IconThemeData(color: colorScheme.onSecondaryContainer),
        unselectedIconTheme: IconThemeData(color: colorScheme.onSurfaceVariant),
        selectedLabelTextStyle: TextStyle(color: colorScheme.onSurface),
        unselectedLabelTextStyle: TextStyle(color: colorScheme.onSurfaceVariant),
        indicatorColor: colorScheme.secondaryContainer,
      ),
      
      tabBarTheme: TabBarThemeData(
        labelColor: colorScheme.primary,
        unselectedLabelColor: colorScheme.onSurfaceVariant,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
      ),
      
      snackBarTheme: SnackBarThemeData(
        backgroundColor: colorScheme.inverseSurface,
        contentTextStyle: TextStyle(color: colorScheme.onInverseSurface),
        actionTextColor: colorScheme.inversePrimary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.md),
        ),
      ),
      
      dialogTheme: DialogThemeData(
        backgroundColor: colorScheme.surface,
        titleTextStyle: TextStyle(
          color: colorScheme.onSurface,
          fontSize: 24,
          fontWeight: FontWeight.w400,
        ),
        contentTextStyle: TextStyle(
          color: colorScheme.onSurfaceVariant,
          fontSize: 16,
          fontWeight: FontWeight.w400,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.lg),
        ),
      ),
      
      popupMenuTheme: PopupMenuThemeData(
        color: colorScheme.surface,
        textStyle: TextStyle(color: colorScheme.onSurface),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.md),
        ),
      ),
      
      tooltipTheme: TooltipThemeData(
        decoration: BoxDecoration(
          color: colorScheme.inverseSurface,
          borderRadius: BorderRadius.circular(vTheme.corners.sm),
        ),
        textStyle: TextStyle(
          color: colorScheme.onInverseSurface,
          fontSize: 12,
        ),
      ),
    );
  }

  /// Creates a ThemeData specifically for Material components that need
  /// consistent color overrides (like DatePicker, TimePicker, etc.)
  static ThemeData createMaterialComponentTheme(
    BuildContext context, 
    VThemeData vTheme,
  ) {
    final ColorScheme colorScheme = toColorScheme(vTheme.colors);
    final ThemeData baseTheme = toThemeData(vTheme);
    
    return baseTheme.copyWith(
      // Date picker theme
      datePickerTheme: DatePickerThemeData(
        backgroundColor: colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        headerBackgroundColor: colorScheme.surface,
        headerForegroundColor: colorScheme.onSurface,
        weekdayStyle: TextStyle(color: colorScheme.onSurface),
        dayStyle: TextStyle(color: colorScheme.onSurface),
        yearStyle: TextStyle(color: colorScheme.onSurface),
        dayBackgroundColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          return null;
        }),
        dayOverlayColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.primary;
          }
          if (states.contains(WidgetState.hovered)) {
            return colorScheme.primary.withValues(alpha: 0.08);
          }
          return null;
        }),
        dayForegroundColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return colorScheme.onPrimary;
          }
          return colorScheme.onSurface;
        }),
        todayForegroundColor: WidgetStateProperty.all(colorScheme.primary),
        todayBackgroundColor: WidgetStateProperty.all(Colors.transparent),
        todayBorder: BorderSide(color: colorScheme.primary),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.lg),
        ),
      ),
      
      // Time picker theme
      timePickerTheme: TimePickerThemeData(
        backgroundColor: colorScheme.surface,
        dialBackgroundColor: colorScheme.surfaceVariant,
        dialHandColor: colorScheme.primary,
        dialTextColor: colorScheme.onSurface,
        entryModeIconColor: colorScheme.onSurfaceVariant,
        hourMinuteColor: colorScheme.surfaceVariant,
        hourMinuteTextColor: colorScheme.onSurfaceVariant,
        dayPeriodColor: colorScheme.surfaceVariant,
        dayPeriodTextColor: colorScheme.onSurfaceVariant,
        helpTextStyle: TextStyle(color: colorScheme.onSurface),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(vTheme.corners.lg),
        ),
      ),
      
      // Bottom sheet theme
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: colorScheme.surface,
        modalBackgroundColor: colorScheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(vTheme.corners.lg),
          ),
        ),
      ),
      
      // Search bar theme
      searchBarTheme: SearchBarThemeData(
        backgroundColor: WidgetStateProperty.all(colorScheme.surfaceVariant),
        overlayColor: WidgetStateProperty.all(colorScheme.surface),
        textStyle: WidgetStateProperty.all(
          TextStyle(color: colorScheme.onSurfaceVariant),
        ),
        hintStyle: WidgetStateProperty.all(
          TextStyle(color: colorScheme.onSurfaceVariant.withValues(alpha: 0.6)),
        ),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(vTheme.corners.lg),
          ),
        ),
      ),
      
      // Menu theme
      menuTheme: MenuThemeData(
        style: MenuStyle(
          backgroundColor: WidgetStateProperty.all(colorScheme.surface),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(vTheme.corners.md),
            ),
          ),
        ),
      ),
      
      // Badge theme
      badgeTheme: BadgeThemeData(
        backgroundColor: colorScheme.error,
        textColor: colorScheme.onError,
        smallSize: 6,
        largeSize: 16,
      ),
      
      // Banner theme
      bannerTheme: MaterialBannerThemeData(
        backgroundColor: colorScheme.surface,
        contentTextStyle: TextStyle(color: colorScheme.onSurface),
      ),
    );
  }
}

/// Extension to easily get ThemeData from VThemeData
extension VThemeDataExtension on VThemeData {
  /// Converts this VThemeData to Flutter's ThemeData
  ThemeData toThemeData() => VThemeIntegration.toThemeData(this);
  
  /// Converts this VThemeData's colors to Flutter's ColorScheme
  ColorScheme toColorScheme() => VThemeIntegration.toColorScheme(colors);
}
