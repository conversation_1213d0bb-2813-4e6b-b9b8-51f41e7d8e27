import 'package:flutter/material.dart';

/// A color scale representing different shades of a color from light to dark
class VColorScale {
  const VColorScale({
    required this.shade50,
    required this.shade100,
    required this.shade200,
    required this.shade300,
    required this.shade400,
    required this.shade500,
    required this.shade600,
    required this.shade700,
    required this.shade800,
    required this.shade900,
    this.shade950,
  });

  final Color shade50;
  final Color shade100;
  final Color shade200;
  final Color shade300;
  final Color shade400;
  final Color shade500; // Primary shade
  final Color shade600;
  final Color shade700;
  final Color shade800;
  final Color shade900;
  final Color? shade950;

  /// Primary color (shade 500)
  Color get primary => shade500;

  /// Light variant (shade 300)
  Color get light => shade300;

  /// Dark variant (shade 700)
  Color get dark => shade700;

  /// Very light background (shade 50)
  Color get background => shade50;

  /// Light background (shade 100)
  Color get backgroundLight => shade100;

  /// Hover state (shade 600)
  Color get hover => shade600;

  /// Active state (shade 700)
  Color get active => shade700;
}

/// Vertoie brand color scales based on the brand colors HTML file
class VBrandColors {
  VBrandColors._();

  /// Orange color scale - Primary brand color
  static const VColorScale orange = VColorScale(
    shade50: Color(0xFFFFF4F0),   // Orange 50 - Subtle backgrounds
    shade100: Color(0xFFFFE5D9),  // Orange 100 - Light backgrounds
    shade200: Color(0xFFFFCAB0),  // Orange 200 - Hover backgrounds
    shade300: Color(0xFFFFA87A),  // Orange 300 - Disabled states
    shade400: Color(0xFFFF8A56),  // Orange 400 - Secondary buttons
    shade500: Color(0xFFFF6B35),  // Orange 500 - Primary brand color
    shade600: Color(0xFFE65100),  // Orange 600 - Primary hover states
    shade700: Color(0xFFCC4700),  // Orange 700 - Active states
    shade800: Color(0xFFB33E00),  // Orange 800 - High contrast text
    shade900: Color(0xFF992E00),  // Orange 900 - Darkest orange
  );

  /// Amber color scale - Secondary brand color
  static const VColorScale amber = VColorScale(
    shade50: Color(0xFFFDF6E3),
    shade100: Color(0xFFFDF6E3),
    shade200: Color(0xFFFBBF24),
    shade300: Color(0xFFFBBF24),
    shade400: Color(0xFFFBBF24),
    shade500: Color(0xFFF7931E),  // Primary amber
    shade600: Color(0xFFD97706),
    shade700: Color(0xFFD97706),
    shade800: Color(0xFF92400E),
    shade900: Color(0xFF92400E),
  );

  /// Neutral gray color scale
  static const VColorScale neutral = VColorScale(
    shade50: Color(0xFFFAFAFA),
    shade100: Color(0xFFF4F4F5),
    shade200: Color(0xFFE4E4E7),
    shade300: Color(0xFFD4D4D8),
    shade400: Color(0xFFA1A1AA),
    shade500: Color(0xFF71717A),
    shade600: Color(0xFF52525B),
    shade700: Color(0xFF3F3F46),
    shade800: Color(0xFF27272A),
    shade900: Color(0xFF1F2937),
    shade950: Color(0xFF111827),
  );

  /// Dark mode neutral colors (inverted)
  static const VColorScale darkNeutral = VColorScale(
    shade50: Color(0xFF111827),   // Dark background
    shade100: Color(0xFF1F2937),  // Dark surface
    shade200: Color(0xFF374151),  // Dark border
    shade300: Color(0xFF4B5563),  // Dark muted
    shade400: Color(0xFF6B7280),  // Dark text secondary
    shade500: Color(0xFF9CA3AF),  // Dark text primary
    shade600: Color(0xFFD1D5DB),  // Light text secondary
    shade700: Color(0xFFE5E7EB),  // Light text primary
    shade800: Color(0xFFF3F4F6),  // Light surface
    shade900: Color(0xFFFAFAFA),  // Light background
    shade950: Color(0xFFFFFFFF),  // Pure white
  );
}
