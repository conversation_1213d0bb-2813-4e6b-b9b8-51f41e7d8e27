# Vertoie UI Theme System Guide

## Overview

The Vertoie UI theme system provides a comprehensive, configurable foundation for building consistent user interfaces. It integrates your brand colors and offers multiple theme variants for different use cases.

## Brand Colors Integration

### Color Scales

The theme system uses your official brand colors from `brand/colors/vertoie_brand_colors.html`:

#### Orange Scale (Primary)
- **Orange 50** (`#FFF4F0`) - Subtle backgrounds
- **Orange 100** (`#FFE5D9`) - Light backgrounds  
- **Orange 200** (`#FFCAB0`) - Hover backgrounds
- **Orange 300** (`#FFA87A`) - Disabled states
- **Orange 400** (`#FF8A56`) - Secondary buttons
- **Orange 500** (`#FF6B35`) - **Primary brand color**
- **Orange 600** (`#E65100`) - Primary hover states
- **Orange 700** (`#CC4700`) - Active states
- **Orange 800** (`#B33E00`) - High contrast text
- **Orange 900** (`#992E00`) - Darkest orange

#### Amber Scale (Secondary)
- **Amber 500** (`#F7931E`) - Secondary brand color
- **Amber 600** (`#D97706`) - Secondary hover states

#### Neutral Scale
- **Gray 50** (`#FAFAFA`) - Page backgrounds
- **Gray 100** (`#F4F4F5`) - Subtle backgrounds
- **Gray 200** (`#E4E4E7`) - Light borders
- **Gray 300** (`#D4D4D8`) - Borders, dividers
- **Gray 400** (`#A1A1AA`) - Placeholders
- **Gray 500** (`#71717A`) - Muted text
- **Gray 600** (`#52525B`) - Secondary text
- **Gray 700** (`#3F3F46`) - Body text
- **Gray 800** (`#27272A`) - Headings
- **Gray 900** (`#1F2937`) - Primary text, logo
- **Gray 950** (`#111827`) - Darkest text

#### Semantic Colors
- **Success**: `#22C55E` (Green)
- **Warning**: `#F59E0B` (Amber)
- **Error**: `#EF4444` (Red)
- **Info**: `#3B82F6` (Blue)

## Theme Variants

### 1. Default Theme (`VThemeData.defaultTheme()`)
- Uses your brand orange as primary color
- Warm white backgrounds
- Balanced spacing and subtle shadows
- **Best for**: General application use

### 2. Brand Showcase (`VThemeData.brand()`)
- Emphasizes brand colors with rounded corners
- Elevated shadows for depth
- Expressive animations
- **Best for**: Marketing pages, landing screens

### 3. Light Theme (`VThemeData.light()`)
- Lighter color variants for bright environments
- Reduced contrast for comfortable viewing
- **Best for**: Outdoor use, bright office environments

### 4. Compact Theme (`VThemeData.compact()`)
- Tight spacing for dense interfaces
- Sharp corners for efficiency
- Flat design with minimal shadows
- **Best for**: Data-heavy applications, dashboards

### 5. Relaxed Theme (`VThemeData.relaxed()`)
- Spacious padding for accessibility
- Rounded corners for friendliness
- Elevated shadows for clarity
- **Best for**: Accessibility-focused applications

### 6. High Contrast (`VThemeData.highContrast()`)
- Maximum contrast for accessibility
- Darker color variants
- Sharp corners for clarity
- **Best for**: Users with visual impairments

## Usage Examples

### Basic Theme Setup

```dart
import 'package:vertoie_ui/vertoie_ui.dart';

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: VThemeProvider(
        theme: VThemeData.defaultTheme(),
        child: MyHomePage(),
      ),
    );
  }
}
```

### Accessing Theme Colors

```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = VThemeProvider.of(context);
    
    return Container(
      color: theme.colors.primary,
      child: Text(
        'Hello World',
        style: TextStyle(color: theme.colors.onPrimary),
      ),
    );
  }
}
```

### Using Brand Color Scales

```dart
// Access specific shades
final orangeLight = theme.colors.orange.shade100;
final orangePrimary = theme.colors.orange.primary; // Same as shade500
final orangeDark = theme.colors.orange.shade700;

// Use semantic color helpers
final primaryHover = theme.colors.orange.hover; // shade600
final primaryActive = theme.colors.orange.active; // shade700
final lightBackground = theme.colors.orange.background; // shade50
```

### Custom Theme Creation

```dart
final customTheme = VThemeData(
  spacing: VSpacing.relaxed,
  corners: VCorners.rounded,
  elevation: VElevation.elevated,
  density: VComponentDensity.comfortable,
  motion: VMotion.subtle,
  colors: VColors.defaultColors(),
  // Theme scale selections
  spacingScale: VSpacingScale.relaxed,
  cornerStyle: VCornerStyle.rounded,
  elevationLevel: VElevationLevel.elevated,
  densityLevel: VDensity.comfortable,
  motionStyle: VMotionStyle.subtle,
);
```

## Design Tokens

### Spacing Scale
- **Tight**: Compact spacing for dense layouts
- **Default**: Balanced spacing for general use
- **Relaxed**: Generous spacing for accessibility

### Corner Styles
- **Sharp**: No border radius (0px)
- **Subtle**: Small radius (4-12px)
- **Rounded**: Medium radius (8-16px)

### Elevation Levels
- **Flat**: No shadows
- **Subtle**: Light shadows (1px blur)
- **Elevated**: Medium shadows (4-6px blur)
- **Floating**: Strong shadows (10-20px blur)

### Component Density
- **Compact**: Height 32px, Padding 8px
- **Comfortable**: Height 40px, Padding 12px
- **Spacious**: Height 48px, Padding 16px

### Motion Styles
- **None**: No animations
- **Subtle**: 150ms ease-in-out
- **Expressive**: 300ms elastic-out

## Best Practices

1. **Consistency**: Use the same theme throughout your application
2. **Accessibility**: Consider high-contrast themes for better accessibility
3. **Context**: Choose appropriate themes for different environments
4. **Brand Alignment**: Use brand colors consistently across components
5. **Performance**: Avoid frequent theme switching for better performance

## Migration from Material Design

The Vertoie theme system is designed to work alongside Material Design. You can gradually migrate components while maintaining consistency:

```dart
// Material + Vertoie hybrid
MaterialApp(
  theme: ThemeData(
    primarySwatch: MaterialColor(0xFFFF6B35, {
      50: Color(0xFFFFF4F0),
      500: Color(0xFFFF6B35),
      // ... other shades
    }),
  ),
  home: VThemeProvider(
    theme: VThemeData.defaultTheme(),
    child: MyApp(),
  ),
)
```
