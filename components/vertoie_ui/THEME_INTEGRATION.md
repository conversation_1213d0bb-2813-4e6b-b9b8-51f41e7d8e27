# Vertoie UI Theme Integration

This document explains how Vertoie UI's custom theme system integrates with Flutter's Material Design theme system.

## Overview

Vertoie UI now provides seamless integration with Flutter's `ThemeData` and `ColorScheme`, ensuring that all Material Design components throughout your app use your custom Vertoie colors consistently.

## Key Features

- **Complete Color Override**: All 45+ Material Design 3 color roles are mapped to your Vertoie brand colors
- **Automatic Component Theming**: All Flutter Material components (buttons, inputs, cards, etc.) automatically use your theme
- **Dynamic Theme Switching**: Support for light/dark mode and real-time theme updates
- **Component-Specific Styling**: Pre-configured styles for all major Material components

## Usage

### Basic Setup

```dart
import 'package:flutter/material.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final vTheme = VThemeData.defaultTheme();
    
    return MaterialApp(
      title: 'My App',
      theme: vTheme.toThemeData(), // Convert to Flutter ThemeData
      home: VThemeProvider(
        theme: vTheme,
        child: MyHomePage(),
      ),
    );
  }
}
```

### Dynamic Theme Changes

```dart
class MyApp extends StatefulWidget {
  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  bool isDarkMode = false;
  VCornerStyle cornerStyle = VCornerStyle.subtle;
  
  VThemeData get currentTheme {
    return VThemeData(
      isDark: isDarkMode,
      cornerStyle: cornerStyle,
      // ... other theme properties
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'My App',
      theme: currentTheme.toThemeData(), // Automatically updates
      home: VThemeProvider(
        theme: currentTheme,
        child: MyHomePage(),
      ),
    );
  }
}
```

### Light and Dark Theme Support

```dart
MaterialApp(
  theme: VThemeData.defaultTheme().toThemeData(),
  darkTheme: VThemeData.dark().toThemeData(),
  themeMode: ThemeMode.system, // Respects system preference
  home: MyHomePage(),
)
```

## Color Mapping

The integration maps your Vertoie colors to Material Design 3 color roles:

### Primary Colors
- `primary` → Your `VColors.primary` (Orange 700)
- `onPrimary` → Your `VColors.onPrimary` (White)
- `primaryContainer` → Orange 200
- `onPrimaryContainer` → Neutral 800

### Secondary Colors
- `secondary` → Your `VColors.secondary` (Amber 700)
- `onSecondary` → Your `VColors.onSecondary` (White)
- `secondaryContainer` → Amber 200
- `onSecondaryContainer` → Neutral 800

### Surface Colors
- `surface` → Your `VColors.surface`
- `onSurface` → Your `VColors.onSurface`
- `surfaceVariant` → Your `VColors.surfaceVariant`
- `surfaceContainer*` → Mapped to your neutral color scale

### Semantic Colors
- `error` → Your `VColors.error`
- `onError` → Your `VColors.onError`
- And so on for warning, success, info...

## Pre-Configured Components

The integration automatically styles these Material components:

- **Buttons**: ElevatedButton, OutlinedButton, TextButton
- **Inputs**: TextField, FormField with proper validation colors
- **Selection**: Checkbox, Radio, Switch, Slider
- **Feedback**: ProgressIndicator, SnackBar, Tooltip
- **Navigation**: BottomNavigationBar, NavigationBar, NavigationRail, TabBar
- **Containment**: Card, Dialog, BottomSheet
- **Pickers**: DatePicker, TimePicker (automatically themed)
- **Lists**: ListTile with proper selection colors
- **Layout**: Divider, Badge, Chip

## Migration from Manual Theming

If you previously had components with manual theme overrides (like in `VDatePicker`), you can now remove those overrides:

### Before (Manual Override)
```dart
showDatePicker(
  context: context,
  builder: (context, child) {
    return Theme(
      data: Theme.of(context).copyWith(
        colorScheme: Theme.of(context).colorScheme.copyWith(
          primary: colors.orange.primary,
          // ... many manual color assignments
        ),
      ),
      child: child!,
    );
  },
)
```

### After (Automatic)
```dart
showDatePicker(
  context: context,
  // No manual theming needed!
)
```

## Advanced Customization

### Component-Specific Overrides

If you need to override specific component themes:

```dart
MaterialApp(
  theme: currentTheme.toThemeData().copyWith(
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        // Custom overrides
      ),
    ),
  ),
)
```

### Material Component Theme Builder

For complex Material components that need special handling:

```dart
VThemeIntegration.createMaterialComponentTheme(context, vTheme)
```

This provides additional theming for components like DatePicker, TimePicker, etc.

## Benefits

1. **Consistency**: All Material components use your brand colors
2. **Maintainability**: Single source of truth for colors
3. **Performance**: No redundant theme building
4. **Compatibility**: Works with all Flutter Material components
5. **Future-Proof**: Automatically supports new Material components

## Best Practices

1. Always use `vTheme.toThemeData()` for MaterialApp theme
2. Keep VThemeProvider as a child of MaterialApp for custom components
3. Use semantic color properties instead of direct color values
4. Test both light and dark themes
5. Validate color contrast ratios for accessibility

## Example

See the example app in `/example` for a complete implementation showing:
- Dynamic theme switching
- Light/dark mode support
- All component types using consistent theming
- Real-time theme updates
