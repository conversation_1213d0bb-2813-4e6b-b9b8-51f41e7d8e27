import 'package:flutter/material.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

// Sample data class for data table demo
class Employee {
  const Employee({
    required this.id,
    required this.name,
    required this.email,
    required this.department,
    required this.position,
    required this.salary,
    required this.joinDate,
    required this.isActive,
  });

  final int id;
  final String name;
  final String email;
  final String department;
  final String position;
  final int salary;
  final DateTime joinDate;
  final bool isActive;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Employee && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

void main() {
  runApp(const VertoieUIExample());
}

class VertoieUIExample extends StatelessWidget {
  const VertoieUIExample({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Vertoie UI Example',
      home: const ExampleHomePage(),
    );
  }
}

class ExampleHomePage extends StatefulWidget {
  const ExampleHomePage({super.key});

  @override
  State<ExampleHomePage> createState() => _ExampleHomePageState();
}

class _ExampleHomePageState extends State<ExampleHomePage> {
  // Individual theme configuration options
  bool isDarkMode = false;
  VCornerStyle cornerStyle = VCornerStyle.subtle;
  VElevationLevel elevationLevel = VElevationLevel.subtle;
  VDensity densityLevel = VDensity.comfortable;
  VMotionStyle motionStyle = VMotionStyle.subtle;

  // Dropdown example state
  String? selectedCountry;
  String? selectedPriority;

  // Checkbox example state
  bool singleCheckbox = false;
  bool tristateCheckbox = false;
  bool? indeterminateCheckbox;
  List<String> selectedFeatures = ['feature1'];
  List<String> selectedNotifications = [];

  // Radio example state
  String? selectedSize = 'medium';
  String? selectedPayment;

  // Switch example state
  bool notificationsEnabled = true;
  bool darkModeSwitch = false;
  bool autoSaveEnabled = false;

  // Slider example state
  double volumeLevel = 0.7;
  double brightnessLevel = 0.5;
  RangeValues priceRange = const RangeValues(20, 80);
  double temperatureRange = 22.0;

  // Date & Time picker example state
  DateTime? selectedDate;
  VDateRange? selectedDateRange;
  TimeOfDay? selectedTime;
  DateTime? selectedDateTime;

  // Interactive Calendar Demo - Sample Events with Advanced Color Coding
  List<VCalendarEvent> calendarEvents = [
    // TODAY - Current work
    VCalendarEvent(
      id: '1',
      title: '🚀 Team Standup',
      date: DateTime.now(),
      description: 'Daily team sync - sprint review',
      startTime: const TimeOfDay(hour: 9, minute: 0),
      endTime: const TimeOfDay(hour: 9, minute: 30),
      status: VEventStatus.confirmed,
      category: VEventCategory.meeting,
      clientId: 'team_internal',
      priority: 2,
      tags: ['daily', 'team', 'standup'],
    ),
    VCalendarEvent(
      id: '2',
      title: '💼 Client Presentation',
      date: DateTime.now(),
      description: 'Q2 Progress Review for Enterprise Client',
      startTime: const TimeOfDay(hour: 14, minute: 0),
      endTime: const TimeOfDay(hour: 15, minute: 30),
      status: VEventStatus.confirmed,
      category: VEventCategory.appointment,
      clientId: 'enterprise_corp',
      priority: 1,
      tags: ['client', 'presentation', 'q2'],
    ),
    
    // TOMORROW - Mix of confirmed and tentative
    VCalendarEvent(
      id: '3',
      title: '🔧 Code Review Session',
      date: DateTime.now().add(const Duration(days: 1)),
      startTime: const TimeOfDay(hour: 10, minute: 0),
      endTime: const TimeOfDay(hour: 11, minute: 30),
      status: VEventStatus.confirmed,
      category: VEventCategory.work,
      clientId: 'tech_team',
      priority: 2,
      tags: ['code-review', 'development'],
    ),
    VCalendarEvent(
      id: '4',
      title: '❓ Maybe: Coffee with Sarah',
      date: DateTime.now().add(const Duration(days: 1)),
      startTime: const TimeOfDay(hour: 15, minute: 0),
      endTime: const TimeOfDay(hour: 16, minute: 0),
      status: VEventStatus.tentative,
      category: VEventCategory.personal,
      priority: 4,
      tags: ['coffee', 'networking'],
    ),
    
    // THIS WEEK - Various statuses and categories
    VCalendarEvent(
      id: '5',
      title: '✈️ Business Trip to Seattle',
      date: DateTime.now().add(const Duration(days: 3)),
      isAllDay: true,
      status: VEventStatus.confirmed,
      category: VEventCategory.travel,
      clientId: 'northwest_branch',
      priority: 1,
      tags: ['travel', 'business', 'seattle'],
    ),
    VCalendarEvent(
      id: '6',
      title: '🎯 Project Alpha Deadline',
      date: DateTime.now().add(const Duration(days: 4)),
      isAllDay: true,
      status: VEventStatus.inProgress,
      category: VEventCategory.work,
      clientId: 'project_alpha',
      priority: 1,
      tags: ['deadline', 'urgent', 'alpha'],
    ),
    VCalendarEvent(
      id: '7',
      title: '🏖️ Team Offsite - Lake Retreat',
      date: DateTime.now().add(const Duration(days: 6)),
      isAllDay: true,
      status: VEventStatus.confirmed,
      category: VEventCategory.personal,
      priority: 3,
      tags: ['team-building', 'offsite', 'retreat'],
    ),
    
    // NEXT WEEK - More variety
    VCalendarEvent(
      id: '8',
      title: '🎄 Company Holiday Party',
      date: DateTime.now().add(const Duration(days: 10)),
      startTime: const TimeOfDay(hour: 18, minute: 0),
      endTime: const TimeOfDay(hour: 22, minute: 0),
      status: VEventStatus.confirmed,
      category: VEventCategory.holiday,
      priority: 3,
      tags: ['holiday', 'company', 'party'],
    ),
    VCalendarEvent(
      id: '9',
      title: '🔴 OVERDUE: Client Follow-up',
      date: DateTime.now().subtract(const Duration(days: 2)),
      startTime: const TimeOfDay(hour: 10, minute: 0),
      endTime: const TimeOfDay(hour: 11, minute: 0),
      status: VEventStatus.overdue,
      category: VEventCategory.appointment,
      clientId: 'delayed_client',
      priority: 1,
      tags: ['overdue', 'follow-up', 'urgent'],
    ),
    VCalendarEvent(
      id: '10',
      title: '✅ Completed: Sprint Planning',
      date: DateTime.now().subtract(const Duration(days: 1)),
      startTime: const TimeOfDay(hour: 9, minute: 0),
      endTime: const TimeOfDay(hour: 11, minute: 0),
      status: VEventStatus.completed,
      category: VEventCategory.meeting,
      clientId: 'development_team',
      priority: 2,
      tags: ['sprint', 'planning', 'completed'],
    ),
    VCalendarEvent(
      id: '11',
      title: '❌ CANCELLED: External Meeting',
      date: DateTime.now().add(const Duration(days: 2)),
      startTime: const TimeOfDay(hour: 13, minute: 0),
      endTime: const TimeOfDay(hour: 14, minute: 0),
      status: VEventStatus.cancelled,
      category: VEventCategory.meeting,
      clientId: 'external_partner',
      priority: 3,
      tags: ['cancelled', 'external'],
    ),
    
    // Different clients for color coding demo
    VCalendarEvent(
      id: '12',
      title: '💡 Innovation Workshop',
      date: DateTime.now().add(const Duration(days: 7)),
      startTime: const TimeOfDay(hour: 9, minute: 0),
      endTime: const TimeOfDay(hour: 17, minute: 0),
      status: VEventStatus.confirmed,
      category: VEventCategory.work,
      clientId: 'innovation_lab',
      priority: 2,
      tags: ['innovation', 'workshop', 'full-day'],
    ),
    VCalendarEvent(
      id: '13',
      title: '🏥 Doctor Appointment',
      date: DateTime.now().add(const Duration(days: 8)),
      startTime: const TimeOfDay(hour: 14, minute: 30),
      endTime: const TimeOfDay(hour: 15, minute: 30),
      status: VEventStatus.confirmed,
      category: VEventCategory.personal,
      priority: 2,
      tags: ['health', 'appointment'],
    ),
  ];

  // Interactive Calendar Configuration Demo
  DateTime? selectedDemoDate;
  VCalendarView currentDemoView = VCalendarView.month;
  bool showNavigationControls = true;
  bool showViewSwitcher = true;
  int firstDayOfWeek = VCalendarWeekStart.monday;

  // Color Coding Configuration Demo
  bool useStatusColors = true;
  bool useCategoryColors = true;
  bool useClientColors = false;

  // Availability Management Configuration Demo
  bool enableAvailabilityManagement = true;
  bool showAvailabilityOverlay = true;
  int bufferMinutes = 15;
  int minimumBookingNotice = 2; // hours
  
  // Time Slot Configuration Demo (for Week/Day views)
  double slotHeight = 60.0;
  int startHour = 8;
  int endHour = 18;
  bool showTimeLabels = true;
  bool showMinorGridLines = true;
  bool showNowIndicator = true;
  
  // Hour spread presets
  String selectedHourSpread = 'Business Hours'; // Default
  final Map<String, Map<String, int>> hourSpreadPresets = {
    'Full Day': {'start': 0, 'end': 24},
    'Extended Business': {'start': 6, 'end': 22},
    'Business Hours': {'start': 8, 'end': 18},
    'Morning Schedule': {'start': 6, 'end': 14},
    'Afternoon Schedule': {'start': 12, 'end': 20},
    'Evening Schedule': {'start': 16, 'end': 24},
    'Short Day': {'start': 9, 'end': 17},
  };
  
  // Sample business hours (Monday to Friday 9 AM - 5 PM)
  late Map<int, VBusinessHours> businessHours;
  
  // Sample time blocks (holidays, blocked times)
  late List<VTimeBlock> timeBlocks;

  @override
  void initState() {
    super.initState();
    
    // Initialize business hours
    businessHours = {
      DateTime.monday: const VBusinessHours(
        dayOfWeek: DateTime.monday,
        isOpen: true,
        openTime: TimeOfDay(hour: 9, minute: 0),
        closeTime: TimeOfDay(hour: 17, minute: 0),
        breakStart: TimeOfDay(hour: 12, minute: 0),
        breakEnd: TimeOfDay(hour: 13, minute: 0),
      ),
      DateTime.tuesday: const VBusinessHours(
        dayOfWeek: DateTime.tuesday,
        isOpen: true,
        openTime: TimeOfDay(hour: 9, minute: 0),
        closeTime: TimeOfDay(hour: 17, minute: 0),
        breakStart: TimeOfDay(hour: 12, minute: 0),
        breakEnd: TimeOfDay(hour: 13, minute: 0),
      ),
      DateTime.wednesday: const VBusinessHours(
        dayOfWeek: DateTime.wednesday,
        isOpen: true,
        openTime: TimeOfDay(hour: 9, minute: 0),
        closeTime: TimeOfDay(hour: 17, minute: 0),
        breakStart: TimeOfDay(hour: 12, minute: 0),
        breakEnd: TimeOfDay(hour: 13, minute: 0),
      ),
      DateTime.thursday: const VBusinessHours(
        dayOfWeek: DateTime.thursday,
        isOpen: true,
        openTime: TimeOfDay(hour: 9, minute: 0),
        closeTime: TimeOfDay(hour: 17, minute: 0),
        breakStart: TimeOfDay(hour: 12, minute: 0),
        breakEnd: TimeOfDay(hour: 13, minute: 0),
      ),
      DateTime.friday: const VBusinessHours(
        dayOfWeek: DateTime.friday,
        isOpen: true,
        openTime: TimeOfDay(hour: 9, minute: 0),
        closeTime: TimeOfDay(hour: 17, minute: 0),
        breakStart: TimeOfDay(hour: 12, minute: 0),
        breakEnd: TimeOfDay(hour: 13, minute: 0),
      ),
      DateTime.saturday: const VBusinessHours(
        dayOfWeek: DateTime.saturday,
        isOpen: false,
      ),
      DateTime.sunday: const VBusinessHours(
        dayOfWeek: DateTime.sunday,
        isOpen: false,
      ),
    };
    
    // Initialize time blocks with dramatic visual examples
    timeBlocks = [
      // WEEKEND BLOCK - Saturday and Sunday should be clearly unavailable
      VTimeBlock(
        id: 'weekend_sat',
        start: DateTime.now().add(Duration(days: (DateTime.saturday - DateTime.now().weekday) % 7)),
        end: DateTime.now().add(Duration(days: (DateTime.saturday - DateTime.now().weekday) % 7)).copyWith(hour: 23, minute: 59),
        type: VTimeBlockType.blocked,
        title: 'Weekend - Office Closed',
        description: 'No business operations on weekends',
        isRecurring: true,
        recurrencePattern: 'weekly',
      ),
      VTimeBlock(
        id: 'weekend_sun',
        start: DateTime.now().add(Duration(days: (DateTime.sunday - DateTime.now().weekday) % 7)),
        end: DateTime.now().add(Duration(days: (DateTime.sunday - DateTime.now().weekday) % 7)).copyWith(hour: 23, minute: 59),
        type: VTimeBlockType.blocked,
        title: 'Weekend - Office Closed',
        description: 'No business operations on weekends',
        isRecurring: true,
        recurrencePattern: 'weekly',
      ),
      
      // HOLIDAY BLOCK - Independence Day (show dramatic red blocking)
      VTimeBlock(
        id: 'holiday_july4',
        start: DateTime.now().add(const Duration(days: 14)),
        end: DateTime.now().add(const Duration(days: 14)).copyWith(hour: 23, minute: 59),
        type: VTimeBlockType.holiday,
        title: '🇺🇸 Independence Day',
        description: 'National Holiday - Office Closed',
      ),
      
      // BLOCKED TIME - All-hands meeting (show amber blocking)
      VTimeBlock(
        id: 'blocked_allhands',
        start: DateTime.now().add(const Duration(days: 7)).copyWith(hour: 14, minute: 0),
        end: DateTime.now().add(const Duration(days: 7)).copyWith(hour: 16, minute: 0),
        type: VTimeBlockType.blocked,
        title: '🏢 All-Hands Meeting',
        description: 'Company-wide meeting - no external appointments',
      ),
      
      // LUNCH BREAK BLOCK - Daily recurring (show subtle amber)
      VTimeBlock(
        id: 'lunch_break',
        start: DateTime.now().copyWith(hour: 12, minute: 0),
        end: DateTime.now().copyWith(hour: 13, minute: 0),
        type: VTimeBlockType.break_,
        title: '🍽️ Lunch Break',
        description: 'Daily lunch break - unavailable for meetings',
        isRecurring: true,
        recurrencePattern: 'daily',
      ),
      
      // BUFFER TIME - After important meetings
      VTimeBlock(
        id: 'buffer_after_client',
        start: DateTime.now().copyWith(hour: 15, minute: 30),
        end: DateTime.now().copyWith(hour: 16, minute: 0),
        type: VTimeBlockType.buffer,
        title: '⏰ Buffer Time',
        description: 'Buffer time after client presentation',
      ),
      
      // VACATION BLOCK - Future vacation days
      VTimeBlock(
        id: 'vacation_week',
        start: DateTime.now().add(const Duration(days: 21)),
        end: DateTime.now().add(const Duration(days: 25)),
        type: VTimeBlockType.blocked,
        title: '🏖️ Vacation Week',
        description: 'Out of office - personal vacation',
      ),
    ];
  }

  // Data Table Demo - Sample Data
  List<Employee> employees = [
    Employee(
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      department: 'Engineering',
      position: 'Senior Developer',
      salary: 85000,
      joinDate: DateTime(2022, 3, 15),
      isActive: true,
    ),
    Employee(
      id: 2,
      name: 'Jane Smith',
      email: '<EMAIL>',
      department: 'Design',
      position: 'UI/UX Designer',
      salary: 72000,
      joinDate: DateTime(2021, 8, 22),
      isActive: true,
    ),
    Employee(
      id: 3,
      name: 'Bob Johnson',
      email: '<EMAIL>',
      department: 'Marketing',
      position: 'Marketing Manager',
      salary: 68000,
      joinDate: DateTime(2020, 11, 5),
      isActive: true,
    ),
    Employee(
      id: 4,
      name: 'Alice Wilson',
      email: '<EMAIL>',
      department: 'Engineering',
      position: 'Team Lead',
      salary: 95000,
      joinDate: DateTime(2019, 4, 10),
      isActive: true,
    ),
    Employee(
      id: 5,
      name: 'Charlie Brown',
      email: '<EMAIL>',
      department: 'Sales',
      position: 'Sales Representative',
      salary: 55000,
      joinDate: DateTime(2023, 1, 20),
      isActive: true,
    ),
    Employee(
      id: 6,
      name: 'Diana Prince',
      email: '<EMAIL>',
      department: 'HR',
      position: 'HR Manager',
      salary: 75000,
      joinDate: DateTime(2021, 6, 12),
      isActive: false,
    ),
    Employee(
      id: 7,
      name: 'Edward Miller',
      email: '<EMAIL>',
      department: 'Engineering',
      position: 'Junior Developer',
      salary: 62000,
      joinDate: DateTime(2023, 9, 8),
      isActive: true,
    ),
    Employee(
      id: 8,
      name: 'Fiona Green',
      email: '<EMAIL>',
      department: 'Design',
      position: 'Graphic Designer',
      salary: 58000,
      joinDate: DateTime(2022, 12, 3),
      isActive: true,
    ),
  ];

  // Data Table Demo State
  VColumnSort? currentSort;
  VRowSelection<Employee> currentSelection = const VRowSelection<Employee>();
  bool tableLoading = false;
  bool tableSelectable = true;
  bool tableSortable = true;
  bool tablePaginated = true;
  bool tableShowBorders = true;
  bool tableAlternateRows = true;
  bool tableSearchable = true; // Search functionality toggle
  int tablePageSize = 5;
  int tableCurrentPage = 0;
  String tableSearchQuery = ''; // Search functionality

  VThemeData get currentTheme {
    return VThemeData(
      cornerStyle: cornerStyle,
      elevationLevel: elevationLevel,
      density: densityLevel,
      motionStyle: motionStyle,
      isDark: isDarkMode,
    );
  }

  // Event Modal Methods
  // Event Modal Methods
  Future<void> _showEventModal([VCalendarEvent? event]) async {
    try {
      print('🟦 _showEventModal called with event: ${event?.title ?? 'null'}');
      
      final result = await showDialog<VEventModalResult>(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          print('🟦 Building VEventModal dialog...');
          return VEventModal(
            event: event,
            onSave: (result) {
              print('🟩 VEventModal onSave called with: ${result.operation}');
              Navigator.of(context).pop(result);
            },
            onDelete: (result) {
              print('🟥 VEventModal onDelete called with: ${result.operation}');
              Navigator.of(context).pop(result);
            },
          );
        },
      );

      print('🟦 Dialog returned with result: $result');
      if (result != null) {
        print('🟦 About to handle event operation: ${result.operation}');
        await _handleEventOperation(result);
        print('🟩 Event operation completed successfully');
      } else {
        print('🟨 Dialog was cancelled/dismissed');
      }
    } catch (e, stackTrace) {
      print('🟥 Error in _showEventModal: $e');
      print('🟥 Stack trace: $stackTrace');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _handleEventOperation(VEventModalResult result) async {
    try {
      print('🟦 _handleEventOperation called with: ${result.operation}');
      
      switch (result.operation) {
        case VEventOperation.delete:
          print('🟥 Handling DELETE operation');
          if (result.originalEvent != null) {
            setState(() {
              calendarEvents.removeWhere((e) => e.id == result.originalEvent!.id);
            });
            
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Event "${result.originalEvent!.title}" deleted'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
          break;
          
        case VEventOperation.create:
          print('🟩 Handling CREATE operation');
          if (result.event != null) {
            print('🟩 Adding event: ${result.event!.title}');
            print('🟩 Current events count: ${calendarEvents.length}');
            
            // Ensure we're still mounted before calling setState
            if (mounted) {
              print('🟩 About to call setState...');
              
              // Try creating a new list instead of modifying existing one
              final newEventsList = List<VCalendarEvent>.from(calendarEvents);
              newEventsList.add(result.event!);
              
              setState(() {
                calendarEvents = newEventsList;
              });
              
              print('🟩 setState completed. New count: ${calendarEvents.length}');
              print('🟩 CREATE operation completed - no async operations');
            } else {
              print('🟥 Widget not mounted, skipping setState');
            }
          } else {
            print('🟥 CREATE operation failed - no event provided');
          }
          break;
          
        case VEventOperation.update:
          print('🟨 Handling UPDATE operation');
          if (result.event != null && result.originalEvent != null) {
            final existingIndex = calendarEvents.indexWhere((e) => e.id == result.originalEvent!.id);
            if (existingIndex >= 0) {
              setState(() {
                calendarEvents[existingIndex] = result.event!;
              });
              
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('Event updated'),
                    backgroundColor: Colors.blue,
                  ),
                );
              }
              print('🟨 UPDATE operation completed');
            }
          }
          break;
          
        case VEventOperation.duplicate:
          print('🟪 Handling DUPLICATE operation');
          if (result.event != null) {
            final duplicatedEvent = VCalendarEvent(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              title: '${result.event!.title} (Copy)',
              description: result.event!.description,
              date: result.event!.date,
              startTime: result.event!.startTime,
              endTime: result.event!.endTime,
              status: result.event!.status,
              category: result.event!.category,
              clientId: result.event!.clientId,
              priority: result.event!.priority,
              tags: [...result.event!.tags],
              location: result.event!.location,
              attendees: [...result.event!.attendees],
              customFields: Map<String, dynamic>.from(result.event!.customFields ?? {}),
            );
            
            setState(() {
              calendarEvents.add(duplicatedEvent);
            });
            
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Event duplicated'),
                  backgroundColor: Colors.purple,
                ),
              );
            }
            print('🟪 DUPLICATE operation completed');
          }
          break;
      }
      print('🟩 _handleEventOperation completed successfully');
      print('🟩 Widget mounted: $mounted');
      print('🟩 Context: ${context.mounted}');
    } catch (e, stackTrace) {
      print('🟥 Error in _handleEventOperation: $e');
      print('🟥 Stack trace: $stackTrace');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error handling event: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // ...existing code...

  @override
  Widget build(BuildContext context) {
    print('🔄 Widget build() called with ${calendarEvents.length} events');
    return VThemeProvider(
      theme: currentTheme,
      child: Theme(
        data: currentTheme.toThemeData(),
        child: Scaffold(
          backgroundColor: currentTheme.colors.background,
          appBar: AppBar(
            title: VText(
              'Vertoie UI Components',
              style: TextStyle(
                color: currentTheme.colors.onSurface,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: currentTheme.colors.surface,
            elevation: 0,
          ),
        body: SingleChildScrollView(
          padding: EdgeInsets.all(currentTheme.componentDensity.lg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Theme Configuration Section
              VCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    VText(
                      'Theme Configuration',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: currentTheme.colors.onSurface,
                      ),
                    ),
                    SizedBox(height: currentTheme.componentDensity.md),

                    // Color Mode Toggle
                    VSwitch(
                      value: isDarkMode,
                      onChanged: (value) {
                        setState(() {
                          isDarkMode = value;
                        });
                      },
                      label: 'Color Mode: ${isDarkMode ? 'Dark' : 'Light'}',
                    ),

                    SizedBox(height: currentTheme.componentDensity.sm),

                    // Density Scale
                    VDropdown<VDensity>(
                      label: 'Density',
                      value: densityLevel,
                      items: VDensity.values.map((density) => VDropdownItem(
                        value: density,
                        child: Text(density.name),
                      )).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            densityLevel = value;
                          });
                        }
                      },
                    ),

                    SizedBox(height: currentTheme.componentDensity.md),

                    // Corner Style
                    VDropdown<VCornerStyle>(
                      label: 'Corners',
                      value: cornerStyle,
                      items: VCornerStyle.values.map((corner) => VDropdownItem(
                        value: corner,
                        child: Text(corner.name),
                      )).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            cornerStyle = value;
                          });
                        }
                      },
                    ),

                    SizedBox(height: currentTheme.componentDensity.md),

                    // Elevation Level
                    VDropdown<VElevationLevel>(
                      label: 'Elevation',
                      value: elevationLevel,
                      items: VElevationLevel.values.map((elevation) => VDropdownItem(
                        value: elevation,
                        child: Text(elevation.name),
                      )).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            elevationLevel = value;
                          });
                        }
                      },
                    ),

                    SizedBox(height: currentTheme.componentDensity.md),

                    // Motion Style
                    VDropdown<VMotionStyle>(
                      label: 'Motion',
                      value: motionStyle,
                      items: VMotionStyle.values.map((motion) => VDropdownItem(
                        value: motion,
                        child: Text(motion.name),
                      )).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            motionStyle = value;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: currentTheme.componentDensity.xl),

              // Dialog Section
              VCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    VText(
                      'Dialogs',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: currentTheme.colors.onSurface,
                      ),
                    ),
                    SizedBox(height: currentTheme.componentDensity.lg),
                    
                    VText(
                      'Test dialogs with different themes and configurations.',
                      style: TextStyle(
                        color: currentTheme.colors.onSurface,
                        fontSize: 14,
                      ),
                    ),
                    SizedBox(height: currentTheme.componentDensity.md),
                    
                    Wrap(
                      spacing: currentTheme.componentDensity.md,
                      runSpacing: currentTheme.componentDensity.md,
                      children: [
                        VButton(
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (_) => VThemeProvider(
                                theme: currentTheme,
                                child: VDialog(
                                  title: 'Simple Dialog',
                                  content: 'This dialog follows the current theme configuration.',
                                  actions: [
                                    VDialogAction(
                                      label: 'Close',
                                      onPressed: () => Navigator.of(context).pop(),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                          child: Text('Show Dialog'),
                        ),
                        VButton(
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (_) => VThemeProvider(
                                theme: currentTheme,
                                child: VDialog(
                                  title: 'Confirmation Dialog',
                                  content: 'Are you sure you want to delete this item?',
                                  actions: [
                                    VDialogAction(
                                      label: 'Cancel',
                                      onPressed: () => Navigator.of(context).pop(),
                                    ),
                                    VDialogAction(
                                      label: 'Delete',
                                      onPressed: () => Navigator.of(context).pop(),
                                      isDestructive: true,
                                      isPrimary: true,
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                          variant: VButtonVariant.outlined,
                          child: Text('Confirmation Dialog'),
                        ),
                        VButton(
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (_) => VThemeProvider(
                                theme: currentTheme,
                                child: VDialog(
                                  title: 'Custom Dialog',
                                  size: VDialogSize.large,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'This is a custom dialog with complex content that adapts to the theme.',
                                        style: TextStyle(
                                          color: currentTheme.colors.onSurface,
                                          fontSize: 14,
                                          height: 1.5,
                                        ),
                                      ),
                                      SizedBox(height: currentTheme.componentDensity.md),
                                      VInput(
                                        placeholder: 'Enter your name',
                                      ),
                                      SizedBox(height: currentTheme.componentDensity.md),
                                      VCheckbox(
                                        value: false,
                                        onChanged: (value) {},
                                        label: 'I agree to the terms and conditions',
                                      ),
                                    ],
                                  ),
                                  actions: [
                                    VDialogAction(
                                      label: 'Cancel',
                                      onPressed: () => Navigator.of(context).pop(),
                                    ),
                                    VDialogAction(
                                      label: 'Save',
                                      onPressed: () {
                                        Navigator.of(context).pop();
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(content: Text('Custom dialog saved!')),
                                        );
                                      },
                                      isPrimary: true,
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                          variant: VButtonVariant.secondary,
                          child: Text('Custom Dialog'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              SizedBox(height: currentTheme.componentDensity.xl),

              // Component Showcase
              VCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    VText(
                      'Component Showcase',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: currentTheme.colors.onSurface,
                      ),
                    ),
                    SizedBox(height: currentTheme.componentDensity.lg),

                    // Buttons Section
                    _buildComponentSection(
                      'Buttons',
                      [
                        // Button Variants
                        VText(
                          'Variants',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        Wrap(
                          spacing: currentTheme.componentDensity.sm,
                          runSpacing: currentTheme.componentDensity.sm,
                          children: [
                            VButton(
                              onPressed: () {},
                              variant: VButtonVariant.primary,
                              child: const Text('Primary'),
                            ),
                            VButton(
                              onPressed: () {},
                              variant: VButtonVariant.secondary,
                              child: const Text('Secondary'),
                            ),
                            VButton(
                              onPressed: () {},
                              variant: VButtonVariant.outlined,
                              child: const Text('Outlined'),
                            ),
                            VButton(
                              onPressed: () {},
                              variant: VButtonVariant.text,
                              child: const Text('Text'),
                            ),
                          ],
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),

                        // Button Sizes
                        VText(
                          'Sizes',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        Wrap(
                          spacing: currentTheme.componentDensity.sm,
                          runSpacing: currentTheme.componentDensity.sm,
                          children: [
                            VButton(
                              onPressed: () {},
                              size: VButtonSize.small,
                              child: const Text('Small'),
                            ),
                            VButton(
                              onPressed: () {},
                              size: VButtonSize.medium,
                              child: const Text('Medium'),
                            ),
                            VButton(
                              onPressed: () {},
                              size: VButtonSize.large,
                              child: const Text('Large'),
                            ),
                          ],
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),

                        // Button States
                        VText(
                          'States',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        Wrap(
                          spacing: currentTheme.componentDensity.sm,
                          runSpacing: currentTheme.componentDensity.sm,
                          children: [
                            VButton(
                              onPressed: () {},
                              child: const Text('Normal'),
                            ),
                            VButton(
                              onPressed: () {},
                              isLoading: true,
                              child: const Text('Loading'),
                            ),
                            VButton(
                              onPressed: null,
                              isDisabled: true,
                              child: const Text('Disabled'),
                            ),
                          ],
                        ),
                      ],
                    ),

                    // Text Components Section
                    _buildComponentSection(
                      'Text',
                      [
                        VText(
                          'Typography Showcase',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VText(
                          'This is a large heading text',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        VText(
                          'This is a medium subtitle text',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        VText(
                          'This is regular body text that demonstrates the default styling of VText component.',
                          style: TextStyle(
                            fontSize: 14,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        VText(
                          'This is muted text for secondary information',
                          style: TextStyle(
                            fontSize: 12,
                            color: currentTheme.colors.neutral.shade500,
                          ),
                        ),
                      ],
                    ),

                    // Input Components Section
                    _buildComponentSection(
                      'Inputs',
                      [
                        VText(
                          'Input Types',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VInput(
                          placeholder: 'Standard text input',
                          type: VInputType.text,
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VInput(
                          placeholder: 'Email address',
                          type: VInputType.email,
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VInput(
                          placeholder: 'Phone number',
                          type: VInputType.phone,
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VInput(
                          placeholder: 'Currency amount',
                          type: VInputType.currency,
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VInput(
                          placeholder: 'Number value',
                          type: VInputType.number,
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VInput(
                          placeholder: 'Disabled input',
                          controller: TextEditingController(text: 'Read only'),
                          enabled: false,
                        ),
                      ],
                    ),

                    // Text Area Components Section
                    _buildComponentSection(
                      'Text Areas',
                      [
                        VText(
                          'Multi-line Inputs',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VTextArea(
                          placeholder: 'Basic text area (3 lines)',
                          minLines: 3,
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VTextArea(
                          placeholder: 'Auto-expanding text area',
                          minLines: 2,
                          maxLines: 6,
                          autoExpand: true,
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VTextArea(
                          placeholder: 'Text area with character counter',
                          minLines: 3,
                          maxLength: 200,
                          showCharacterCounter: true,
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VTextArea(
                          placeholder: 'Disabled text area',
                          controller: TextEditingController(text: 'This is read-only content\nthat spans multiple lines\nand cannot be edited.'),
                          enabled: false,
                          minLines: 3,
                        ),
                      ],
                    ),

                    // Dropdown Components Section
                    _buildComponentSection(
                      'Dropdowns',
                      [
                        VText(
                          'Selection Controls',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VDropdown<String>(
                          label: 'Country',
                          value: selectedCountry,
                          hint: 'Select a country',
                          items: ['United States', 'Canada', 'United Kingdom', 'Germany', 'France']
                              .map((country) => VDropdownItem(
                                    value: country,
                                    child: Text(country),
                                  ))
                              .toList(),
                          onChanged: (value) {
                            setState(() {
                              selectedCountry = value;
                            });
                          },
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VDropdown<String>(
                          label: 'Priority Level',
                          value: selectedPriority,
                          items: ['Low', 'Medium', 'High', 'Critical']
                              .map((priority) => VDropdownItem(
                                    value: priority,
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 12,
                                          height: 12,
                                          decoration: BoxDecoration(
                                            color: priority == 'Low'
                                                ? Colors.green
                                                : priority == 'Medium'
                                                    ? Colors.orange
                                                    : priority == 'High'
                                                        ? Colors.red
                                                        : Colors.purple,
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        SizedBox(width: 8),
                                        Text(priority),
                                      ],
                                    ),
                                  ))
                              .toList(),
                          onChanged: (value) {
                            setState(() {
                              selectedPriority = value;
                            });
                          },
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VDropdown<String>(
                          label: 'Disabled Dropdown',
                          enabled: false,
                          hint: 'This dropdown is disabled',
                          items: ['Option 1', 'Option 2']
                              .map((option) => VDropdownItem(
                                    value: option,
                                    child: Text(option),
                                  ))
                              .toList(),
                          onChanged: (value) {
                            // Handle selection
                          },
                        ),
                      ],
                    ),

                    // VSelect Components Section
                    _buildComponentSection(
                      'Select Components',
                      [
                        VText(
                          'Advanced Selection Controls',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VSelect<String>(
                          label: 'Single Select',
                          hint: 'Choose one option',
                          items: ['Option 1', 'Option 2', 'Option 3', 'Option 4']
                              .map((option) => VSelectItem(
                                    value: option,
                                    label: option,
                                  ))
                              .toList(),
                          onChanged: (value) {
                            print('Selected: $value');
                          },
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VSelect<String>(
                          label: 'Multi Select',
                          hint: 'Choose multiple options',
                          mode: VSelectMode.multiple,
                          items: ['Feature A', 'Feature B', 'Feature C', 'Feature D']
                              .map((feature) => VSelectItem(
                                    value: feature,
                                    label: feature,
                                  ))
                              .toList(),
                          onMultiChanged: (values) {
                            print('Selected: $values');
                          },
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VSelect<String>(
                          label: 'Searchable Select',
                          hint: 'Search and select',
                          searchable: true,
                          items: ['Apple', 'Banana', 'Cherry', 'Date', 'Elderberry']
                              .map((fruit) => VSelectItem(
                                    value: fruit,
                                    label: fruit,
                                  ))
                              .toList(),
                          onChanged: (value) {
                            print('Selected: $value');
                          },
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VSelect<String>(
                          label: 'Searchable Multi Select',
                          hint: 'Search and select multiple',
                          searchable: true,
                          mode: VSelectMode.multiple,
                          items: ['React', 'Vue', 'Angular', 'Svelte', 'Flutter', 'React Native', 'Next.js', 'Nuxt.js']
                              .map((tech) => VSelectItem(
                                    value: tech,
                                    label: tech,
                                  ))
                              .toList(),
                          onMultiChanged: (values) {
                            print('Selected: $values');
                          },
                        ),
                      ],
                    ),

                    // Checkbox Components Section
                    _buildComponentSection(
                      'Checkboxes',
                      [
                        VText(
                          'Single Checkboxes',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VCheckbox(
                          value: singleCheckbox,
                          onChanged: (value) {
                            setState(() {
                              singleCheckbox = value ?? false;
                            });
                          },
                          label: 'Basic checkbox',
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VCheckbox(
                          value: indeterminateCheckbox,
                          tristate: true,
                          onChanged: (value) {
                            setState(() {
                              indeterminateCheckbox = value;
                            });
                          },
                          label: 'Tristate checkbox (click to cycle)',
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VCheckbox(
                          value: true,
                          enabled: false,
                          onChanged: (value) {},
                          label: 'Disabled checked',
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VCheckbox(
                          value: false,
                          enabled: false,
                          onChanged: (value) {},
                          label: 'Disabled unchecked',
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),

                        VText(
                          'Checkbox Groups',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VCheckboxGroup<String>(
                          label: 'Features (Vertical)',
                          options: [
                            VCheckboxOption(value: 'feature1', label: 'Analytics Dashboard'),
                            VCheckboxOption(value: 'feature2', label: 'Real-time Notifications'),
                            VCheckboxOption(value: 'feature3', label: 'Advanced Reporting'),
                            VCheckboxOption(value: 'feature4', label: 'API Access'),
                          ],
                          selectedValues: selectedFeatures,
                          showSelectAll: true,
                          direction: VCheckboxGroupDirection.vertical,
                          onChanged: (values) {
                            setState(() {
                              selectedFeatures = values;
                            });
                          },
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VCheckboxGroup<String>(
                          label: 'Notifications (Horizontal)',
                          options: [
                            VCheckboxOption(value: 'email', label: 'Email'),
                            VCheckboxOption(value: 'sms', label: 'SMS'),
                            VCheckboxOption(value: 'push', label: 'Push'),
                          ],
                          selectedValues: selectedNotifications,
                          direction: VCheckboxGroupDirection.horizontal,
                          onChanged: (values) {
                            setState(() {
                              selectedNotifications = values;
                            });
                          },
                        ),
                      ],
                    ),

                    // Radio Components Section
                    _buildComponentSection(
                      'Radio Buttons',
                      [
                        VText(
                          'Single Radio Buttons',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        Row(
                          children: [
                            VRadio<String>(
                              value: 'small',
                              groupValue: selectedSize,
                              onChanged: (value) {
                                setState(() {
                                  selectedSize = value;
                                });
                              },
                              label: 'Small',
                            ),
                            SizedBox(width: currentTheme.componentDensity.lg),
                            VRadio<String>(
                              value: 'medium',
                              groupValue: selectedSize,
                              onChanged: (value) {
                                setState(() {
                                  selectedSize = value;
                                });
                              },
                              label: 'Medium',
                            ),
                            SizedBox(width: currentTheme.componentDensity.lg),
                            VRadio<String>(
                              value: 'large',
                              groupValue: selectedSize,
                              onChanged: (value) {
                                setState(() {
                                  selectedSize = value;
                                });
                              },
                              label: 'Large',
                            ),
                          ],
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),

                        VText(
                          'Radio Groups',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VRadioGroup<String>(
                          label: 'Payment Method (Vertical)',
                          options: [
                            VRadioOption(value: 'credit', label: 'Credit Card'),
                            VRadioOption(value: 'debit', label: 'Debit Card'),
                            VRadioOption(value: 'paypal', label: 'PayPal'),
                            VRadioOption(value: 'bank', label: 'Bank Transfer'),
                          ],
                          value: selectedPayment,
                          direction: VRadioGroupDirection.vertical,
                          onChanged: (value) {
                            setState(() {
                              selectedPayment = value;
                            });
                          },
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VRadioGroup<String>(
                          label: 'Delivery Speed (Horizontal)',
                          options: [
                            VRadioOption(value: 'standard', label: 'Standard'),
                            VRadioOption(value: 'express', label: 'Express'),
                            VRadioOption(value: 'overnight', label: 'Overnight'),
                          ],
                          value: 'express',
                          direction: VRadioGroupDirection.horizontal,
                          onChanged: (value) {
                            // Handle selection
                          },
                        ),
                      ],
                    ),

                    // Switch Components Section
                    _buildComponentSection(
                      'Switches',
                      [
                        VText(
                          'Toggle Controls',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VSwitch(
                          value: notificationsEnabled,
                          onChanged: (value) {
                            setState(() {
                              notificationsEnabled = value;
                            });
                          },
                          label: 'Enable notifications',
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VSwitch(
                          value: darkModeSwitch,
                          onChanged: (value) {
                            setState(() {
                              darkModeSwitch = value;
                            });
                          },
                          label: 'Dark mode (switch)',
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VSwitch(
                          value: autoSaveEnabled,
                          onChanged: (value) {
                            setState(() {
                              autoSaveEnabled = value;
                            });
                          },
                          label: 'Auto-save documents',
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VSwitch(
                          value: true,
                          onChanged: null,
                          enabled: false,
                          label: 'Disabled switch (on)',
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VSwitch(
                          value: false,
                          onChanged: null,
                          enabled: false,
                          label: 'Disabled switch (off)',
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VSwitch(
                          value: true,
                          onChanged: (value) {},
                          adaptive: false,
                          label: 'Material style (non-adaptive)',
                        ),
                      ],
                    ),

                    // Slider Components Section
                    _buildComponentSection(
                      'Sliders',
                      [
                        VText(
                          'Value Selection Controls',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VSlider(
                          type: VSliderType.single,
                          value: volumeLevel,
                          onChanged: (value) {
                            setState(() {
                              volumeLevel = value;
                            });
                          },
                          label: 'Volume Level (${(volumeLevel * 100).round()}%)',
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VSlider(
                          type: VSliderType.single,
                          value: brightnessLevel,
                          onChanged: (value) {
                            setState(() {
                              brightnessLevel = value;
                            });
                          },
                          label: 'Brightness (${(brightnessLevel * 100).round()}%)',
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VSlider(
                          type: VSliderType.range,
                          rangeValues: priceRange,
                          onRangeChanged: (values) {
                            setState(() {
                              priceRange = values;
                            });
                          },
                          min: 0.0,
                          max: 100.0,
                          label: 'Price Range (\$${priceRange.start.round()} - \$${priceRange.end.round()})',
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VSlider(
                          type: VSliderType.single,
                          value: temperatureRange,
                          onChanged: (value) {
                            setState(() {
                              temperatureRange = value;
                            });
                          },
                          min: 16.0,
                          max: 30.0,
                          divisions: 14,
                          label: 'Temperature (${temperatureRange.round()}°C)',
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VSlider(
                          type: VSliderType.single,
                          value: 0.5,
                          onChanged: null,
                          enabled: false,
                          label: 'Disabled slider',
                        ),
                      ],
                    ),

                    // Date & Time Components Section
                    _buildComponentSection(
                      'Date & Time Pickers',
                      [
                        VText(
                          'Date and Time Selection Controls',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VDatePicker(
                          label: 'Birth Date',
                          value: selectedDate,
                          placeholder: 'Select your birth date',
                          onChanged: (value) {
                            setState(() {
                              selectedDate = value;
                            });
                          },
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VDateRangePicker(
                          label: 'Project Duration',
                          value: selectedDateRange,
                          startPlaceholder: 'Start date',
                          endPlaceholder: 'End date',
                          onChanged: (value) {
                            setState(() {
                              selectedDateRange = value;
                            });
                          },
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VTimePicker(
                          label: 'Meeting Time (12-hour)',
                          value: selectedTime,
                          placeholder: 'Select meeting time',
                          format: VTimeFormat.hour12,
                          onChanged: (value) {
                            setState(() {
                              selectedTime = value;
                            });
                          },
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VTimePicker(
                          label: 'Departure Time (24-hour)',
                          placeholder: 'Select departure time',
                          format: VTimeFormat.hour24,
                          onChanged: (value) {
                            // Handle time selection
                          },
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VDateTimePicker(
                          label: 'Appointment (Combined)',
                          value: selectedDateTime,
                          placeholder: 'Select appointment date & time',
                          separateFields: false,
                          onChanged: (value) {
                            setState(() {
                              selectedDateTime = value;
                            });
                          },
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VDateTimePicker(
                          label: 'Event Schedule (Separate Fields)',
                          separateFields: true,
                          timeFormat: VTimeFormat.hour24,
                          datePlaceholder: 'Date',
                          timePlaceholder: 'Time',
                          onChanged: (value) {
                            // Handle date-time selection
                          },
                        ),
                        SizedBox(height: currentTheme.componentDensity.md),
                        VDatePicker(
                          label: 'Disabled Date Picker',
                          enabled: false,
                          placeholder: 'Cannot select date',
                        ),
                      ],
                    ),

                    // Interactive Calendar Demo Section
                    _buildComponentSection(
                      'Interactive Calendar Demo',
                      [
                        VText(
                          'VCalendar with Configurable Features',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        
                        // Configuration Controls
                        VText(
                          'Configuration Options:',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.xs),
                        
                        // Configuration Toggles
                        Wrap(
                          spacing: 16,
                          runSpacing: 8,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                VSwitch(
                                  value: showNavigationControls,
                                  onChanged: (value) {
                                    setState(() {
                                      showNavigationControls = value;
                                    });
                                  },
                                ),
                                SizedBox(width: 8),
                                VText('Navigation Controls', style: TextStyle(fontSize: 12)),
                              ],
                            ),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                VSwitch(
                                  value: showViewSwitcher,
                                  onChanged: (value) {
                                    setState(() {
                                      showViewSwitcher = value;
                                    });
                                  },
                                ),
                                SizedBox(width: 8),
                                VText('View Switcher', style: TextStyle(fontSize: 12)),
                              ],
                            ),
                          ],
                        ),
                        
                        SizedBox(height: currentTheme.componentDensity.sm),
                        
                        // First day of week selector
                        Row(
                          children: [
                            VText(
                              'Week starts: ',
                              style: TextStyle(
                                fontSize: 12,
                                color: currentTheme.colors.onSurface,
                              ),
                            ),
                            VRadioGroup<int>(
                              value: firstDayOfWeek,
                              options: const [
                                VRadioOption(value: VCalendarWeekStart.monday, label: 'Monday'),
                                VRadioOption(value: VCalendarWeekStart.sunday, label: 'Sunday'),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  firstDayOfWeek = value ?? VCalendarWeekStart.monday;
                                });
                              },
                              direction: VRadioGroupDirection.horizontal,
                            ),
                          ],
                        ),
                        
                        SizedBox(height: currentTheme.componentDensity.sm),
                        
                        // Hour Spread Configuration
                        Row(
                          children: [
                            VText(
                              'Hour Range: ',
                              style: TextStyle(
                                fontSize: 12,
                                color: currentTheme.colors.onSurface,
                              ),
                            ),
                            Expanded(
                              child: VSelect<String>(
                                value: selectedHourSpread,
                                items: hourSpreadPresets.keys.map((preset) => VSelectItem(
                                  value: preset,
                                  label: '$preset (${hourSpreadPresets[preset]!['start']}:00 - ${hourSpreadPresets[preset]!['end']}:00)',
                                )).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      selectedHourSpread = value;
                                      startHour = hourSpreadPresets[value]!['start']!;
                                      endHour = hourSpreadPresets[value]!['end']!;
                                    });
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                        
                        SizedBox(height: currentTheme.componentDensity.sm),
                        
                        // Color Coding Configuration
                        VText(
                          'Color Coding Options:',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.xs),
                        
                        Wrap(
                          spacing: 16,
                          runSpacing: 8,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                VSwitch(
                                  value: useStatusColors,
                                  onChanged: (value) {
                                    setState(() {
                                      useStatusColors = value;
                                    });
                                  },
                                ),
                                SizedBox(width: 8),
                                VText('Status Colors', style: TextStyle(fontSize: 12)),
                              ],
                            ),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                VSwitch(
                                  value: useCategoryColors,
                                  onChanged: (value) {
                                    setState(() {
                                      useCategoryColors = value;
                                    });
                                  },
                                ),
                                SizedBox(width: 8),
                                VText('Category Colors', style: TextStyle(fontSize: 12)),
                              ],
                            ),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                VSwitch(
                                  value: useClientColors,
                                  onChanged: (value) {
                                    setState(() {
                                      useClientColors = value;
                                    });
                                  },
                                ),
                                SizedBox(width: 8),
                                VText('Client Colors', style: TextStyle(fontSize: 12)),
                              ],
                            ),
                          ],
                        ),
                        
                        SizedBox(height: currentTheme.componentDensity.sm),
                        
                        // Availability Management Configuration
                        VText(
                          'Availability Management:',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.xs),
                        
                        Wrap(
                          spacing: 16,
                          runSpacing: 8,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                VSwitch(
                                  value: enableAvailabilityManagement,
                                  onChanged: (value) {
                                    setState(() {
                                      enableAvailabilityManagement = value;
                                    });
                                  },
                                ),
                                SizedBox(width: 8),
                                VText('Enable Availability', style: TextStyle(fontSize: 12)),
                              ],
                            ),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                VSwitch(
                                  value: showAvailabilityOverlay,
                                  onChanged: (value) {
                                    setState(() {
                                      showAvailabilityOverlay = value;
                                    });
                                  },
                                ),
                                SizedBox(width: 8),
                                VText('Show Overlay', style: TextStyle(fontSize: 12)),
                              ],
                            ),
                          ],
                        ),
                        
                        SizedBox(height: currentTheme.componentDensity.xs),
                        
                        // Buffer time and booking notice controls
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  VText(
                                    'Buffer Time: ${bufferMinutes}min',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: currentTheme.colors.onSurface,
                                    ),
                                  ),
                                  VSlider(
                                    type: VSliderType.single,
                                    value: bufferMinutes.toDouble(),
                                    min: 0,
                                    max: 60,
                                    divisions: 12,
                                    onChanged: (value) {
                                      setState(() {
                                        bufferMinutes = value.round();
                                      });
                                    },
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  VText(
                                    'Min Notice: ${minimumBookingNotice}h',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: currentTheme.colors.onSurface,
                                    ),
                                  ),
                                  VSlider(
                                    type: VSliderType.single,
                                    value: minimumBookingNotice.toDouble(),
                                    min: 0,
                                    max: 48,
                                    divisions: 16,
                                    onChanged: (value) {
                                      setState(() {
                                        minimumBookingNotice = value.round();
                                      });
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        
                        SizedBox(height: currentTheme.componentDensity.sm),
                        
                        // Interactive Calendar
                        Container(
                          height: 450,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: currentTheme.colors.neutral.shade300,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(currentTheme.corners.md),
                          ),
                          child: Builder(
                            builder: (context) {
                              try {
                                print('🟦 Building VCalendar with ${calendarEvents.length} events');
                                return VCalendar(
                                  events: calendarEvents,
                            initialView: currentDemoView,
                            firstDayOfWeek: firstDayOfWeek,
                            showNavigationControls: showNavigationControls,
                            showViewSwitcher: showViewSwitcher,
                            colorConfig: VEventColorConfig(
                              useStatusColors: useStatusColors,
                              useCategoryColors: useCategoryColors,
                              useClientColors: useClientColors,
                              clientColors: {
                                // Make client colors dramatically different and obvious
                                'team_internal': const Color(0xFF2563EB), // Bold Blue
                                'enterprise_corp': const Color(0xFF7C3AED), // Purple  
                                'tech_team': const Color(0xFF059669), // Green
                                'project_alpha': const Color(0xFFDC2626), // Red
                                'northwest_branch': const Color(0xFFEA580C), // Orange
                                'innovation_lab': const Color(0xFFDB2777), // Pink
                                'development_team': const Color(0xFF0891B2), // Cyan
                                'external_partner': const Color(0xFF65A30D), // Lime
                                'delayed_client': const Color(0xFFA21CAF), // Magenta
                              },
                            ),
                            availabilityConfig: enableAvailabilityManagement ? VAvailabilityConfig(
                              businessHours: businessHours,
                              timeBlocks: timeBlocks,
                              bufferMinutes: bufferMinutes,
                              minimumBookingNotice: minimumBookingNotice,
                              showAvailabilityOverlay: showAvailabilityOverlay,
                            ) : null,
                            timeSlotConfig: VTimeSlotConfig(
                              slotHeight: slotHeight,
                              startHour: startHour,
                              endHour: endHour,
                              showTimeLabels: showTimeLabels,
                              showMinorGridLines: showMinorGridLines,
                              showNowIndicator: showNowIndicator,
                            ),
                            onEventTapped: (event) {
                              _showEventModal(event);
                            },
                            onDateSelected: (date) {
                              setState(() {
                                selectedDemoDate = date;
                              });
                              // Just select the date, don't create event
                              print('Selected date: $date');
                            },
                            onViewChanged: (view) {
                              setState(() {
                                currentDemoView = view;
                              });
                            },
                            onMonthChanged: (date) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Month: ${date.month}/${date.year}'),
                                  duration: const Duration(milliseconds: 800),
                                ),
                              );
                            },
                          );
                              } catch (e, stackTrace) {
                                print('🟥 Error building VCalendar: $e');
                                print('🟥 Stack trace: $stackTrace');
                                return Container(
                                  height: 450,
                                  child: Center(
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(Icons.error, color: Colors.red, size: 48),
                                        SizedBox(height: 16),
                                        Text('Calendar Error: $e'),
                                      ],
                                    ),
                                  ),
                                );
                              }
                            },
                          ),
                        ),
                        
                        SizedBox(height: currentTheme.componentDensity.sm),
                        
                        // Add Event Button
                        Center(
                          child: VButton(
                            onPressed: () async {
                              try {
                                print('About to show event modal for NEW event...');
                                // Pass null to create a completely new event
                                await _showEventModal(null);
                                print('Event modal completed successfully');
                              } catch (e) {
                                print('Error in event modal: $e');
                                // Show simple error message
                                if (mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('Error creating event: $e'),
                                      backgroundColor: currentTheme.colors.error,
                                    ),
                                  );
                                }
                              }
                            },
                            variant: VButtonVariant.primary,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.add_circle_outline,
                                  size: 20,
                                  color: currentTheme.colors.onPrimary,
                                ),
                                SizedBox(width: currentTheme.componentDensity.xs),
                                const Text('Add Event'),
                              ],
                            ),
                          ),
                        ),
                        
                        SizedBox(height: currentTheme.componentDensity.md),
                        
                        // Feature explanations and testing guide
                        Container(
                          padding: EdgeInsets.all(currentTheme.componentDensity.md),
                          decoration: BoxDecoration(
                            color: currentTheme.colors.orange.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(currentTheme.corners.sm),
                            border: Border.all(
                              color: currentTheme.colors.orange.primary.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              VText(
                                '🎯 How to Test the Advanced Calendar Features',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: currentTheme.colors.onSurface,
                                ),
                              ),
                              SizedBox(height: currentTheme.componentDensity.sm),
                              VText(
                                '✨ COLOR CODING SYSTEM:\n'
                                '• Toggle "Status Colors" - See 🔴 overdue, ✅ completed, ❓ tentative events\n'
                                '• Toggle "Category Colors" - See 💼 work vs 🏥 personal vs ✈️ travel events\n'
                                '• Toggle "Client Colors" - Each client gets a unique bold color\n\n'
                                '🕐 AVAILABILITY MANAGEMENT:\n'
                                '• Toggle "Availability" - See business hours (blue strip on top)\n'
                                '• Toggle "Overlay" - See blocked weekends, holidays (red), lunch breaks\n'
                                '• Adjust buffer time and notice period\n\n'
                                '📅 VISUAL INDICATORS TO LOOK FOR:\n'
                                '• Event status badges (CONFIRMED, OVERDUE, etc.)\n'
                                '• Color-coded event dots in month view\n'
                                '• Availability strips on day cells\n'
                                '• Red circles for blocked/unavailable days',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: currentTheme.colors.onSurface,
                                  height: 1.4,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    // Container Components Section
                    _buildComponentSection(
                      'Containers',
                      [
                        VText(
                          'Layout Components',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VContainer(
                          color: currentTheme.colors.primary.withValues(alpha: 0.1),
                          child: VText(
                            'Primary colored container with themed spacing',
                            style: TextStyle(color: currentTheme.colors.onSurface),
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VContainer(
                          color: currentTheme.colors.secondary.withValues(alpha: 0.1),
                          child: VText(
                            'Secondary colored container',
                            style: TextStyle(color: currentTheme.colors.onSurface),
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        VContainer(
                          color: currentTheme.colors.success.withValues(alpha: 0.1),
                          child: VText(
                            'Success colored container',
                            style: TextStyle(color: currentTheme.colors.onSurface),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: currentTheme.componentDensity.xl),

              // Data Tables & Grids Section
              VCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    VText(
                      'Data Tables & Grids',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: currentTheme.colors.onSurface,
                      ),
                    ),
                    SizedBox(height: currentTheme.componentDensity.lg),

                    _buildComponentSection(
                      'Table Configuration',
                      [
                        Row(
                          children: [
                            Expanded(
                              child: VSwitch(
                                value: tableSelectable,
                                onChanged: (value) {
                                  setState(() {
                                    tableSelectable = value;
                                    if (!value) {
                                      currentSelection = const VRowSelection<Employee>();
                                    }
                                  });
                                },
                                label: 'Selectable Rows',
                              ),
                            ),
                            SizedBox(width: currentTheme.componentDensity.gap),
                            Expanded(
                              child: VSwitch(
                                value: tableSortable,
                                onChanged: (value) {
                                  setState(() {
                                    tableSortable = value;
                                  });
                                },
                                label: 'Sortable Columns',
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        Row(
                          children: [
                            Expanded(
                              child: VSwitch(
                                value: tableSearchable,
                                onChanged: (value) {
                                  setState(() {
                                    tableSearchable = value;
                                  });
                                },
                                label: 'Search Enabled',
                              ),
                            ),
                            SizedBox(width: currentTheme.componentDensity.gap),
                            Expanded(
                              child: VSwitch(
                                value: tablePaginated,
                                onChanged: (value) {
                                  setState(() {
                                    tablePaginated = value;
                                  });
                                },
                                label: 'Pagination',
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        Row(
                          children: [
                            Expanded(
                              child: VSwitch(
                                value: tableShowBorders,
                                onChanged: (value) {
                                  setState(() {
                                    tableShowBorders = value;
                                  });
                                },
                                label: 'Show Borders',
                              ),
                            ),
                            SizedBox(width: currentTheme.componentDensity.gap),
                            Expanded(
                              child: VSwitch(
                                value: tableSearchable,
                                onChanged: (value) {
                                  setState(() {
                                    tableSearchable = value;
                                    if (!value) {
                                      tableSearchQuery = ''; // Clear search when disabling
                                    }
                                  });
                                },
                                label: 'Searchable',
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        Row(
                          children: [
                            Expanded(
                              child: VSwitch(
                                value: tablePaginated,
                                onChanged: (value) {
                                  setState(() {
                                    tablePaginated = value;
                                  });
                                },
                                label: 'Pagination',
                              ),
                            ),
                            SizedBox(width: currentTheme.componentDensity.gap),
                            Expanded(
                              child: VSwitch(
                                value: tableShowBorders,
                                onChanged: (value) {
                                  setState(() {
                                    tableShowBorders = value;
                                  });
                                },
                                label: 'Show Borders',
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        Row(
                          children: [
                            Expanded(
                              child: VSwitch(
                                value: tableAlternateRows,
                                onChanged: (value) {
                                  setState(() {
                                    tableAlternateRows = value;
                                  });
                                },
                                label: 'Alternate Row Colors',
                              ),
                            ),
                            SizedBox(width: currentTheme.componentDensity.gap),
                            Expanded(child: Container()), // Empty space for balance
                          ],
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        Row(
                          children: [
                            Expanded(
                              child: VDropdown<int>(
                                label: 'Page Size',
                                value: tablePageSize,
                                items: [3, 5, 10, 15, 20].map((size) => VDropdownItem(
                                  value: size,
                                  child: Text('$size rows'),
                                )).toList(),
                                onChanged: (value) {
                                  setState(() {
                                    tablePageSize = value ?? 5;
                                    tableCurrentPage = 0; // Reset to first page
                                  });
                                },
                              ),
                            ),
                            SizedBox(width: currentTheme.componentDensity.gap),
                            Expanded(
                              child: Row(
                                children: [
                                  VButton(
                                    onPressed: () {
                                      setState(() {
                                        tableLoading = true;
                                      });
                                      // Simulate loading
                                      Future.delayed(const Duration(seconds: 2), () {
                                        setState(() {
                                          tableLoading = false;
                                        });
                                      });
                                    },
                                    variant: VButtonVariant.outlined,
                                    child: Text('Test Loading'),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),

                    SizedBox(height: currentTheme.componentDensity.lg),

                    _buildComponentSection(
                      'Employee Data Table',
                      [
                        // Search bar - only show if searchable is enabled
                        if (tableSearchable) ...[
                          TextField(
                            decoration: InputDecoration(
                              labelText: 'Search employees',
                              hintText: 'Search by name, email, department, or position...',
                              prefixIcon: Icon(Icons.search),
                              suffixIcon: tableSearchQuery.isNotEmpty
                                  ? IconButton(
                                      icon: Icon(Icons.clear),
                                      onPressed: () {
                                        setState(() {
                                          tableSearchQuery = '';
                                        });
                                      },
                                    )
                                  : null,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(currentTheme.corners.sm),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(currentTheme.corners.sm),
                                borderSide: BorderSide(
                                  color: currentTheme.colors.onSurface.withOpacity(0.3),
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(currentTheme.corners.sm),
                                borderSide: BorderSide(
                                  color: currentTheme.colors.primary,
                                  width: 2,
                                ),
                              ),
                            ),
                            onChanged: (value) {
                              setState(() {
                                tableSearchQuery = value;
                                tableCurrentPage = 0; // Reset to first page when searching
                              });
                            },
                          ),
                          SizedBox(height: currentTheme.componentDensity.gap),
                        ],
                        Container(
                          height: 400,
                          child: VDataTable<Employee>(
                            columns: [
                              VDataColumn<Employee>(
                                key: 'name',
                                label: 'Name',
                                getValue: (employee) => employee.name,
                                sortable: true,
                              ),
                              VDataColumn<Employee>(
                                key: 'email',
                                label: 'Email',
                                getValue: (employee) => employee.email,
                                sortable: true,
                              ),
                              VDataColumn<Employee>(
                                key: 'department',
                                label: 'Department',
                                getValue: (employee) => employee.department,
                                sortable: true,
                              ),
                              VDataColumn<Employee>(
                                key: 'position',
                                label: 'Position',
                                getValue: (employee) => employee.position,
                                sortable: true,
                              ),
                              VDataColumn<Employee>(
                                key: 'salary',
                                label: 'Salary',
                                alignment: Alignment.centerRight,
                                getValue: (employee) => employee.salary,
                                sortable: true,
                                builder: (context, employee, index) => Text(
                                  '\$${employee.salary.toString().replaceAllMapped(
                                    RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                                    (Match m) => '${m[1]},',
                                  )}',
                                  style: TextStyle(
                                    color: currentTheme.colors.onSurface,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              VDataColumn<Employee>(
                                key: 'status',
                                label: 'Status',
                                alignment: Alignment.center,
                                getValue: (employee) => employee.isActive ? 'Active' : 'Inactive',
                                sortable: true,
                                builder: (context, employee, index) => Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: currentTheme.componentDensity.horizontalPadding * 0.75,
                                    vertical: currentTheme.componentDensity.verticalPadding * 0.25,
                                  ),
                                  decoration: BoxDecoration(
                                    color: employee.isActive
                                        ? currentTheme.colors.success.withOpacity(0.1)
                                        : currentTheme.colors.error.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(currentTheme.corners.sm),
                                    border: Border.all(
                                      color: employee.isActive
                                          ? currentTheme.colors.success
                                          : currentTheme.colors.error,
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    employee.isActive ? 'Active' : 'Inactive',
                                    style: TextStyle(
                                      color: employee.isActive
                                          ? currentTheme.colors.success
                                          : currentTheme.colors.error,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                            data: employees,
                            searchQuery: tableSearchable && tableSearchQuery.isNotEmpty ? tableSearchQuery : null,
                            loading: tableLoading,
                            selectable: tableSelectable,
                            sortable: tableSortable,
                            paginated: tablePaginated,
                            pageSize: tablePageSize,
                            currentPage: tableCurrentPage,
                            showBorders: tableShowBorders,
                            alternateRowColors: tableAlternateRows,
                            currentSort: currentSort,
                            selection: currentSelection,
                            onSort: (sort) {
                              setState(() {
                                currentSort = sort;
                                // Sort the employees list based on the column
                                employees.sort((a, b) {
                                  dynamic valueA, valueB;
                                  
                                  switch (sort.columnKey) {
                                    case 'name':
                                      valueA = a.name;
                                      valueB = b.name;
                                      break;
                                    case 'email':
                                      valueA = a.email;
                                      valueB = b.email;
                                      break;
                                    case 'department':
                                      valueA = a.department;
                                      valueB = b.department;
                                      break;
                                    case 'position':
                                      valueA = a.position;
                                      valueB = b.position;
                                      break;
                                    case 'salary':
                                      valueA = a.salary;
                                      valueB = b.salary;
                                      break;
                                    case 'status':
                                      valueA = a.isActive ? 'Active' : 'Inactive';
                                      valueB = b.isActive ? 'Active' : 'Inactive';
                                      break;
                                    default:
                                      return 0;
                                  }
                                  
                                  int comparison = valueA.compareTo(valueB);
                                  return sort.direction == VSortDirection.ascending 
                                      ? comparison 
                                      : -comparison;
                                });
                              });
                            },
                            onSelectionChanged: (selection) {
                              setState(() {
                                currentSelection = selection;
                              });
                            },
                            onPageChanged: (page) {
                              setState(() {
                                tableCurrentPage = page;
                              });
                            },
                            onRowTap: (employee, index) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Tapped: ${employee.name}'),
                                  duration: const Duration(seconds: 2),
                                ),
                              );
                            },
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.gap),
                        if (currentSelection.selectedItems.isNotEmpty)
                          Container(
                            padding: EdgeInsets.all(currentTheme.componentDensity.gap),
                            decoration: BoxDecoration(
                              color: currentTheme.colors.primary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(currentTheme.corners.sm),
                              border: Border.all(
                                color: currentTheme.colors.primary.withOpacity(0.3),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Selected Employees (${currentSelection.selectedItems.length}):',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color: currentTheme.colors.primary,
                                  ),
                                ),
                                SizedBox(height: currentTheme.componentDensity.smallGap),
                                Wrap(
                                  spacing: currentTheme.componentDensity.smallGap,
                                  runSpacing: currentTheme.componentDensity.smallGap,
                                  children: currentSelection.selectedItems.map((employee) => 
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: currentTheme.componentDensity.horizontalPadding * 0.5,
                                        vertical: currentTheme.componentDensity.verticalPadding * 0.25,
                                      ),
                                      decoration: BoxDecoration(
                                        color: currentTheme.colors.primary.withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(currentTheme.corners.sm),
                                      ),
                                      child: Text(
                                        employee.name,
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: currentTheme.colors.primary,
                                        ),
                                      ),
                                    ),
                                  ).toList(),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: currentTheme.componentDensity.xl),

              // Cards Showcase
              VCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    VText(
                      'Cards & Layouts',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: currentTheme.colors.onSurface,
                      ),
                    ),
                    SizedBox(height: currentTheme.componentDensity.lg),

                    _buildComponentSection(
                      'Cards',
                      [
                        VText(
                          'Different Card Styles',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),

                        // Basic Card
                        VCard(
                          child: VText(
                            'Basic Card - This card uses the default theme settings for elevation, corners, and spacing.',
                            style: TextStyle(color: currentTheme.colors.onSurface),
                          ),
                        ),

                        SizedBox(height: currentTheme.componentDensity.sm),

                        // Card with custom padding
                        VCard(
                          padding: EdgeInsets.all(currentTheme.componentDensity.lg),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              VText(
                                'Card with Custom Padding',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: currentTheme.colors.onSurface,
                                ),
                              ),
                              SizedBox(height: currentTheme.componentDensity.sm),
                              VText(
                                'This card demonstrates custom padding and multiple child elements.',
                                style: TextStyle(color: currentTheme.colors.onSurface),
                              ),
                              SizedBox(height: currentTheme.componentDensity.sm),
                              VButton(
                                onPressed: () {},
                                size: VButtonSize.small,
                                child: const Text('Action'),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              SizedBox(height: currentTheme.componentDensity.xl),

              // Brand Colors Demo Section
              VCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    VText(
                      'Brand Colors',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: currentTheme.colors.onSurface,
                      ),
                    ),
                    SizedBox(height: currentTheme.componentDensity.lg),

                    _buildComponentSection(
                      'Color Palette',
                      [
                        // Primary Colors Row
                        VText(
                          'Primary Colors',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        Row(
                          children: [
                            _buildColorSwatch('Primary', currentTheme.colors.primary),
                            SizedBox(width: currentTheme.componentDensity.sm),
                            _buildColorSwatch('Primary Variant', currentTheme.colors.primaryVariant),
                            SizedBox(width: currentTheme.componentDensity.sm),
                            _buildColorSwatch('Secondary', currentTheme.colors.secondary),
                            SizedBox(width: currentTheme.componentDensity.sm),
                            _buildColorSwatch('Secondary Variant', currentTheme.colors.secondaryVariant),
                          ],
                        ),

                        SizedBox(height: currentTheme.componentDensity.md),

                        // Semantic Colors Row
                        VText(
                          'Semantic Colors',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        Row(
                          children: [
                            _buildColorSwatch('Success', currentTheme.colors.success),
                            SizedBox(width: currentTheme.componentDensity.sm),
                            _buildColorSwatch('Warning', currentTheme.colors.warning),
                            SizedBox(width: currentTheme.componentDensity.sm),
                            _buildColorSwatch('Error', currentTheme.colors.error),
                            SizedBox(width: currentTheme.componentDensity.sm),
                            _buildColorSwatch('Info', currentTheme.colors.info),
                          ],
                        ),

                        SizedBox(height: currentTheme.componentDensity.md),

                        // Orange Scale
                        VText(
                          'Orange Scale (Primary Brand)',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: currentTheme.colors.onSurface,
                          ),
                        ),
                        SizedBox(height: currentTheme.componentDensity.sm),
                        Row(
                          children: [
                            _buildColorSwatch('50', currentTheme.colors.orange.shade50),
                            SizedBox(width: currentTheme.componentDensity.xs),
                            _buildColorSwatch('100', currentTheme.colors.orange.shade100),
                            SizedBox(width: currentTheme.componentDensity.xs),
                            _buildColorSwatch('300', currentTheme.colors.orange.shade300),
                            SizedBox(width: currentTheme.componentDensity.xs),
                            _buildColorSwatch('500', currentTheme.colors.orange.shade500),
                            SizedBox(width: currentTheme.componentDensity.xs),
                            _buildColorSwatch('700', currentTheme.colors.orange.shade700),
                            SizedBox(width: currentTheme.componentDensity.xs),
                            _buildColorSwatch('900', currentTheme.colors.orange.shade900),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              SizedBox(height: currentTheme.componentDensity.xl),

            ],  // closes Column children
          ),    // closes Column
        ),      // closes SingleChildScrollView  
      ),        // closes Scaffold
    ),          // closes Theme
  );            // closes VThemeProvider
  }

  Widget _buildColorSwatch(String label, Color color) {
    return Expanded(
      child: Column(
        children: [
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(currentTheme.corners.sm),
              border: Border.all(
                color: currentTheme.colors.neutral.shade300,
                width: 1,
              ),
            ),
          ),
          SizedBox(height: currentTheme.componentDensity.xs),
          VText(
            label,
            style: TextStyle(
              fontSize: 10,
              color: currentTheme.colors.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildComponentSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        VText(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: currentTheme.colors.primary,
          ),
        ),
        SizedBox(height: currentTheme.componentDensity.md),
        ...children,
        SizedBox(height: currentTheme.componentDensity.lg),
      ],
    );
  }
}
