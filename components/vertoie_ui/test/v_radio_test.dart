import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VRadio Tests', () {
    testWidgets('should render basic radio button', (tester) async {
      String? groupValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadio<String>(
                value: 'option1',
                groupValue: groupValue,
                onChanged: (value) => groupValue = value,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(VRadio<String>), findsOneWidget);
      expect(find.byType(GestureDetector), findsOneWidget);
    });

    testWidgets('should render radio button with label', (tester) async {
      String? groupValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadio<String>(
                value: 'option1',
                groupValue: groupValue,
                onChanged: (value) => groupValue = value,
                label: 'Test Label',
              ),
            ),
          ),
        ),
      );

      expect(find.byType(VRadio<String>), findsOneWidget);
      expect(find.text('Test Label'), findsOneWidget);
      expect(find.byType(Row), findsOneWidget);
    });

    testWidgets('should handle tap and change selection', (tester) async {
      String? groupValue;
      String? changedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadio<String>(
                value: 'option1',
                groupValue: groupValue,
                onChanged: (value) => changedValue = value,
                label: 'Test Radio',
              ),
            ),
          ),
        ),
      );

      // Tap the radio button
      await tester.tap(find.byType(VRadio<String>));
      await tester.pump();

      expect(changedValue, equals('option1'));
    });

    testWidgets('should handle tap on label', (tester) async {
      String? groupValue;
      String? changedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadio<String>(
                value: 'option1',
                groupValue: groupValue,
                onChanged: (value) => changedValue = value,
                label: 'Test Label',
              ),
            ),
          ),
        ),
      );

      // Tap the label text
      await tester.tap(find.text('Test Label'));
      await tester.pump();

      expect(changedValue, equals('option1'));
    });

    testWidgets('should show selected state correctly', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadio<String>(
                value: 'option1',
                groupValue: 'option1', // Selected
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      // Should have inner dot when selected
      final animatedContainers = find.byType(AnimatedContainer);
      expect(animatedContainers, findsNWidgets(2)); // Outer circle + inner dot
    });

    testWidgets('should show unselected state correctly', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadio<String>(
                value: 'option1',
                groupValue: 'option2', // Not selected
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      // Should only have outer circle when not selected
      final animatedContainers = find.byType(AnimatedContainer);
      expect(animatedContainers, findsOneWidget); // Only outer circle
    });

    testWidgets('should not call onChanged when already selected', (tester) async {
      String? changedValue;
      int callCount = 0;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadio<String>(
                value: 'option1',
                groupValue: 'option1', // Already selected
                onChanged: (value) {
                  changedValue = value;
                  callCount++;
                },
              ),
            ),
          ),
        ),
      );

      // Tap the already selected radio button
      await tester.tap(find.byType(VRadio<String>));
      await tester.pump();

      expect(callCount, equals(0));
      expect(changedValue, isNull);
    });

    testWidgets('should be disabled when enabled is false', (tester) async {
      String? changedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadio<String>(
                value: 'option1',
                groupValue: null,
                enabled: false,
                onChanged: (value) => changedValue = value,
              ),
            ),
          ),
        ),
      );

      // Try to tap the disabled radio button
      await tester.tap(find.byType(VRadio<String>));
      await tester.pump();

      expect(changedValue, isNull);
    });

    testWidgets('should have reduced opacity when disabled', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadio<String>(
                value: 'option1',
                groupValue: null,
                enabled: false,
                onChanged: (value) {},
                label: 'Disabled',
              ),
            ),
          ),
        ),
      );

      final opacity = tester.widget<Opacity>(find.byType(Opacity));
      expect(opacity.opacity, equals(0.6));
    });

    testWidgets('should work without onChanged callback', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadio<String>(
                value: 'option1',
                groupValue: 'option1',
                onChanged: null,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(VRadio<String>), findsOneWidget);
      
      // Should not crash when tapped
      await tester.tap(find.byType(VRadio<String>));
      await tester.pump();
    });

    testWidgets('should work with different value types', (tester) async {
      int? groupValue;
      int? changedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadio<int>(
                value: 42,
                groupValue: groupValue,
                onChanged: (value) => changedValue = value,
                label: 'Number Option',
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(VRadio<int>));
      await tester.pump();

      expect(changedValue, equals(42));
    });

    testWidgets('should handle null group value', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadio<String>(
                value: 'option1',
                groupValue: null,
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      expect(find.byType(VRadio<String>), findsOneWidget);
      
      // Should show unselected state
      final animatedContainers = find.byType(AnimatedContainer);
      expect(animatedContainers, findsOneWidget); // Only outer circle
    });
  });
}
