import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VCalendar', () {
    testWidgets('renders with default properties', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VCalendar(),
            ),
          ),
        ),
      );

      expect(find.byType(VCalendar), findsOneWidget);
      expect(find.text('Month'), findsOneWidget);
      expect(find.text('Week'), findsOneWidget);
      expect(find.text('Day'), findsOneWidget);
    });

    testWidgets('displays current month by default', (WidgetTester tester) async {
      final now = DateTime.now();
      
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const <PERSON><PERSON><PERSON>(
              body: VCalendar(),
            ),
          ),
        ),
      );

      // Check that current month is displayed
      final monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      final currentMonthName = monthNames[now.month - 1];
      expect(find.textContaining(currentMonthName), findsOneWidget);
      expect(find.textContaining('${now.year}'), findsOneWidget);
    });

    testWidgets('switches between views when view buttons are tapped', (WidgetTester tester) async {
      VCalendarView? changedView;
      
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VCalendar(
                onViewChanged: (view) => changedView = view,
              ),
            ),
          ),
        ),
      );

      // Tap Week view
      await tester.tap(find.text('Week'));
      await tester.pumpAndSettle();
      expect(changedView, equals(VCalendarView.week));

      // Tap Day view
      await tester.tap(find.text('Day'));
      await tester.pumpAndSettle();
      expect(changedView, equals(VCalendarView.day));

      // Tap Month view
      await tester.tap(find.text('Month'));
      await tester.pumpAndSettle();
      expect(changedView, equals(VCalendarView.month));
    });

    testWidgets('navigates between months with navigation buttons', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VCalendar(
                initialDate: DateTime(2024, 6, 15), // June 2024
              ),
            ),
          ),
        ),
      );

      // Verify initial month
      expect(find.textContaining('June'), findsOneWidget);

      // Navigate to next month
      await tester.tap(find.byIcon(Icons.chevron_right));
      await tester.pumpAndSettle();
      expect(find.textContaining('July'), findsOneWidget);

      // Navigate to previous month (twice to go back to May)
      await tester.tap(find.byIcon(Icons.chevron_left));
      await tester.pumpAndSettle();
      await tester.tap(find.byIcon(Icons.chevron_left));
      await tester.pumpAndSettle();
      expect(find.textContaining('May'), findsOneWidget);
    });

    testWidgets('navigates to today with Today button', (WidgetTester tester) async {
      final now = DateTime.now();
      final pastDate = DateTime(2020, 1, 15); // January 2020

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VCalendar(
                initialDate: pastDate,
              ),
            ),
          ),
        ),
      );

      // Verify we're in January 2020
      expect(find.textContaining('January'), findsOneWidget);
      expect(find.textContaining('2020'), findsOneWidget);

      // Tap Today button
      await tester.tap(find.text('Today'));
      await tester.pumpAndSettle();

      // Should now show current month/year
      final monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      final currentMonthName = monthNames[now.month - 1];
      expect(find.textContaining(currentMonthName), findsOneWidget);
      expect(find.textContaining('${now.year}'), findsOneWidget);
    });

    testWidgets('Today button shows active state when viewing current period', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VCalendar(), // Defaults to current date
            ),
          ),
        ),
      );

      // Today button should be present
      expect(find.text('Today'), findsOneWidget);

      // In a real test, you might check for specific styling properties
      // that indicate the active state, but that's complex with widget testing
    });

    testWidgets('calls onDateSelected when date is tapped', (WidgetTester tester) async {
      DateTime? selectedDate;
      
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VCalendar(
                initialDate: DateTime(2024, 6, 15),
                onDateSelected: (date) => selectedDate = date,
              ),
            ),
          ),
        ),
      );

      // Tap on day 10
      await tester.tap(find.text('10'));
      await tester.pumpAndSettle();
      
      expect(selectedDate, isNotNull);
      expect(selectedDate!.day, equals(10));
      expect(selectedDate!.month, equals(6));
      expect(selectedDate!.year, equals(2024));
    });

    testWidgets('displays events with indicators', (WidgetTester tester) async {
      final events = [
        VCalendarEvent(
          id: '1',
          title: 'Meeting',
          date: DateTime(2024, 6, 15),
        ),
        VCalendarEvent(
          id: '2',
          title: 'Lunch',
          date: DateTime(2024, 6, 15),
          color: Colors.green,
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VCalendar(
                initialDate: DateTime(2024, 6, 15),
                events: events,
              ),
            ),
          ),
        ),
      );

      // Events should be displayed as indicators in month view
      expect(find.byType(VCalendar), findsOneWidget);
      // Note: Event indicators are small containers, hard to test directly
      // In a real test, you might check for specific container properties
    });

    testWidgets('displays events in day view', (WidgetTester tester) async {
      final events = [
        VCalendarEvent(
          id: '1',
          title: 'Team Meeting',
          date: DateTime(2024, 6, 15),
          description: 'Weekly team sync',
          startTime: const TimeOfDay(hour: 9, minute: 0),
          endTime: const TimeOfDay(hour: 10, minute: 0),
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VCalendar(
                initialDate: DateTime(2024, 6, 15),
                initialView: VCalendarView.day,
                events: events,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Team Meeting'), findsOneWidget);
      expect(find.text('Weekly team sync'), findsOneWidget);
      expect(find.textContaining('9:00 AM'), findsOneWidget);
    });

    testWidgets('calls onEventTapped when event is tapped in day view', (WidgetTester tester) async {
      VCalendarEvent? tappedEvent;
      final event = VCalendarEvent(
        id: '1',
        title: 'Tappable Event',
        date: DateTime(2024, 6, 15),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VCalendar(
                initialDate: DateTime(2024, 6, 15),
                initialView: VCalendarView.day,
                events: [event],
                onEventTapped: (e) => tappedEvent = e,
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Tappable Event'));
      await tester.pumpAndSettle();
      
      expect(tappedEvent, equals(event));
    });

    testWidgets('hides navigation controls when showNavigationControls is false', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VCalendar(
                showNavigationControls: false,
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.chevron_left), findsNothing);
      expect(find.byIcon(Icons.chevron_right), findsNothing);
    });

    testWidgets('hides view switcher when showViewSwitcher is false', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VCalendar(
                showViewSwitcher: false,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Month'), findsNothing);
      expect(find.text('Week'), findsNothing);
      expect(find.text('Day'), findsNothing);
    });

    testWidgets('respects custom first day of week', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VCalendar(
                firstDayOfWeek: DateTime.sunday,
              ),
            ),
          ),
        ),
      );

      // Check that Sunday appears first in weekday headers
      // This is a simplified test - in reality you'd check the order of weekday labels
      expect(find.byType(VCalendar), findsOneWidget);
    });

    group('VCalendarEvent', () {
      test('creates event with required properties', () {
        final event = VCalendarEvent(
          id: 'test-id',
          title: 'Test Event',
          date: DateTime(2024, 6, 15),
        );

        expect(event.id, equals('test-id'));
        expect(event.title, equals('Test Event'));
        expect(event.date, equals(DateTime(2024, 6, 15)));
        expect(event.isAllDay, isFalse);
        expect(event.description, isNull);
        expect(event.color, isNull);
        expect(event.startTime, isNull);
        expect(event.endTime, isNull);
      });

      test('creates event with all properties', () {
        final event = VCalendarEvent(
          id: 'test-id',
          title: 'Test Event',
          date: DateTime(2024, 6, 15),
          description: 'Test description',
          color: Colors.red,
          isAllDay: false,
          startTime: const TimeOfDay(hour: 9, minute: 0),
          endTime: const TimeOfDay(hour: 10, minute: 0),
        );

        expect(event.id, equals('test-id'));
        expect(event.title, equals('Test Event'));
        expect(event.date, equals(DateTime(2024, 6, 15)));
        expect(event.description, equals('Test description'));
        expect(event.color, equals(Colors.red));
        expect(event.isAllDay, isFalse);
        expect(event.startTime, equals(const TimeOfDay(hour: 9, minute: 0)));
        expect(event.endTime, equals(const TimeOfDay(hour: 10, minute: 0)));
      });
    });
  });
}
