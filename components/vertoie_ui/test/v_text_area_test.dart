import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VTextArea Tests', () {
    testWidgets('should render basic text area', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: const MaterialApp(
            home: Scaffold(
              body: VTextArea(
                placeholder: 'Enter text',
              ),
            ),
          ),
        ),
      );

      expect(find.byType(VTextArea), findsOneWidget);
      expect(find.byType(TextFormField), findsOneWidget);
      expect(find.text('Enter text'), findsOneWidget);
    });

    testWidgets('should handle text input', (tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VText<PERSON>rea(
                controller: controller,
                placeholder: 'Enter text',
              ),
            ),
          ),
        ),
      );

      await tester.enterText(find.byType(TextFormField), 'Hello\nWorld');
      expect(controller.text, 'Hello\nWorld');
    });

    testWidgets('should show character counter when enabled', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: const MaterialApp(
            home: Scaffold(
              body: VTextArea(
                placeholder: 'Enter text',
                maxLength: 100,
                showCharacterCounter: true,
              ),
            ),
          ),
        ),
      );

      expect(find.text('0/100'), findsOneWidget);
    });

    testWidgets('should be disabled when enabled is false', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: const MaterialApp(
            home: Scaffold(
              body: VTextArea(
                placeholder: 'Enter text',
                enabled: false,
              ),
            ),
          ),
        ),
      );

      final textField = tester.widget<TextFormField>(find.byType(TextFormField));
      expect(textField.enabled, false);
    });
  });
}
