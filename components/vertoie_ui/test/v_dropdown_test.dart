import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VDropdown Tests', () {
    testWidgets('should render dropdown with items', (WidgetTester tester) async {
      String? selectedValue;
      
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDropdown<String>(
                label: 'Test Dropdown',
                items: ['Item 1', 'Item 2', 'Item 3']
                    .map((item) => VDropdownItem(
                          value: item,
                          child: Text(item),
                        ))
                    .toList(),
                onChanged: (value) {
                  selectedValue = value;
                },
              ),
            ),
          ),
        ),
      );

      // Verify the label is displayed
      expect(find.text('Test Dropdown'), findsOneWidget);
      
      // Verify the hint is displayed initially
      expect(find.text('Select an option'), findsOneWidget);
      
      // Tap the dropdown to open it
      await tester.tap(find.byType(DropdownButton<String>));
      await tester.pumpAndSettle();
      
      // Verify all items are displayed
      expect(find.text('Item 1'), findsOneWidget);
      expect(find.text('Item 2'), findsOneWidget);
      expect(find.text('Item 3'), findsOneWidget);
      
      // Select an item
      await tester.tap(find.text('Item 2').last);
      await tester.pumpAndSettle();
      
      // Verify the selected value is updated
      expect(selectedValue, equals('Item 2'));
    });

    testWidgets('should handle disabled state', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDropdown<String>(
                label: 'Disabled Dropdown',
                enabled: false,
                items: ['Item 1', 'Item 2']
                    .map((item) => VDropdownItem(
                          value: item,
                          child: Text(item),
                        ))
                    .toList(),
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      // Verify the dropdown is disabled
      final dropdown = tester.widget<DropdownButton<String>>(
        find.byType(DropdownButton<String>),
      );
      expect(dropdown.onChanged, isNull);
    });

    testWidgets('should display custom hint', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDropdown<String>(
                hint: 'Custom hint text',
                items: ['Item 1']
                    .map((item) => VDropdownItem(
                          value: item,
                          child: Text(item),
                        ))
                    .toList(),
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      expect(find.text('Custom hint text'), findsOneWidget);
    });

    testWidgets('should show selected value', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDropdown<String>(
                value: 'Item 2',
                items: ['Item 1', 'Item 2', 'Item 3']
                    .map((item) => VDropdownItem(
                          value: item,
                          child: Text(item),
                        ))
                    .toList(),
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      // The dropdown should show the selected value
      expect(find.text('Item 2'), findsOneWidget);
    });

    testWidgets('should respect theme styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData(
              cornerStyle: VCornerStyle.rounded,
              isDark: true,
            ),
            child: Scaffold(
              body: VDropdown<String>(
                items: ['Item 1']
                    .map((item) => VDropdownItem(
                          value: item,
                          child: Text(item),
                        ))
                    .toList(),
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      // Verify the dropdown container has the expected styling
      final container = tester.widget<Container>(
        find.descendant(
          of: find.byType(VDropdown<String>),
          matching: find.byType(Container),
        ).first,
      );

      final decoration = container.decoration as BoxDecoration?;
      expect(decoration?.borderRadius, isNotNull);
      expect(decoration?.border, isNotNull);
    });
  });
}
