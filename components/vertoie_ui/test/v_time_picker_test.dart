import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VTimePicker', () {
    testWidgets('renders with placeholder when no value is set', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VTimePicker(
                placeholder: 'Select time',
              ),
            ),
          ),
        ),
      );

      expect(find.text('Select time'), findsOneWidget);
      expect(find.byIcon(Icons.access_time), findsOneWidget);
    });

    testWidgets('renders with formatted time when value is set (12-hour format)', (tester) async {
      const testTime = TimeOfDay(hour: 14, minute: 30);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VTimePicker(
                value: testTime,
                format: VTimeFormat.hour12,
              ),
            ),
          ),
        ),
      );

      expect(find.text('2:30 PM'), findsOneWidget);
      expect(find.byIcon(Icons.access_time), findsOneWidget);
    });

    testWidgets('renders with formatted time when value is set (24-hour format)', (tester) async {
      const testTime = TimeOfDay(hour: 14, minute: 30);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VTimePicker(
                value: testTime,
                format: VTimeFormat.hour24,
              ),
            ),
          ),
        ),
      );

      expect(find.text('14:30'), findsOneWidget);
      expect(find.byIcon(Icons.access_time), findsOneWidget);
    });

    testWidgets('handles midnight correctly in 12-hour format', (tester) async {
      const testTime = TimeOfDay(hour: 0, minute: 0);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VTimePicker(
                value: testTime,
                format: VTimeFormat.hour12,
              ),
            ),
          ),
        ),
      );

      expect(find.text('12:00 AM'), findsOneWidget);
    });

    testWidgets('handles noon correctly in 12-hour format', (tester) async {
      const testTime = TimeOfDay(hour: 12, minute: 0);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VTimePicker(
                value: testTime,
                format: VTimeFormat.hour12,
              ),
            ),
          ),
        ),
      );

      expect(find.text('12:00 PM'), findsOneWidget);
    });

    testWidgets('shows clear button when value is set and showClearButton is true', (tester) async {
      const testTime = TimeOfDay(hour: 14, minute: 30);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VTimePicker(
                value: testTime,
                showClearButton: true,
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.clear), findsOneWidget);
    });

    testWidgets('hides clear button when showClearButton is false', (tester) async {
      const testTime = TimeOfDay(hour: 14, minute: 30);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VTimePicker(
                value: testTime,
                showClearButton: false,
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.clear), findsNothing);
    });

    testWidgets('calls onChanged when clear button is tapped', (tester) async {
      const testTime = TimeOfDay(hour: 14, minute: 30);
      TimeOfDay? changedValue;

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VTimePicker(
                value: testTime,
                onChanged: (value) => changedValue = value,
                showClearButton: true,
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byIcon(Icons.clear));
      await tester.pump();

      expect(changedValue, isNull);
    });

    testWidgets('shows label when provided', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VTimePicker(
                label: 'Meeting Time',
                placeholder: 'Select time',
              ),
            ),
          ),
        ),
      );

      expect(find.text('Meeting Time'), findsOneWidget);
    });

    testWidgets('uses custom time format when provided', (tester) async {
      const testTime = TimeOfDay(hour: 14, minute: 30);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VTimePicker(
                value: testTime,
                timeFormat: (time) => '${time.hour}h${time.minute}m',
              ),
            ),
          ),
        ),
      );

      expect(find.text('14h30m'), findsOneWidget);
    });

    testWidgets('is disabled when enabled is false', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VTimePicker(
                enabled: false,
                placeholder: 'Select time',
              ),
            ),
          ),
        ),
      );

      final inkWell = tester.widget<InkWell>(find.byType(InkWell));
      expect(inkWell.onTap, isNull);
    });

    testWidgets('opens time picker when tapped', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VTimePicker(
                placeholder: 'Select time',
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(VTimePicker));
      await tester.pumpAndSettle();

      // Check if time picker dialog is shown
      expect(find.byType(TimePickerDialog), findsOneWidget);
    });

    testWidgets('calls onChanged when time is selected from picker', (tester) async {
      TimeOfDay? changedValue;

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VTimePicker(
                onChanged: (value) => changedValue = value,
                placeholder: 'Select time',
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(VTimePicker));
      await tester.pumpAndSettle();

      // Tap OK button (time picker should have a default time selected)
      await tester.tap(find.text('OK'));
      await tester.pumpAndSettle();

      expect(changedValue, isNotNull);
    });

    testWidgets('handles focus correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VTimePicker(
                placeholder: 'Select time',
              ),
            ),
          ),
        ),
      );

      // Focus the time picker
      await tester.tap(find.byType(VTimePicker));
      await tester.pump();

      // Check that the input field has focus styling
      final container = tester.widget<AnimatedContainer>(find.byType(AnimatedContainer));
      final decoration = container.decoration as BoxDecoration;
      expect(decoration.border, isA<Border>());
    });

    testWidgets('formats single digit minutes correctly', (tester) async {
      const testTime = TimeOfDay(hour: 14, minute: 5);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VTimePicker(
                value: testTime,
                format: VTimeFormat.hour24,
              ),
            ),
          ),
        ),
      );

      expect(find.text('14:05'), findsOneWidget);
    });

    testWidgets('formats single digit hours correctly in 12-hour format', (tester) async {
      const testTime = TimeOfDay(hour: 9, minute: 30);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VTimePicker(
                value: testTime,
                format: VTimeFormat.hour12,
              ),
            ),
          ),
        ),
      );

      expect(find.text('9:30 AM'), findsOneWidget);
    });
  });
}
