import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VDateRangePicker', () {
    testWidgets('renders with placeholder when no value is set', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDateRangePicker(
                startPlaceholder: 'Start date',
                endPlaceholder: 'End date',
              ),
            ),
          ),
        ),
      );

      expect(find.text('Start date - End date'), findsOneWidget);
      expect(find.byIcon(Icons.date_range), findsOneWidget);
    });

    testWidgets('renders with formatted date range when value is set', (tester) async {
      final testRange = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateRangePicker(
                value: testRange,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Jan 15 - Jan 20'), findsOneWidget);
      expect(find.byIcon(Icons.date_range), findsOneWidget);
    });

    testWidgets('shows clear button when value is set and showClearButton is true', (tester) async {
      final testRange = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateRangePicker(
                value: testRange,
                showClearButton: true,
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.clear), findsOneWidget);
    });

    testWidgets('hides clear button when showClearButton is false', (tester) async {
      final testRange = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateRangePicker(
                value: testRange,
                showClearButton: false,
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.clear), findsNothing);
    });

    testWidgets('calls onChanged when clear button is tapped', (tester) async {
      final testRange = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );
      VDateRange? changedValue;

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateRangePicker(
                value: testRange,
                onChanged: (value) => changedValue = value,
                showClearButton: true,
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byIcon(Icons.clear));
      await tester.pump();

      expect(changedValue, isNull);
    });

    testWidgets('shows label when provided', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDateRangePicker(
                label: 'Date Range',
                startPlaceholder: 'Start',
                endPlaceholder: 'End',
              ),
            ),
          ),
        ),
      );

      expect(find.text('Date Range'), findsOneWidget);
    });

    testWidgets('uses custom date format when provided', (tester) async {
      final testRange = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateRangePicker(
                value: testRange,
                dateFormat: (date) => '${date.day}/${date.month}',
              ),
            ),
          ),
        ),
      );

      expect(find.text('15/1 - 20/1'), findsOneWidget);
    });

    testWidgets('is disabled when enabled is false', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDateRangePicker(
                enabled: false,
                startPlaceholder: 'Start',
                endPlaceholder: 'End',
              ),
            ),
          ),
        ),
      );

      final inkWell = tester.widget<InkWell>(find.byType(InkWell));
      expect(inkWell.onTap, isNull);
    });

    testWidgets('opens date range picker when tapped', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDateRangePicker(
                startPlaceholder: 'Start',
                endPlaceholder: 'End',
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(VDateRangePicker));
      await tester.pumpAndSettle();

      // Check if date range picker dialog is shown
      expect(find.byType(DateRangePickerDialog), findsOneWidget);
    });

    testWidgets('respects firstDate and lastDate constraints', (tester) async {
      final firstDate = DateTime(2024, 1, 1);
      final lastDate = DateTime(2024, 12, 31);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateRangePicker(
                firstDate: firstDate,
                lastDate: lastDate,
                startPlaceholder: 'Start',
                endPlaceholder: 'End',
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(VDateRangePicker));
      await tester.pumpAndSettle();

      // The date range picker should be constrained to the specified range
      expect(find.byType(DateRangePickerDialog), findsOneWidget);
    });

    testWidgets('handles focus correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDateRangePicker(
                startPlaceholder: 'Start',
                endPlaceholder: 'End',
              ),
            ),
          ),
        ),
      );

      // Focus the date range picker
      await tester.tap(find.byType(VDateRangePicker));
      await tester.pump();

      // Check that the input field has focus styling
      final container = tester.widget<AnimatedContainer>(find.byType(AnimatedContainer));
      final decoration = container.decoration as BoxDecoration;
      expect(decoration.border, isA<Border>());
    });
  });

  group('VDateRange', () {
    test('equality works correctly', () {
      final range1 = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );
      final range2 = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );
      final range3 = VDateRange(
        start: DateTime(2024, 1, 16),
        end: DateTime(2024, 1, 20),
      );

      expect(range1, equals(range2));
      expect(range1, isNot(equals(range3)));
    });

    test('hashCode works correctly', () {
      final range1 = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );
      final range2 = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );

      expect(range1.hashCode, equals(range2.hashCode));
    });

    test('toString works correctly', () {
      final range = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );

      expect(range.toString(), contains('VDateRange'));
      expect(range.toString(), contains('2024-01-15'));
      expect(range.toString(), contains('2024-01-20'));
    });
  });
}
