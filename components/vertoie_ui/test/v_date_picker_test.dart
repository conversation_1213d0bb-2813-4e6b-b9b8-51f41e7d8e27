import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VDatePicker', () {
    testWidgets('renders with placeholder when no value is set', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDatePicker(
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      expect(find.text('Select date'), findsOneWidget);
      expect(find.byIcon(Icons.calendar_today), findsOneWidget);
    });

    testWidgets('renders with formatted date when value is set', (tester) async {
      final testDate = DateTime(2024, 1, 15);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDatePicker(
                value: testDate,
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      expect(find.text('Jan 15, 2024'), findsOneWidget);
      expect(find.byIcon(Icons.calendar_today), findsOneWidget);
    });

    testWidgets('shows clear button when value is set and showClearButton is true', (tester) async {
      final testDate = DateTime(2024, 1, 15);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDatePicker(
                value: testDate,
                showClearButton: true,
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.clear), findsOneWidget);
    });

    testWidgets('hides clear button when showClearButton is false', (tester) async {
      final testDate = DateTime(2024, 1, 15);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDatePicker(
                value: testDate,
                showClearButton: false,
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.clear), findsNothing);
    });

    testWidgets('calls onChanged when clear button is tapped', (tester) async {
      final testDate = DateTime(2024, 1, 15);
      DateTime? changedValue;

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDatePicker(
                value: testDate,
                onChanged: (value) => changedValue = value,
                showClearButton: true,
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byIcon(Icons.clear));
      await tester.pump();

      expect(changedValue, isNull);
    });

    testWidgets('shows label when provided', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDatePicker(
                label: 'Birth Date',
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      expect(find.text('Birth Date'), findsOneWidget);
    });

    testWidgets('uses custom date format when provided', (tester) async {
      final testDate = DateTime(2024, 1, 15);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDatePicker(
                value: testDate,
                dateFormat: (date) => '${date.day}/${date.month}/${date.year}',
              ),
            ),
          ),
        ),
      );

      expect(find.text('15/1/2024'), findsOneWidget);
    });

    testWidgets('is disabled when enabled is false', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDatePicker(
                enabled: false,
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      final inkWell = tester.widget<InkWell>(find.byType(InkWell));
      expect(inkWell.onTap, isNull);
    });

    testWidgets('opens date picker when tapped', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDatePicker(
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(InkWell));
      await tester.pumpAndSettle();

      // Check if date picker dialog is shown
      expect(find.byType(DatePickerDialog), findsOneWidget);
    });

    testWidgets('calls onChanged when date is selected from picker', (tester) async {
      DateTime? changedValue;

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDatePicker(
                onChanged: (value) => changedValue = value,
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(InkWell));
      await tester.pumpAndSettle();

      // Tap OK button (date picker should have a default date selected)
      await tester.tap(find.text('OK'));
      await tester.pumpAndSettle();

      expect(changedValue, isNotNull);
    });

    testWidgets('respects firstDate and lastDate constraints', (tester) async {
      final firstDate = DateTime(2024, 1, 1);
      final lastDate = DateTime(2024, 12, 31);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDatePicker(
                firstDate: firstDate,
                lastDate: lastDate,
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(InkWell));
      await tester.pumpAndSettle();

      // The date picker should be constrained to the specified range
      expect(find.byType(DatePickerDialog), findsOneWidget);
    });

    testWidgets('handles focus correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDatePicker(
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      // Focus the date picker
      await tester.tap(find.byType(InkWell));
      await tester.pump();

      // Check that the input field has focus styling
      final container = tester.widget<AnimatedContainer>(find.byType(AnimatedContainer));
      final decoration = container.decoration as BoxDecoration;
      expect(decoration.border, isA<Border>());
    });
  });
}
