import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VBrandColors', () {
    test('orange scale has correct primary color', () {
      expect(VBrandColors.orange.primary, const Color(0xFFFF6B35));
      expect(VBrandColors.orange.shade500, const Color(0xFFFF6B35));
    });

    test('orange scale has correct light and dark variants', () {
      expect(VBrandColors.orange.light, const Color(0xFFFFA87A)); // shade300
      expect(VBrandColors.orange.dark, const Color(0xFFCC4700)); // shade700
    });

    test('orange scale has correct background colors', () {
      expect(VBrandColors.orange.background, const Color(0xFFFFF4F0)); // shade50
      expect(VBrandColors.orange.backgroundLight, const Color(0xFFFFE5D9)); // shade100
    });

    test('orange scale has correct interaction states', () {
      expect(VBrandColors.orange.hover, const Color(0xFFE65100)); // shade600
      expect(VBrandColors.orange.active, const Color(0xFFCC4700)); // shade700
    });

    test('amber scale has correct primary color', () {
      expect(VBrandColors.amber.primary, const Color(0xFFF7931E));
      expect(VBrandColors.amber.shade500, const Color(0xFFF7931E));
    });

    test('neutral scale has correct text colors', () {
      expect(VBrandColors.neutral.shade900, const Color(0xFF1F2937)); // Primary text
      expect(VBrandColors.neutral.shade700, const Color(0xFF3F3F46)); // Body text
      expect(VBrandColors.neutral.shade500, const Color(0xFF71717A)); // Muted text
    });

    test('dark neutral colors are correctly defined', () {
      expect(VBrandColors.darkNeutral.shade50, const Color(0xFF111827));
      expect(VBrandColors.darkNeutral.shade700, const Color(0xFFE5E7EB));
    });
  });

  group('VColors', () {
    test('defaultColors uses brand colors', () {
      final colors = VColors.defaultColors();

      expect(colors.primary, const Color(0xFFCC4700)); // Orange 700 (darker for better contrast)
      expect(colors.primaryVariant, const Color(0xFFB8400A)); // Orange 800
      expect(colors.secondary, const Color(0xFFB45309)); // Amber 700 (darker for better contrast)
      expect(colors.secondaryVariant, const Color(0xFF92400E)); // Amber 800
    });

    test('defaultColors has correct semantic colors', () {
      final colors = VColors.defaultColors();

      expect(colors.success, const Color(0xFF22C55E));
      expect(colors.warning, const Color(0xFFF59E0B));
      expect(colors.error, const Color(0xFFDC2626)); // Darker for better contrast
      expect(colors.info, const Color(0xFF3B82F6));
    });

    test('defaultColors has correct background colors', () {
      final colors = VColors.defaultColors();
      
      expect(colors.background, const Color(0xFFFFF7F0)); // Warm white
      expect(colors.surface, const Color(0xFFFFFFFF)); // Pure white
    });

    test('can create custom colors', () {
      final colors = VColors.defaultColors();

      expect(colors.primary, const Color(0xFFCC4700)); // Orange 700 (darker for better contrast)
      expect(colors.onSurface, const Color(0xFF1F2937)); // Gray 900
    });

    test('dark theme uses dark variants', () {
      final colors = VColors.dark();

      expect(colors.primary, const Color(0xFFCC4700)); // Orange 700 (darker for better contrast)
      expect(colors.onSurface, const Color(0xFFE5E7EB)); // Light text on dark
      expect(colors.background, const Color(0xFF111827)); // Dark background
    });

    test('brand color scales are included', () {
      final colors = VColors.defaultColors();
      
      expect(colors.orange, VBrandColors.orange);
      expect(colors.amber, VBrandColors.amber);
      expect(colors.neutral, VBrandColors.neutral);
    });
  });

  group('VColorScale', () {
    test('has correct shade properties', () {
      const scale = VColorScale(
        shade50: Color(0xFF000050),
        shade100: Color(0xFF000100),
        shade200: Color(0xFF000200),
        shade300: Color(0xFF000300),
        shade400: Color(0xFF000400),
        shade500: Color(0xFF000500),
        shade600: Color(0xFF000600),
        shade700: Color(0xFF000700),
        shade800: Color(0xFF000800),
        shade900: Color(0xFF000900),
      );

      expect(scale.primary, const Color(0xFF000500)); // shade500
      expect(scale.light, const Color(0xFF000300)); // shade300
      expect(scale.dark, const Color(0xFF000700)); // shade700
      expect(scale.background, const Color(0xFF000050)); // shade50
      expect(scale.backgroundLight, const Color(0xFF000100)); // shade100
      expect(scale.hover, const Color(0xFF000600)); // shade600
      expect(scale.active, const Color(0xFF000700)); // shade700
    });
  });
}
