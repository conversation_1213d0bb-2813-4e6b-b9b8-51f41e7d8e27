import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VDateTimePicker', () {
    testWidgets('renders with placeholder when no value is set (combined field)', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDateTimePicker(
                placeholder: 'Select date & time',
                separateFields: false,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Select date & time'), findsOneWidget);
      expect(find.byIcon(Icons.event_available), findsOneWidget);
    });

    testWidgets('renders with formatted date-time when value is set (combined field)', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 14, 30);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateTimePicker(
                value: testDateTime,
                separateFields: false,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Jan 15, 2024 at 2:30 PM'), findsOneWidget);
      expect(find.byIcon(Icons.event_available), findsOneWidget);
    });

    testWidgets('renders separate fields when separateFields is true', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 14, 30);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateTimePicker(
                value: testDateTime,
                separateFields: true,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Jan 15, 2024'), findsOneWidget);
      expect(find.text('2:30 PM'), findsOneWidget);
      expect(find.byIcon(Icons.calendar_today), findsOneWidget);
      expect(find.byIcon(Icons.access_time), findsOneWidget);
    });

    testWidgets('renders placeholders in separate fields when no value is set', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDateTimePicker(
                separateFields: true,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Date'), findsOneWidget);
      expect(find.text('Time'), findsOneWidget);
    });

    testWidgets('renders custom placeholders in separate fields when provided', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDateTimePicker(
                separateFields: true,
                datePlaceholder: 'Select date',
                timePlaceholder: 'Select time',
              ),
            ),
          ),
        ),
      );

      expect(find.text('Select date'), findsOneWidget);
      expect(find.text('Select time'), findsOneWidget);
    });

    testWidgets('shows clear button when value is set and showClearButton is true', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 14, 30);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateTimePicker(
                value: testDateTime,
                showClearButton: true,
                separateFields: false,
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.clear), findsOneWidget);
    });

    testWidgets('hides clear button when showClearButton is false', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 14, 30);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateTimePicker(
                value: testDateTime,
                showClearButton: false,
                separateFields: false,
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.clear), findsNothing);
    });

    testWidgets('calls onChanged when clear button is tapped', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 14, 30);
      DateTime? changedValue;

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateTimePicker(
                value: testDateTime,
                onChanged: (value) => changedValue = value,
                showClearButton: true,
                separateFields: false,
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byIcon(Icons.clear));
      await tester.pump();

      expect(changedValue, isNull);
    });

    testWidgets('shows label when provided', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDateTimePicker(
                label: 'Appointment Time',
                placeholder: 'Select date & time',
              ),
            ),
          ),
        ),
      );

      expect(find.text('Appointment Time'), findsOneWidget);
    });

    testWidgets('uses custom date-time format when provided', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 14, 30);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateTimePicker(
                value: testDateTime,
                dateTimeFormat: (dateTime) => '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}',
              ),
            ),
          ),
        ),
      );

      expect(find.text('15/1/2024 14:30'), findsOneWidget);
    });

    testWidgets('uses 24-hour format when specified', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 14, 30);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateTimePicker(
                value: testDateTime,
                timeFormat: VTimeFormat.hour24,
                separateFields: true,
              ),
            ),
          ),
        ),
      );

      expect(find.text('14:30'), findsOneWidget);
    });

    testWidgets('is disabled when enabled is false', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDateTimePicker(
                enabled: false,
                placeholder: 'Select date & time',
              ),
            ),
          ),
        ),
      );

      final inkWell = tester.widget<InkWell>(find.byType(InkWell));
      expect(inkWell.onTap, isNull);
    });

    testWidgets('opens date picker first when combined field is tapped', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDateTimePicker(
                placeholder: 'Select date & time',
                separateFields: false,
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(VDateTimePicker));
      await tester.pumpAndSettle();

      // Check if date picker dialog is shown first
      expect(find.byType(DatePickerDialog), findsOneWidget);
    });

    testWidgets('opens date picker when date field is tapped in separate fields', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDateTimePicker(
                separateFields: true,
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byIcon(Icons.calendar_today));
      await tester.pumpAndSettle();

      // Check if date picker dialog is shown
      expect(find.byType(DatePickerDialog), findsOneWidget);
    });

    testWidgets('opens time picker when time field is tapped in separate fields', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDateTimePicker(
                separateFields: true,
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byIcon(Icons.access_time));
      await tester.pumpAndSettle();

      // Check if time picker dialog is shown
      expect(find.byType(TimePickerDialog), findsOneWidget);
    });

    testWidgets('respects firstDate and lastDate constraints', (tester) async {
      final firstDate = DateTime(2024, 1, 1);
      final lastDate = DateTime(2024, 12, 31);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateTimePicker(
                firstDate: firstDate,
                lastDate: lastDate,
                placeholder: 'Select date & time',
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(VDateTimePicker));
      await tester.pumpAndSettle();

      // The date picker should be constrained to the specified range
      expect(find.byType(DatePickerDialog), findsOneWidget);
    });

    testWidgets('handles focus correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDateTimePicker(
                placeholder: 'Select date & time',
              ),
            ),
          ),
        ),
      );

      // Focus the date-time picker
      await tester.tap(find.byType(VDateTimePicker));
      await tester.pump();

      // Check that the input field has focus styling
      final container = tester.widget<AnimatedContainer>(find.byType(AnimatedContainer));
      final decoration = container.decoration as BoxDecoration;
      expect(decoration.border, isA<Border>());
    });

    testWidgets('preserves time when only date is changed in separate fields', (tester) async {
      final initialDateTime = DateTime(2024, 1, 15, 14, 30);
      DateTime? changedValue;

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateTimePicker(
                value: initialDateTime,
                onChanged: (value) => changedValue = value,
                separateFields: true,
              ),
            ),
          ),
        ),
      );

      // Tap the date field
      await tester.tap(find.byIcon(Icons.calendar_today));
      await tester.pumpAndSettle();

      // Select a different date (day 20)
      await tester.tap(find.text('20'));
      await tester.pumpAndSettle();

      // Tap OK
      await tester.tap(find.text('OK'));
      await tester.pumpAndSettle();

      expect(changedValue, isNotNull);
      expect(changedValue!.day, equals(20));
      expect(changedValue!.hour, equals(14)); // Time should be preserved
      expect(changedValue!.minute, equals(30));
    });

    testWidgets('preserves date when only time is changed in separate fields', (tester) async {
      final initialDateTime = DateTime(2024, 1, 15, 14, 30);
      DateTime? changedValue;

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDateTimePicker(
                value: initialDateTime,
                onChanged: (value) => changedValue = value,
                separateFields: true,
              ),
            ),
          ),
        ),
      );

      // Tap the time field
      await tester.tap(find.byIcon(Icons.access_time));
      await tester.pumpAndSettle();

      // Tap OK (time picker should have a time selected)
      await tester.tap(find.text('OK'));
      await tester.pumpAndSettle();

      expect(changedValue, isNotNull);
      expect(changedValue!.day, equals(15)); // Date should be preserved
      expect(changedValue!.month, equals(1));
      expect(changedValue!.year, equals(2024));
    });
  });
}
