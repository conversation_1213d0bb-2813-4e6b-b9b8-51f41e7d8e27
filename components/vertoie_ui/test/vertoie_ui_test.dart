import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VThemeData', () {
    test('creates default theme correctly', () {
      final theme = VThemeData.defaultTheme();
      expect(theme.density, VDensity.comfortable);
      expect(theme.cornerStyle, VCornerStyle.subtle);
      expect(theme.elevationLevel, VElevationLevel.subtle);
      expect(theme.isDark, false);
    });

    test('creates dark theme correctly', () {
      final theme = VThemeData.dark();
      expect(theme.isDark, true);
      expect(theme.density, VDensity.comfortable);
    });

    test('creates custom theme correctly', () {
      final theme = VThemeData.defaultTheme().copyWith(
        density: VDensity.compact,
        cornerStyle: VCornerStyle.rounded,
      );
      expect(theme.density, VDensity.compact);
      expect(theme.cornerStyle, VCornerStyle.rounded);
    });
  });

  group('VComponentDensity', () {
    test('has correct default values', () {
      const density = VComponentDensity.comfortable;
      expect(density.xs, 8);
      expect(density.sm, 16);
      expect(density.md, 24);
      expect(density.lg, 32);
      expect(density.xl, 40);
    });
  });
}
