import 'dart:math' show pow;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

/// Utility class for accessibility testing helpers
class VAccessibilityTestHelpers {
  /// Tests color contrast ratios for accessibility compliance
  static void expectSufficientColorContrast(Color foreground, Color background) {
    final contrast = _calculateContrastRatio(foreground, background);
    expect(
      contrast,
      greaterThanOrEqualTo(4.5),
      reason: 'Color contrast ratio $contrast does not meet WCAG AA standards (4.5:1)',
    );
  }

  /// Tests large text color contrast ratios
  static void expectSufficientLargeTextContrast(Color foreground, Color background) {
    final contrast = _calculateContrastRatio(foreground, background);
    expect(
      contrast,
      greaterThanOrEqualTo(3.0),
      reason: 'Large text contrast ratio $contrast does not meet WCAG AA standards (3:1)',
    );
  }

  /// Calculates the contrast ratio between two colors
  static double _calculateContrastRatio(Color color1, Color color2) {
    final lum1 = _relativeLuminance(color1);
    final lum2 = _relativeLuminance(color2);
    
    final lighter = lum1 > lum2 ? lum1 : lum2;
    final darker = lum1 > lum2 ? lum2 : lum1;
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Calculates the relative luminance of a color
  static double _relativeLuminance(Color color) {
    final r = _linearize(color.red / 255.0);
    final g = _linearize(color.green / 255.0);
    final b = _linearize(color.blue / 255.0);
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// Linearizes an RGB color component
  static double _linearize(double component) {
    if (component <= 0.03928) {
      return component / 12.92;
    } else {
      return pow((component + 0.055) / 1.055, 2.4).toDouble();
    }
  }

  /// Tests that touch targets meet minimum size requirements (44dp)
  static void expectMinimumTouchTarget(WidgetTester tester, Finder finder) {
    final element = tester.element(finder);
    final renderBox = element.renderObject as RenderBox?;
    
    if (renderBox != null) {
      final size = renderBox.size;
      expect(
        size.width,
        greaterThanOrEqualTo(44.0),
        reason: 'Touch target width ${size.width} is below minimum 44dp',
      );
      expect(
        size.height,
        greaterThanOrEqualTo(44.0),
        reason: 'Touch target height ${size.height} is below minimum 44dp',
      );
    }
  }

  /// Verifies that a widget has semantic information
  static void expectHasSemantics(WidgetTester tester, Finder finder) {
    final element = tester.element(finder);
    final renderObject = element.renderObject;
    
    expect(
      renderObject,
      isNotNull,
      reason: 'Widget should have a render object for semantic testing',
    );
  }

  /// Tests that interactive elements are keyboard accessible
  static Future<void> expectKeyboardAccessible(
    WidgetTester tester,
    Finder finder,
  ) async {
    // Focus the widget
    await tester.tap(finder);
    await tester.pump();

    // For buttons, check if they can be focused
    final widget = tester.widget(finder);
    if (widget is ElevatedButton || widget.runtimeType.toString().contains('VButton')) {
      // Just verify the widget exists and can be tapped
      expect(finder, findsOneWidget);
      return;
    }

    // For input fields, verify focus capability
    try {
      final focusNode = Focus.of(tester.element(finder));
      expect(focusNode.hasFocus || focusNode.hasPrimaryFocus, isTrue);
    } catch (e) {
      // If Focus.of fails, just verify the widget exists
      expect(finder, findsOneWidget);
    }

    // Test that Enter/Space can activate it (for buttons)
    await tester.sendKeyEvent(LogicalKeyboardKey.enter);
    await tester.pump();
  }
}
