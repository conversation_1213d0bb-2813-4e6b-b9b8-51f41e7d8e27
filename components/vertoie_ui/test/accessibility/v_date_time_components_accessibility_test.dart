import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

import '../accessibility_test_helpers.dart';

void main() {
  group('Date & Time Components Accessibility Tests', () {
    group('VDateRangePicker', () {
      testWidgets('meets accessibility guidelines', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: const Scaffold(
                body: VDateRangePicker(
                  startPlaceholder: 'Start date',
                  endPlaceholder: 'End date',
                ),
              ),
            ),
          ),
        );

        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });

      testWidgets('has proper semantic labels with value', (tester) async {
        final testRange = VDateRange(
          start: DateTime(2024, 1, 15),
          end: DateTime(2024, 1, 20),
        );

        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: Scaffold(
                body: VDateRangePicker(
                  label: 'Project Duration',
                  value: testRange,
                ),
              ),
            ),
          ),
        );

        expect(find.text('Project Duration'), findsOneWidget);
        expect(find.text('Jan 15 - Jan 20'), findsOneWidget);

        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });

      testWidgets('clear button has proper accessibility', (tester) async {
        final testRange = VDateRange(
          start: DateTime(2024, 1, 15),
          end: DateTime(2024, 1, 20),
        );

        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: Scaffold(
                body: VDateRangePicker(
                  value: testRange,
                  showClearButton: true,
                ),
              ),
            ),
          ),
        );

        expect(find.byIcon(Icons.clear), findsOneWidget);
        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });
    });

    group('VTimePicker', () {
      testWidgets('meets accessibility guidelines', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: const Scaffold(
                body: VTimePicker(
                  placeholder: 'Select time',
                ),
              ),
            ),
          ),
        );

        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });

      testWidgets('has proper semantic labels with 12-hour format', (tester) async {
        const testTime = TimeOfDay(hour: 14, minute: 30);

        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: const Scaffold(
                body: VTimePicker(
                  label: 'Meeting Time',
                  value: testTime,
                  format: VTimeFormat.hour12,
                ),
              ),
            ),
          ),
        );

        expect(find.text('Meeting Time'), findsOneWidget);
        expect(find.text('2:30 PM'), findsOneWidget);

        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });

      testWidgets('has proper semantic labels with 24-hour format', (tester) async {
        const testTime = TimeOfDay(hour: 14, minute: 30);

        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: const Scaffold(
                body: VTimePicker(
                  label: 'Meeting Time',
                  value: testTime,
                  format: VTimeFormat.hour24,
                ),
              ),
            ),
          ),
        );

        expect(find.text('Meeting Time'), findsOneWidget);
        expect(find.text('14:30'), findsOneWidget);

        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });

      testWidgets('handles edge times accessibility', (tester) async {
        const midnight = TimeOfDay(hour: 0, minute: 0);

        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: const Scaffold(
                body: VTimePicker(
                  value: midnight,
                  format: VTimeFormat.hour12,
                ),
              ),
            ),
          ),
        );

        expect(find.text('12:00 AM'), findsOneWidget);
        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });
    });

    group('VDateTimePicker', () {
      testWidgets('meets accessibility guidelines (combined field)', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: const Scaffold(
                body: VDateTimePicker(
                  placeholder: 'Select date & time',
                  separateFields: false,
                ),
              ),
            ),
          ),
        );

        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });

      testWidgets('meets accessibility guidelines (separate fields)', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: const Scaffold(
                body: VDateTimePicker(
                  separateFields: true,
                ),
              ),
            ),
          ),
        );

        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });

      testWidgets('has proper semantic labels with value (combined)', (tester) async {
        final testDateTime = DateTime(2024, 1, 15, 14, 30);

        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: Scaffold(
                body: VDateTimePicker(
                  label: 'Appointment',
                  value: testDateTime,
                  separateFields: false,
                ),
              ),
            ),
          ),
        );

        expect(find.text('Appointment'), findsOneWidget);
        expect(find.text('Jan 15, 2024 at 2:30 PM'), findsOneWidget);

        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });

      testWidgets('has proper semantic labels with value (separate)', (tester) async {
        final testDateTime = DateTime(2024, 1, 15, 14, 30);

        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: Scaffold(
                body: VDateTimePicker(
                  label: 'Event Schedule',
                  value: testDateTime,
                  separateFields: true,
                ),
              ),
            ),
          ),
        );

        expect(find.text('Event Schedule'), findsOneWidget);
        expect(find.text('Jan 15, 2024'), findsOneWidget);
        expect(find.text('2:30 PM'), findsOneWidget);

        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });

      testWidgets('separate fields have proper individual accessibility', (tester) async {
        final testDateTime = DateTime(2024, 1, 15, 14, 30);

        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: Scaffold(
                body: VDateTimePicker(
                  value: testDateTime,
                  separateFields: true,
                ),
              ),
            ),
          ),
        );

        // Both date and time icons should be present and accessible
        expect(find.byIcon(Icons.calendar_today), findsOneWidget);
        expect(find.byIcon(Icons.access_time), findsOneWidget);

        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });

      testWidgets('works with 24-hour format accessibility', (tester) async {
        final testDateTime = DateTime(2024, 1, 15, 14, 30);

        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: Scaffold(
                body: VDateTimePicker(
                  value: testDateTime,
                  timeFormat: VTimeFormat.hour24,
                  separateFields: true,
                ),
              ),
            ),
          ),
        );

        expect(find.text('14:30'), findsOneWidget);
        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });
    });

    group('Cross-component accessibility', () {
      testWidgets('all components work with dark theme', (tester) async {
        final testDate = DateTime(2024, 1, 15);
        final testTime = TimeOfDay(hour: 14, minute: 30);
        final testDateTime = DateTime(2024, 1, 15, 14, 30);
        final testRange = VDateRange(
          start: DateTime(2024, 1, 15),
          end: DateTime(2024, 1, 20),
        );

        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.dark(),
              child: Scaffold(
                body: Column(
                  children: [
                    VDatePicker(value: testDate),
                    VDateRangePicker(value: testRange),
                    VTimePicker(value: testTime),
                    VDateTimePicker(value: testDateTime),
                  ],
                ),
              ),
            ),
          ),
        );

        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });

      testWidgets('all components work with different densities', (tester) async {
        final testDate = DateTime(2024, 1, 15);

        for (final density in [VDensity.compact, VDensity.comfortable, VDensity.spacious]) {
          await tester.pumpWidget(
            MaterialApp(
              home: VThemeProvider(
                theme: VThemeData.defaultTheme().copyWith(density: density),
                child: Scaffold(
                  body: VDatePicker(value: testDate),
                ),
              ),
            ),
          );

          await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
        }
      });

      testWidgets('all components have minimum touch targets', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: const Scaffold(
                body: Column(
                  children: [
                    VDatePicker(placeholder: 'Date'),
                    VDateRangePicker(startPlaceholder: 'Start', endPlaceholder: 'End'),
                    VTimePicker(placeholder: 'Time'),
                    VDateTimePicker(placeholder: 'Date & Time'),
                  ],
                ),
              ),
            ),
          ),
        );

        // Check that all components meet minimum touch target size
        final containers = tester.widgetList<AnimatedContainer>(find.byType(AnimatedContainer));
        for (final container in containers) {
          expect(container.constraints?.minHeight, greaterThanOrEqualTo(44.0));
        }

        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });

      testWidgets('all components support keyboard navigation', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme(),
              child: const Scaffold(
                body: Column(
                  children: [
                    VDatePicker(placeholder: 'Date'),
                    VTimePicker(placeholder: 'Time'),
                  ],
                ),
              ),
            ),
          ),
        );

        // Test tab navigation
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pump();
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pump();

        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      });
    });
  });
}
