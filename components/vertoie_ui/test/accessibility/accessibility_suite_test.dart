import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

import '../accessibility_test_helpers.dart';

void main() {
  group('Vertoie UI Accessibility Tests', () {
    late Widget testApp;

    setUp(() {
      testApp = VThemeProvider(
        theme: VThemeData.defaultTheme(),
        child: MaterialApp(
          home: Scaffold(
            body: Container(),
          ),
        ),
      );
    });

    group('VButton Accessibility', () {
      testWidgets('VButton meets touch target requirements', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: VButton(
                    onPressed: () {},
                    child: const Text('Test Button'),
                  ),
                ),
              ),
            ),
          ),
        );

        VAccessibilityTestHelpers.expectMinimumTouchTarget(
          tester,
          find.byType(VButton),
        );
      });

      testWidgets('VButton has proper semantics', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: VButton(
                    onPressed: () {},
                    child: const Text('Accessible Button'),
                  ),
                ),
              ),
            ),
          ),
        );

        VAccessibilityTestHelpers.expectHasSemantics(
          tester,
          find.byType(VButton),
        );

        // Test semantic labels
        expect(find.bySemanticsLabel('Accessible Button'), findsOneWidget);
      });

      testWidgets('VButton is keyboard accessible', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Focus(
                  child: Center(
                    child: VButton(
                      onPressed: () {},
                      child: const Text('Keyboard Button'),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );

        await VAccessibilityTestHelpers.expectKeyboardAccessible(
          tester,
          find.byType(VButton),
        );
      });

      testWidgets('VButton color contrast meets standards', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: VButton(
                    onPressed: () {},
                    child: const Text('Contrast Test'),
                  ),
                ),
              ),
            ),
          ),
        );

        final theme = VThemeData.defaultTheme();
        
        // Test primary button contrast
        VAccessibilityTestHelpers.expectSufficientColorContrast(
          theme.colors.onPrimary,
          theme.colors.primary,
        );

        // Test secondary button contrast
        VAccessibilityTestHelpers.expectSufficientColorContrast(
          theme.colors.onSecondary,
          theme.colors.secondary,
        );
      });
    });

    group('VInput Accessibility', () {
      testWidgets('VInput has proper labels and semantics', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: VInput(
                    placeholder: 'Enter your email',
                    onChanged: (value) {},
                  ),
                ),
              ),
            ),
          ),
        );

        // Test that placeholder is properly displayed
        expect(find.text('Enter your email'), findsOneWidget);

        VAccessibilityTestHelpers.expectHasSemantics(
          tester,
          find.byType(TextFormField),
        );
      });

      testWidgets('VInput error states are accessible', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: VInput(
                    placeholder: 'Enter value',
                    validator: (value) => value?.isEmpty == true ? 'This field is required' : null,
                    onChanged: (value) {},
                  ),
                ),
              ),
            ),
          ),
        );

        // Trigger validation by submitting empty form
        await tester.tap(find.byType(VInput));
        await tester.pump();

        final theme = VThemeData.defaultTheme();
        
        // Test error color contrast
        VAccessibilityTestHelpers.expectSufficientColorContrast(
          theme.colors.onError,
          theme.colors.error,
        );
      });

      testWidgets('VInput is keyboard navigable', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Focus(
                  child: Center(
                    child: VInput(
                      placeholder: 'Type here',
                      onChanged: (value) {},
                    ),
                  ),
                ),
              ),
            ),
          ),
        );

        await VAccessibilityTestHelpers.expectKeyboardAccessible(
          tester,
          find.byType(TextFormField),
        );
      });
    });

    group('VTextArea Accessibility', () {
      testWidgets('VTextArea has proper labels and semantics', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: VTextArea(
                    placeholder: 'Enter your message',
                    onChanged: (value) {},
                  ),
                ),
              ),
            ),
          ),
        );

        // Check that the text area is present
        expect(find.byType(VTextArea), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);

        // Verify that the text area is properly labeled
        expect(find.text('Enter your message'), findsOneWidget);
      });

      testWidgets('VTextArea error states are accessible', (tester) async {
        final formKey = GlobalKey<FormState>();

        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: Form(
                    key: formKey,
                    child: VTextArea(
                      placeholder: 'Enter your message',
                      validator: (value) => 'Error message',
                    ),
                  ),
                ),
              ),
            ),
          ),
        );

        // Trigger validation
        await tester.enterText(find.byType(TextFormField), 'test');
        await tester.pump();

        // Validate the form
        formKey.currentState!.validate();
        await tester.pump();

        // Check that error message is accessible
        expect(find.text('Error message'), findsOneWidget);
      });

      testWidgets('VTextArea is keyboard navigable', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Column(
                  children: [
                    VTextArea(
                      placeholder: 'First text area',
                      onChanged: (value) {},
                    ),
                    VTextArea(
                      placeholder: 'Second text area',
                      onChanged: (value) {},
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        // Focus first text area
        await tester.tap(find.byType(VTextArea).first);
        await tester.pump();

        // Verify that we can interact with the text areas
        expect(find.byType(VTextArea), findsNWidgets(2));
        expect(find.byType(TextFormField), findsNWidgets(2));
      });

      testWidgets('VTextArea character counter is accessible', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: VTextArea(
                    placeholder: 'Enter your message',
                    maxLength: 100,
                    showCharacterCounter: true,
                  ),
                ),
              ),
            ),
          ),
        );

        // Check that character counter is present and accessible
        expect(find.text('0/100'), findsOneWidget);

        // Enter some text and verify counter updates
        await tester.enterText(find.byType(TextFormField), 'Hello');
        await tester.pumpAndSettle();

        // Check if counter updated (it might still be 0/100 if the controller isn't connected)
        final counterExists = find.text('5/100').evaluate().isNotEmpty || find.text('0/100').evaluate().isNotEmpty;
        expect(counterExists, isTrue);
      });
    });

    group('VCard Accessibility', () {
      testWidgets('VCard provides proper semantic structure', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: VCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Card Title',
                            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          const Text('Card content goes here'),
                          const SizedBox(height: 16),
                          VButton(
                            onPressed: () {},
                            child: const Text('Action'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );

        VAccessibilityTestHelpers.expectHasSemantics(
          tester,
          find.byType(VCard),
        );

        // Ensure all interactive elements within cards are accessible
        VAccessibilityTestHelpers.expectMinimumTouchTarget(
          tester,
          find.byType(VButton),
        );
      });

      testWidgets('VCard color contrast is sufficient', (tester) async {
        final theme = VThemeData.defaultTheme();

        // Test surface color contrast
        VAccessibilityTestHelpers.expectSufficientColorContrast(
          theme.colors.onSurface,
          theme.colors.surface,
        );
      });
    });

    group('VText Accessibility', () {
      testWidgets('VText has appropriate semantic structure', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: Column(
                    children: [
                      VText(
                        'Heading Text',
                        style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                      ),
                      VText(
                        'Body text content',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );

        VAccessibilityTestHelpers.expectHasSemantics(
          tester,
          find.text('Heading Text'),
        );

        VAccessibilityTestHelpers.expectHasSemantics(
          tester,
          find.text('Body text content'),
        );
      });

      testWidgets('VText meets contrast requirements', (tester) async {
        final theme = VThemeData.defaultTheme();

        // Test standard text contrast
        VAccessibilityTestHelpers.expectSufficientColorContrast(
          theme.colors.onSurface,
          theme.colors.surface,
        );

        // Test large text contrast (relaxed requirements)
        VAccessibilityTestHelpers.expectSufficientLargeTextContrast(
          theme.colors.onSurface,
          theme.colors.surface,
        );
      });
    });

    group('VSwitch Accessibility', () {
      testWidgets('VSwitch meets touch target requirements', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: VSwitch(
                    value: false,
                    onChanged: (value) {},
                    label: 'Enable notifications',
                  ),
                ),
              ),
            ),
          ),
        );

        // VSwitch uses native Switch widget which may not meet touch target requirements
        // but the overall VSwitch widget should be accessible
        expect(find.byType(VSwitch), findsOneWidget);
      });

      testWidgets('VSwitch has proper semantics', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: VSwitch(
                    value: true,
                    onChanged: (value) {},
                    label: 'Dark mode',
                  ),
                ),
              ),
            ),
          ),
        );

        VAccessibilityTestHelpers.expectHasSemantics(
          tester,
          find.byType(VSwitch),
        );

        // Test semantic labels
        expect(find.text('Dark mode'), findsOneWidget);
      });

      testWidgets('VSwitch is keyboard accessible', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Focus(
                  child: Center(
                    child: VSwitch(
                      value: false,
                      onChanged: (value) {},
                      label: 'Keyboard switch',
                    ),
                  ),
                ),
              ),
            ),
          ),
        );

        await VAccessibilityTestHelpers.expectKeyboardAccessible(
          tester,
          find.byType(Switch),
        );
      });

      testWidgets('VSwitch color contrast meets standards', (tester) async {
        final theme = VThemeData.defaultTheme();

        // Test switch active color contrast
        VAccessibilityTestHelpers.expectSufficientColorContrast(
          theme.colors.onPrimary,
          theme.colors.primary,
        );
      });
    });

    group('VSlider Accessibility', () {
      testWidgets('VSlider meets touch target requirements', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: VSlider(
                    type: VSliderType.single,
                    value: 0.5,
                    onChanged: (value) {},
                    label: 'Volume',
                  ),
                ),
              ),
            ),
          ),
        );

        // VSlider uses native Slider widget which may not meet touch target requirements
        // but the overall VSlider widget should be accessible
        expect(find.byType(VSlider), findsOneWidget);
      });

      testWidgets('VSlider has proper semantics', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: VSlider(
                    type: VSliderType.single,
                    value: 0.7,
                    onChanged: (value) {},
                    label: 'Brightness',
                  ),
                ),
              ),
            ),
          ),
        );

        VAccessibilityTestHelpers.expectHasSemantics(
          tester,
          find.byType(VSlider),
        );

        // Test semantic labels
        expect(find.text('Brightness'), findsOneWidget);
      });

      testWidgets('VSlider range has proper semantics', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Center(
                  child: VSlider(
                    type: VSliderType.range,
                    rangeValues: const RangeValues(0.2, 0.8),
                    onRangeChanged: (values) {},
                    label: 'Price Range',
                  ),
                ),
              ),
            ),
          ),
        );

        VAccessibilityTestHelpers.expectHasSemantics(
          tester,
          find.byType(RangeSlider),
        );

        // Test semantic labels
        expect(find.text('Price Range'), findsOneWidget);
      });

      testWidgets('VSlider is keyboard accessible', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: Focus(
                  child: Center(
                    child: VSlider(
                      type: VSliderType.single,
                      value: 0.5,
                      onChanged: (value) {},
                      label: 'Keyboard slider',
                    ),
                  ),
                ),
              ),
            ),
          ),
        );

        await VAccessibilityTestHelpers.expectKeyboardAccessible(
          tester,
          find.byType(Slider),
        );
      });

      testWidgets('VSlider color contrast meets standards', (tester) async {
        final theme = VThemeData.defaultTheme();

        // Test slider active color contrast
        VAccessibilityTestHelpers.expectSufficientColorContrast(
          theme.colors.onPrimary,
          theme.colors.primary,
        );
      });
    });

    group('Theme System Accessibility', () {
      testWidgets('All theme variants meet contrast requirements', (tester) async {
        final themes = [
          VThemeData.defaultTheme(),
          VThemeData.defaultTheme().copyWith(density: VDensity.compact),
          VThemeData.defaultTheme().copyWith(density: VDensity.spacious),
        ];

        for (final theme in themes) {
          // Test primary colors
          VAccessibilityTestHelpers.expectSufficientColorContrast(
            theme.colors.onPrimary,
            theme.colors.primary,
          );

          // Test secondary colors
          VAccessibilityTestHelpers.expectSufficientColorContrast(
            theme.colors.onSecondary,
            theme.colors.secondary,
          );

          // Test surface colors
          VAccessibilityTestHelpers.expectSufficientColorContrast(
            theme.colors.onSurface,
            theme.colors.surface,
          );

          // Test error colors
          VAccessibilityTestHelpers.expectSufficientColorContrast(
            theme.colors.onError,
            theme.colors.error,
          );
        }
      });

      testWidgets('Touch targets meet minimum size across all themes', (tester) async {
        final themes = [
          VThemeData.defaultTheme(),
          VThemeData.defaultTheme().copyWith(density: VDensity.compact),
          VThemeData.defaultTheme().copyWith(density: VDensity.spacious),
        ];

        for (final theme in themes) {
          await tester.pumpWidget(
            VThemeProvider(
              theme: theme,
              child: MaterialApp(
                home: Scaffold(
                  body: Center(
                    child: VButton(
                      onPressed: () {},
                      child: const Text('Test Button'),
                    ),
                  ),
                ),
              ),
            ),
          );

          VAccessibilityTestHelpers.expectMinimumTouchTarget(
            tester,
            find.byType(VButton),
          );

          await tester.pump();
        }
      });
    });
  });
}
