import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

import '../accessibility_test_helpers.dart';

void main() {
  group('VDatePicker Accessibility Tests', () {
    testWidgets('meets accessibility guidelines', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDatePicker(
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
    });

    testWidgets('has proper semantic labels', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDatePicker(
                label: 'Birth Date',
                placeholder: 'Select your birth date',
              ),
            ),
          ),
        ),
      );

      // Check that the label is present
      expect(find.text('Birth Date'), findsOneWidget);
      
      // Check that the placeholder is present
      expect(find.text('Select your birth date'), findsOneWidget);

      await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
    });

    testWidgets('is focusable and has proper focus behavior', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDatePicker(
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      // Find the focusable element
      final focusableWidget = find.byType(InkWell);
      expect(focusableWidget, findsOneWidget);

      // Test focus behavior
      await tester.tap(focusableWidget);
      await tester.pump();

      await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
    });

    testWidgets('has proper semantics when disabled', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDatePicker(
                enabled: false,
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      // Check that the widget is not interactive when disabled
      final inkWell = tester.widget<InkWell>(find.byType(InkWell));
      expect(inkWell.onTap, isNull);

      await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
    });

    testWidgets('has proper semantics with value', (tester) async {
      final testDate = DateTime(2024, 1, 15);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDatePicker(
                value: testDate,
                label: 'Selected Date',
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      // Check that the formatted date is displayed
      expect(find.text('Jan 15, 2024'), findsOneWidget);

      await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
    });

    testWidgets('clear button has proper accessibility', (tester) async {
      final testDate = DateTime(2024, 1, 15);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDatePicker(
                value: testDate,
                showClearButton: true,
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      // Check that clear button is present and accessible
      expect(find.byIcon(Icons.clear), findsOneWidget);

      await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
    });

    testWidgets('has proper contrast ratios', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDatePicker(
                label: 'Date Field',
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
    });

    testWidgets('works with dark theme', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.dark(),
            child: const Scaffold(
              body: VDatePicker(
                label: 'Date Field',
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
    });

    testWidgets('has minimum touch target size', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDatePicker(
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      // Check that the touch target meets minimum size requirements
      final container = tester.widget<AnimatedContainer>(find.byType(AnimatedContainer));
      expect(container.constraints?.minHeight, greaterThanOrEqualTo(44.0));

      await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
    });

    testWidgets('supports screen readers', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDatePicker(
                label: 'Appointment Date',
                placeholder: 'Select appointment date',
              ),
            ),
          ),
        ),
      );

      // Test that screen readers can find the semantic information
      expect(find.text('Appointment Date'), findsOneWidget);
      expect(find.text('Select appointment date'), findsOneWidget);

      await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
    });

    testWidgets('handles keyboard navigation', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDatePicker(
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      // Test keyboard focus
      await tester.sendKeyEvent(LogicalKeyboardKey.tab);
      await tester.pump();

      await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
    });

    testWidgets('has proper semantic roles', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: const Scaffold(
              body: VDatePicker(
                label: 'Date Input',
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      // The widget should be semantically identified as a button/input
      final semantics = tester.getSemantics(find.byType(VDatePicker));
      expect(semantics.hasAction(SemanticsAction.tap), isTrue);

      await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
    });

    testWidgets('provides proper feedback for state changes', (tester) async {
      DateTime? selectedDate;

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDatePicker(
                onChanged: (date) => selectedDate = date,
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      // Test that state changes are properly communicated
      await tester.tap(find.byType(VDatePicker));
      await tester.pumpAndSettle();

      // Date picker dialog should be accessible
      expect(find.byType(DatePickerDialog), findsOneWidget);

      await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
    });

    testWidgets('works with different density settings', (tester) async {
      final densities = [VDensity.compact, VDensity.comfortable, VDensity.spacious];

      for (final density in densities) {
        await tester.pumpWidget(
          MaterialApp(
            home: VThemeProvider(
              theme: VThemeData.defaultTheme().copyWith(density: density),
              child: const Scaffold(
                body: VDatePicker(
                  label: 'Date Field',
                  placeholder: 'Select date',
                ),
              ),
            ),
          ),
        );

        await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
      }
    });

    testWidgets('maintains accessibility with custom formatting', (tester) async {
      final testDate = DateTime(2024, 1, 15);

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDatePicker(
                value: testDate,
                dateFormat: (date) => 'Custom: ${date.day}/${date.month}/${date.year}',
                label: 'Custom Date',
                placeholder: 'Select date',
              ),
            ),
          ),
        ),
      );

      // Check that custom formatted date is accessible
      expect(find.text('Custom: 15/1/2024'), findsOneWidget);

      await VAccessibilityTestHelpers.meetsAccessibilityGuidelines(tester);
    });
  });
}
