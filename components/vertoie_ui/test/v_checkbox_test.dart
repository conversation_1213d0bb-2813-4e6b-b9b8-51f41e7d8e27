import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VCheckbox Tests', () {
    testWidgets('should render basic checkbox', (tester) async {
      bool? value = false;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckbox(
                value: value,
                onChanged: (newValue) => value = newValue,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(VCheckbox), findsOneWidget);
      expect(find.byType(GestureDetector), findsOneWidget);
    });

    testWidgets('should render checkbox with label', (tester) async {
      bool? value = false;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckbox(
                value: value,
                onChanged: (newValue) => value = newValue,
                label: 'Test Label',
              ),
            ),
          ),
        ),
      );

      expect(find.byType(VCheckbox), findsOneWidget);
      expect(find.text('Test Label'), findsOneWidget);
      expect(find.byType(Row), findsOneWidget);
    });

    testWidgets('should handle tap and change state', (tester) async {
      bool? value = false;
      bool? changedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckbox(
                value: value,
                onChanged: (newValue) => changedValue = newValue,
                label: 'Test Checkbox',
              ),
            ),
          ),
        ),
      );

      // Tap the checkbox
      await tester.tap(find.byType(VCheckbox));
      await tester.pump();

      expect(changedValue, isTrue);
    });

    testWidgets('should handle tap on label', (tester) async {
      bool? value = false;
      bool? changedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckbox(
                value: value,
                onChanged: (newValue) => changedValue = newValue,
                label: 'Test Label',
              ),
            ),
          ),
        ),
      );

      // Tap the label text
      await tester.tap(find.text('Test Label'));
      await tester.pump();

      expect(changedValue, isTrue);
    });

    testWidgets('should support tristate mode', (tester) async {
      bool? value = false;
      bool? changedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckbox(
                value: value,
                tristate: true,
                onChanged: (newValue) => changedValue = newValue,
              ),
            ),
          ),
        ),
      );

      // First tap: unchecked -> checked
      await tester.tap(find.byType(VCheckbox));
      await tester.pump();
      expect(changedValue, isTrue);

      // Update value and test again
      value = changedValue;
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckbox(
                value: value,
                tristate: true,
                onChanged: (newValue) => changedValue = newValue,
              ),
            ),
          ),
        ),
      );

      // Second tap: checked -> indeterminate
      await tester.tap(find.byType(VCheckbox));
      await tester.pump();
      expect(changedValue, isNull);
    });

    testWidgets('should show correct icon for checked state', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckbox(
                value: true,
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.check), findsOneWidget);
    });

    testWidgets('should show correct icon for indeterminate state', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckbox(
                value: null,
                tristate: true,
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.remove), findsOneWidget);
    });

    testWidgets('should not show icon for unchecked state', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckbox(
                value: false,
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.check), findsNothing);
      expect(find.byIcon(Icons.remove), findsNothing);
    });

    testWidgets('should be disabled when enabled is false', (tester) async {
      bool? changedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckbox(
                value: false,
                enabled: false,
                onChanged: (newValue) => changedValue = newValue,
              ),
            ),
          ),
        ),
      );

      // Try to tap the disabled checkbox
      await tester.tap(find.byType(VCheckbox));
      await tester.pump();

      expect(changedValue, isNull);
    });

    testWidgets('should have reduced opacity when disabled', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckbox(
                value: false,
                enabled: false,
                onChanged: (value) {},
                label: 'Disabled',
              ),
            ),
          ),
        ),
      );

      final opacity = tester.widget<Opacity>(find.byType(Opacity));
      expect(opacity.opacity, equals(0.6));
    });

    testWidgets('should work without onChanged callback', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckbox(
                value: true,
                onChanged: null,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(VCheckbox), findsOneWidget);
      
      // Should not crash when tapped
      await tester.tap(find.byType(VCheckbox));
      await tester.pump();
    });
  });
}
