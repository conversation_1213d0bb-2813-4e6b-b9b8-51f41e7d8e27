import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VButton Widget Tests', () {
    testWidgets('creates button with correct text', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VButton(
                onPressed: () {},
                child: const Text('Test Button'),
              ),
            ),
          ),
        ),
      );

      expect(find.text('Test Button'), findsOneWidget);
      expect(find.byType(VButton), findsOneWidget);
    });

    testWidgets('respects disabled state', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VButton(
                onPressed: () {},
                isDisabled: true,
                child: const Text('Disabled <PERSON><PERSON>'),
              ),
            ),
          ),
        ),
      );

      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(button.onPressed, isNull);
    });

    testWidgets('shows loading state correctly', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VButton(
                onPressed: () {},
                isLoading: true,
                child: const Text('Loading Button'),
              ),
            ),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading Button'), findsOneWidget);
    });

    group('Button Variants', () {
      testWidgets('renders primary variant correctly', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: VButton(
                  onPressed: () {},
                  variant: VButtonVariant.primary,
                  child: const Text('Primary'),
                ),
              ),
            ),
          ),
        );

        expect(find.text('Primary'), findsOneWidget);
      });

      testWidgets('renders secondary variant correctly', (tester) async {
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: VButton(
                  onPressed: () {},
                  variant: VButtonVariant.secondary,
                  child: const Text('Secondary'),
                ),
              ),
            ),
          ),
        );

        expect(find.text('Secondary'), findsOneWidget);
      });
    });
  });
}
