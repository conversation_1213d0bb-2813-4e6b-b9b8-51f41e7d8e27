import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VRadioGroup Tests', () {
    final testOptions = [
      VRadioOption(value: 'option1', label: 'Option 1'),
      VRadioOption(value: 'option2', label: 'Option 2'),
      VRadioOption(value: 'option3', label: 'Option 3'),
    ];

    testWidgets('should render radio group with options', (tester) async {
      String? selectedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadioGroup<String>(
                options: testOptions,
                value: selectedValue,
                onChanged: (value) => selectedValue = value,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(VRadioGroup<String>), findsOneWidget);
      expect(find.byType(VRadio<String>), findsNWidgets(3));
      expect(find.text('Option 1'), findsOneWidget);
      expect(find.text('Option 2'), findsOneWidget);
      expect(find.text('Option 3'), findsOneWidget);
    });

    testWidgets('should render with group label', (tester) async {
      String? selectedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadioGroup<String>(
                options: testOptions,
                value: selectedValue,
                onChanged: (value) => selectedValue = value,
                label: 'Test Group',
              ),
            ),
          ),
        ),
      );

      expect(find.text('Test Group'), findsOneWidget);
    });

    testWidgets('should handle option selection', (tester) async {
      String? selectedValue;
      String? changedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadioGroup<String>(
                options: testOptions,
                value: selectedValue,
                onChanged: (value) => changedValue = value,
              ),
            ),
          ),
        ),
      );

      // Tap the first option
      await tester.tap(find.text('Option 1'));
      await tester.pump();

      expect(changedValue, equals('option1'));
    });

    testWidgets('should show selected option correctly', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadioGroup<String>(
                options: testOptions,
                value: 'option2', // Second option selected
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      // All radio buttons should be present
      expect(find.byType(VRadio<String>), findsNWidgets(3));
      
      // The selected radio should have the correct group value
      final radioWidgets = tester.widgetList<VRadio<String>>(find.byType(VRadio<String>));
      expect(radioWidgets.every((radio) => radio.groupValue == 'option2'), isTrue);
    });

    testWidgets('should render horizontally when direction is horizontal', (tester) async {
      String? selectedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadioGroup<String>(
                options: testOptions,
                value: selectedValue,
                onChanged: (value) {},
                direction: VRadioGroupDirection.horizontal,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(Wrap), findsOneWidget);
    });

    testWidgets('should render vertically when direction is vertical', (tester) async {
      String? selectedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadioGroup<String>(
                options: testOptions,
                value: selectedValue,
                onChanged: (value) {},
                direction: VRadioGroupDirection.vertical,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(Column), findsOneWidget);
    });

    testWidgets('should respect disabled state', (tester) async {
      String? selectedValue;
      String? changedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadioGroup<String>(
                options: testOptions,
                value: selectedValue,
                onChanged: (value) => changedValue = value,
                enabled: false,
              ),
            ),
          ),
        ),
      );

      // Try to tap an option
      await tester.tap(find.text('Option 1'));
      await tester.pump();

      expect(changedValue, isNull);
    });

    testWidgets('should respect individual option disabled state', (tester) async {
      final optionsWithDisabled = [
        VRadioOption(value: 'option1', label: 'Option 1'),
        VRadioOption(value: 'option2', label: 'Option 2', enabled: false),
        VRadioOption(value: 'option3', label: 'Option 3'),
      ];
      
      String? selectedValue;
      String? changedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadioGroup<String>(
                options: optionsWithDisabled,
                value: selectedValue,
                onChanged: (value) => changedValue = value,
              ),
            ),
          ),
        ),
      );

      // Try to tap the disabled option
      await tester.tap(find.text('Option 2'));
      await tester.pump();

      expect(changedValue, isNull);

      // Tap an enabled option
      await tester.tap(find.text('Option 1'));
      await tester.pump();

      expect(changedValue, equals('option1'));
    });

    testWidgets('should work with different value types', (tester) async {
      final intOptions = [
        VRadioOption(value: 1, label: 'One'),
        VRadioOption(value: 2, label: 'Two'),
        VRadioOption(value: 3, label: 'Three'),
      ];
      
      int? selectedValue;
      int? changedValue;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadioGroup<int>(
                options: intOptions,
                value: selectedValue,
                onChanged: (value) => changedValue = value,
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Two'));
      await tester.pump();

      expect(changedValue, equals(2));
    });

    testWidgets('should handle null initial value', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadioGroup<String>(
                options: testOptions,
                value: null,
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      expect(find.byType(VRadioGroup<String>), findsOneWidget);
      expect(find.byType(VRadio<String>), findsNWidgets(3));
      
      // All radios should have null group value
      final radioWidgets = tester.widgetList<VRadio<String>>(find.byType(VRadio<String>));
      expect(radioWidgets.every((radio) => radio.groupValue == null), isTrue);
    });

    testWidgets('should maintain selection state correctly', (tester) async {
      String? selectedValue = 'option1';
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadioGroup<String>(
                options: testOptions,
                value: selectedValue,
                onChanged: (value) => selectedValue = value,
              ),
            ),
          ),
        ),
      );

      // Verify first option is selected
      final radioWidgets = tester.widgetList<VRadio<String>>(find.byType(VRadio<String>));
      final firstRadio = radioWidgets.first;
      expect(firstRadio.isSelected, isTrue);
      
      // Other radios should not be selected
      final otherRadios = radioWidgets.skip(1);
      expect(otherRadios.every((radio) => !radio.isSelected), isTrue);
    });

    testWidgets('should handle empty options list', (tester) async {
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VRadioGroup<String>(
                options: [],
                value: null,
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      expect(find.byType(VRadioGroup<String>), findsOneWidget);
      expect(find.byType(VRadio<String>), findsNothing);
    });
  });
}
