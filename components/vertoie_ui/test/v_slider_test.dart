import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VSlider Tests', () {
    group('Single Slider Tests', () {
      testWidgets('should render basic single slider', (tester) async {
        double sliderValue = 0.5;
        
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: VSlider(
                  type: VSliderType.single,
                  value: sliderValue,
                  onChanged: (value) => sliderValue = value,
                ),
              ),
            ),
          ),
        );

        expect(find.byType(VSlider), findsOneWidget);
        expect(find.byType(Slider), findsOneWidget);
        expect(find.byType(RangeSlider), findsNothing);
      });

      testWidgets('should handle single slider value changes', (tester) async {
        double sliderValue = 0.5;
        
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: StatefulBuilder(
                  builder: (context, setState) {
                    return VSlider(
                      type: VSliderType.single,
                      value: sliderValue,
                      onChanged: (value) {
                        setState(() {
                          sliderValue = value;
                        });
                      },
                    );
                  },
                ),
              ),
            ),
          ),
        );

        // Find the slider and verify it exists
        final slider = find.byType(Slider);
        expect(slider, findsOneWidget);

        // Verify initial value
        expect(sliderValue, equals(0.5));
      });

      testWidgets('should render single slider with label', (tester) async {
        double sliderValue = 0.5;
        
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: VSlider(
                  type: VSliderType.single,
                  value: sliderValue,
                  onChanged: (value) => sliderValue = value,
                  label: 'Volume',
                ),
              ),
            ),
          ),
        );

        expect(find.text('Volume'), findsOneWidget);
        expect(find.byType(Column), findsOneWidget);
      });

      testWidgets('should handle disabled single slider', (tester) async {
        double sliderValue = 0.5;
        
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: VSlider(
                  type: VSliderType.single,
                  value: sliderValue,
                  onChanged: (value) => sliderValue = value,
                  enabled: false,
                ),
              ),
            ),
          ),
        );

        expect(find.byType(Opacity), findsWidgets);
        
        // Drag should not change value when disabled
        final slider = find.byType(Slider);
        await tester.drag(slider, const Offset(100, 0));
        await tester.pump();
        
        expect(sliderValue, equals(0.5));
      });

      testWidgets('should handle stepped single slider', (tester) async {
        double sliderValue = 2.0;
        
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: VSlider(
                  type: VSliderType.single,
                  value: sliderValue,
                  onChanged: (value) => sliderValue = value,
                  min: 0.0,
                  max: 10.0,
                  divisions: 10,
                ),
              ),
            ),
          ),
        );

        final sliderWidget = tester.widget<Slider>(find.byType(Slider));
        expect(sliderWidget.divisions, equals(10));
        expect(sliderWidget.min, equals(0.0));
        expect(sliderWidget.max, equals(10.0));
      });
    });

    group('Range Slider Tests', () {
      testWidgets('should render basic range slider', (tester) async {
        RangeValues rangeValues = const RangeValues(0.2, 0.8);
        
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: VSlider(
                  type: VSliderType.range,
                  rangeValues: rangeValues,
                  onRangeChanged: (values) => rangeValues = values,
                ),
              ),
            ),
          ),
        );

        expect(find.byType(VSlider), findsOneWidget);
        expect(find.byType(RangeSlider), findsOneWidget);
        expect(find.byType(Slider), findsNothing);
      });

      testWidgets('should handle range slider value changes', (tester) async {
        RangeValues rangeValues = const RangeValues(0.2, 0.8);
        
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: StatefulBuilder(
                  builder: (context, setState) {
                    return VSlider(
                      type: VSliderType.range,
                      rangeValues: rangeValues,
                      onRangeChanged: (values) {
                        setState(() {
                          rangeValues = values;
                        });
                      },
                    );
                  },
                ),
              ),
            ),
          ),
        );

        // Find the range slider and verify it exists
        final rangeSlider = find.byType(RangeSlider);
        expect(rangeSlider, findsOneWidget);

        // Verify initial values
        expect(rangeValues, equals(const RangeValues(0.2, 0.8)));
      });

      testWidgets('should render range slider with label', (tester) async {
        RangeValues rangeValues = const RangeValues(0.2, 0.8);
        
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: VSlider(
                  type: VSliderType.range,
                  rangeValues: rangeValues,
                  onRangeChanged: (values) => rangeValues = values,
                  label: 'Price Range',
                ),
              ),
            ),
          ),
        );

        expect(find.text('Price Range'), findsOneWidget);
        expect(find.byType(Column), findsOneWidget);
      });

      testWidgets('should handle stepped range slider', (tester) async {
        RangeValues rangeValues = const RangeValues(2.0, 8.0);
        
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: VSlider(
                  type: VSliderType.range,
                  rangeValues: rangeValues,
                  onRangeChanged: (values) => rangeValues = values,
                  min: 0.0,
                  max: 10.0,
                  divisions: 10,
                ),
              ),
            ),
          ),
        );

        final rangeSliderWidget = tester.widget<RangeSlider>(find.byType(RangeSlider));
        expect(rangeSliderWidget.divisions, equals(10));
        expect(rangeSliderWidget.min, equals(0.0));
        expect(rangeSliderWidget.max, equals(10.0));
      });
    });

    group('Theme Integration Tests', () {
      testWidgets('should respect theme colors', (tester) async {
        double sliderValue = 0.5;
        
        await tester.pumpWidget(
          VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: MaterialApp(
              home: Scaffold(
                body: VSlider(
                  type: VSliderType.single,
                  value: sliderValue,
                  onChanged: (value) => sliderValue = value,
                ),
              ),
            ),
          ),
        );

        final sliderTheme = tester.widget<SliderTheme>(find.byType(SliderTheme));
        final theme = VThemeData.defaultTheme();
        
        expect(sliderTheme.data.activeTrackColor, theme.colors.primary);
        expect(sliderTheme.data.thumbColor, theme.colors.primary);
      });
    });

    group('Assertion Tests', () {
      testWidgets('should assert single slider requirements', (tester) async {
        expect(
          () => VSlider(
            type: VSliderType.single,
            // Missing value
            onChanged: (value) {},
          ),
          throwsAssertionError,
        );
      });

      testWidgets('should assert range slider requirements', (tester) async {
        expect(
          () => VSlider(
            type: VSliderType.range,
            // Missing rangeValues
            value: 0.5,
            onChanged: (value) {},
          ),
          throwsAssertionError,
        );
      });
    });
  });
}
