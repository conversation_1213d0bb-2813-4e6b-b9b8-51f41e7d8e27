import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

/// Helper class for golden tests in Vertoie UI components
class VGoldenTestHelpers {
  /// Standard device configurations for golden testing
  static const List<Device> testDevices = [
    Device.phone,
    Device.iphone11,
    Device.tabletPortrait,
    Device.tabletLandscape,
  ];

  /// Creates a themed test widget wrapper for golden tests
  static Widget createThemedTestWidget({
    required Widget child,
    VThemeData? theme,
    ThemeMode themeMode = ThemeMode.light,
  }) {
    return VThemeProvider(
      theme: theme ?? VThemeData.defaultTheme(),
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        themeMode: themeMode,
        theme: ThemeData.light(),
        darkTheme: ThemeData.dark(),
        home: Scaffold(
          body: Center(child: child),
        ),
      ),
    );
  }

  /// Runs golden tests for a component across multiple themes
  static Future<void> testComponentAcrossThemes({
    required WidgetTester tester,
    required Widget Function(VThemeData theme) builder,
    required String componentName,
    List<Device>? devices,
  }) async {
    final testDevices = devices ?? VGoldenTestHelpers.testDevices;

    // Test with default theme
    await tester.pumpWidgetBuilder(
      createThemedTestWidget(
        child: builder(VThemeData.defaultTheme()),
      ),
    );
    await screenMatchesGolden(tester, '${componentName}_default_theme');

    // Test with compact theme
    await tester.pumpWidgetBuilder(
      createThemedTestWidget(
        child: builder(VThemeData.defaultTheme().copyWith(density: VDensity.compact)),
      ),
    );
    await screenMatchesGolden(tester, '${componentName}_compact_theme');

    // Test with dark theme
    await tester.pumpWidgetBuilder(
      createThemedTestWidget(
        child: builder(VThemeData.dark()),
      ),
    );
    await screenMatchesGolden(tester, '${componentName}_dark_theme');
  }

  /// Tests component states (normal, hover, pressed, disabled, etc.)
  static Future<void> testComponentStates({
    required WidgetTester tester,
    required Widget Function() normalBuilder,
    Widget Function()? hoverBuilder,
    Widget Function()? pressedBuilder,
    Widget Function()? disabledBuilder,
    Widget Function()? loadingBuilder,
    required String componentName,
    VThemeData? theme,
  }) async {
    final themeData = theme ?? VThemeData.defaultTheme();

    // Test normal state
    await tester.pumpWidgetBuilder(
      createThemedTestWidget(
        child: normalBuilder(),
        theme: themeData,
      ),
    );
    await tester.pump();
    await screenMatchesGolden(tester, '${componentName}_normal');

    // Test disabled state if provided
    if (disabledBuilder != null) {
      await tester.pumpWidgetBuilder(
        createThemedTestWidget(
          child: disabledBuilder(),
          theme: themeData,
        ),
      );
      await tester.pump();
      await screenMatchesGolden(tester, '${componentName}_disabled');
    }

    // Test loading state if provided
    if (loadingBuilder != null) {
      await tester.pumpWidgetBuilder(
        createThemedTestWidget(
          child: loadingBuilder(),
          theme: themeData,
        ),
      );
      // For loading states with animations, pump a few frames then capture
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 100));
      await tester.pump(const Duration(milliseconds: 100));

      // Use expectLater with matchesGoldenFile to avoid pumpAndSettle timeout
      await expectLater(
        find.byType(Scaffold),
        matchesGoldenFile('${componentName}_loading.png'),
      );
    }
  }

  /// Creates a showcase widget for multiple component variants
  static Widget createComponentShowcase({
    required List<Widget> variants,
    required String title,
    int columns = 2,
  }) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: columns,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: 3,
              children: variants,
            ),
          ],
        ),
      ),
    );
  }

  /// Tests color variants for components
  static Future<void> testColorVariants({
    required WidgetTester tester,
    required Widget Function(Color color) builder,
    required String componentName,
    VThemeData? theme,
  }) async {
    final themeData = theme ?? VThemeData.defaultTheme();
    final colors = [
      themeData.colors.primary,
      themeData.colors.secondary,
      themeData.colors.surface,
      themeData.colors.error,
    ];

    for (int i = 0; i < colors.length; i++) {
      final colorName = [
        'primary',
        'secondary',
        'surface',
        'error',
      ][i];

      await tester.pumpWidgetBuilder(
        createThemedTestWidget(
          child: builder(colors[i]),
          theme: themeData,
        ),
      );
      await screenMatchesGolden(tester, '${componentName}_${colorName}_color');
    }
  }

  /// Tests component sizing variants
  static Future<void> testSizeVariants({
    required WidgetTester tester,
    required Widget Function(double size) builder,
    required String componentName,
    VThemeData? theme,
  }) async {
    final themeData = theme ?? VThemeData.defaultTheme();
    final sizes = [16.0, 24.0, 32.0, 48.0];
    final sizeNames = ['small', 'medium', 'large', 'extra_large'];

    for (int i = 0; i < sizes.length; i++) {
      await tester.pumpWidgetBuilder(
        createThemedTestWidget(
          child: builder(sizes[i]),
          theme: themeData,
        ),
      );
      await screenMatchesGolden(tester, '${componentName}_${sizeNames[i]}_size');
    }
  }
}
