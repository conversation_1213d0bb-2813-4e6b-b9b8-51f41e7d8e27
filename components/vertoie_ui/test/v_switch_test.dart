import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VSwitch Tests', () {
    testWidgets('should render basic switch', (tester) async {
      bool switchValue = false;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VSwitch(
                value: switchValue,
                onChanged: (value) => switchValue = value,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(VSwitch), findsOneWidget);
      expect(find.byType(Switch), findsOneWidget);
    });

    testWidgets('should handle value changes', (tester) async {
      bool switchValue = false;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: StatefulBuilder(
                builder: (context, setState) {
                  return VSwitch(
                    value: switchValue,
                    onChanged: (value) {
                      setState(() {
                        switchValue = value;
                      });
                    },
                  );
                },
              ),
            ),
          ),
        ),
      );

      // Initially false
      expect(switchValue, false);

      // Tap to toggle
      await tester.tap(find.byType(Switch));
      await tester.pump();

      expect(switchValue, true);
    });

    testWidgets('should render with label', (tester) async {
      bool switchValue = false;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VSwitch(
                value: switchValue,
                onChanged: (value) => switchValue = value,
                label: 'Enable notifications',
              ),
            ),
          ),
        ),
      );

      expect(find.text('Enable notifications'), findsOneWidget);
      expect(find.byType(Row), findsOneWidget);
    });

    testWidgets('should handle disabled state', (tester) async {
      bool switchValue = false;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VSwitch(
                value: switchValue,
                onChanged: (value) => switchValue = value,
                enabled: false,
              ),
            ),
          ),
        ),
      );

      // Should be wrapped in Opacity (there might be multiple Opacity widgets)
      expect(find.byType(Opacity), findsWidgets);
      
      // Tap should not change value
      await tester.tap(find.byType(Switch));
      await tester.pump();
      
      expect(switchValue, false);
    });

    testWidgets('should use Cupertino style on iOS when adaptive', (tester) async {
      bool switchValue = false;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            theme: ThemeData(platform: TargetPlatform.iOS),
            home: Scaffold(
              body: VSwitch(
                value: switchValue,
                onChanged: (value) => switchValue = value,
                adaptive: true,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(CupertinoSwitch), findsOneWidget);
      expect(find.byType(Switch), findsNothing);
    });

    testWidgets('should use Material style when not adaptive', (tester) async {
      bool switchValue = false;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            theme: ThemeData(platform: TargetPlatform.iOS),
            home: Scaffold(
              body: VSwitch(
                value: switchValue,
                onChanged: (value) => switchValue = value,
                adaptive: false,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(Switch), findsOneWidget);
      expect(find.byType(CupertinoSwitch), findsNothing);
    });

    testWidgets('should handle label tap when enabled', (tester) async {
      bool switchValue = false;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: StatefulBuilder(
                builder: (context, setState) {
                  return VSwitch(
                    value: switchValue,
                    onChanged: (value) {
                      setState(() {
                        switchValue = value;
                      });
                    },
                    label: 'Toggle me',
                  );
                },
              ),
            ),
          ),
        ),
      );

      // Tap on the label text
      await tester.tap(find.text('Toggle me'));
      await tester.pump();

      expect(switchValue, true);
    });

    testWidgets('should not handle label tap when disabled', (tester) async {
      bool switchValue = false;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VSwitch(
                value: switchValue,
                onChanged: (value) => switchValue = value,
                label: 'Disabled toggle',
                enabled: false,
              ),
            ),
          ),
        ),
      );

      // Tap on the label text
      await tester.tap(find.text('Disabled toggle'));
      await tester.pump();

      expect(switchValue, false);
    });

    testWidgets('should respect theme colors', (tester) async {
      bool switchValue = true;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VSwitch(
                value: switchValue,
                onChanged: (value) => switchValue = value,
              ),
            ),
          ),
        ),
      );

      final switchWidget = tester.widget<Switch>(find.byType(Switch));
      final theme = VThemeData.defaultTheme();
      
      expect(switchWidget.activeColor, theme.colors.primary);
    });
  });
}
