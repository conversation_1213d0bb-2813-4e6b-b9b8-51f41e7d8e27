import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VCheckboxGroup Tests', () {
    final testOptions = [
      VCheckboxOption(value: 'option1', label: 'Option 1'),
      VCheckboxOption(value: 'option2', label: 'Option 2'),
      VCheckboxOption(value: 'option3', label: 'Option 3'),
    ];

    testWidgets('should render checkbox group with options', (tester) async {
      List<String> selectedValues = [];
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckboxGroup<String>(
                options: testOptions,
                selectedValues: selectedValues,
                onChanged: (values) => selectedValues = values,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(VCheckboxGroup<String>), findsOneWidget);
      expect(find.byType(VCheckbox), findsNWidgets(3));
      expect(find.text('Option 1'), findsOneWidget);
      expect(find.text('Option 2'), findsOneWidget);
      expect(find.text('Option 3'), findsOneWidget);
    });

    testWidgets('should render with group label', (tester) async {
      List<String> selectedValues = [];
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckboxGroup<String>(
                options: testOptions,
                selectedValues: selectedValues,
                onChanged: (values) => selectedValues = values,
                label: 'Test Group',
              ),
            ),
          ),
        ),
      );

      expect(find.text('Test Group'), findsOneWidget);
    });

    testWidgets('should handle option selection', (tester) async {
      List<String> selectedValues = [];
      List<String>? changedValues;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckboxGroup<String>(
                options: testOptions,
                selectedValues: selectedValues,
                onChanged: (values) => changedValues = values,
              ),
            ),
          ),
        ),
      );

      // Tap the first option
      await tester.tap(find.text('Option 1'));
      await tester.pump();

      expect(changedValues, contains('option1'));
      expect(changedValues?.length, equals(1));
    });

    testWidgets('should handle option deselection', (tester) async {
      List<String> selectedValues = ['option1'];
      List<String>? changedValues;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckboxGroup<String>(
                options: testOptions,
                selectedValues: selectedValues,
                onChanged: (values) => changedValues = values,
              ),
            ),
          ),
        ),
      );

      // Tap the first option to deselect it
      await tester.tap(find.text('Option 1'));
      await tester.pump();

      expect(changedValues, isNotNull);
      expect(changedValues, isNot(contains('option1')));
      expect(changedValues?.length, equals(0));
    });

    testWidgets('should render horizontally when direction is horizontal', (tester) async {
      List<String> selectedValues = [];
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckboxGroup<String>(
                options: testOptions,
                selectedValues: selectedValues,
                onChanged: (values) {},
                direction: VCheckboxGroupDirection.horizontal,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(Wrap), findsOneWidget);
    });

    testWidgets('should render vertically when direction is vertical', (tester) async {
      List<String> selectedValues = [];
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckboxGroup<String>(
                options: testOptions,
                selectedValues: selectedValues,
                onChanged: (values) {},
                direction: VCheckboxGroupDirection.vertical,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(Column), findsOneWidget);
    });

    testWidgets('should show select all checkbox when enabled', (tester) async {
      List<String> selectedValues = [];
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckboxGroup<String>(
                options: testOptions,
                selectedValues: selectedValues,
                onChanged: (values) {},
                showSelectAll: true,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(VCheckbox), findsNWidgets(4)); // 3 options + 1 select all
      expect(find.text('Select All'), findsOneWidget);
    });

    testWidgets('should handle select all functionality', (tester) async {
      List<String> selectedValues = [];
      List<String>? changedValues;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckboxGroup<String>(
                options: testOptions,
                selectedValues: selectedValues,
                onChanged: (values) => changedValues = values,
                showSelectAll: true,
              ),
            ),
          ),
        ),
      );

      // Tap select all
      await tester.tap(find.text('Select All'));
      await tester.pump();

      expect(changedValues, isNotNull);
      expect(changedValues?.length, equals(3));
      expect(changedValues, containsAll(['option1', 'option2', 'option3']));
    });

    testWidgets('should handle deselect all functionality', (tester) async {
      List<String> selectedValues = ['option1', 'option2', 'option3'];
      List<String>? changedValues;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckboxGroup<String>(
                options: testOptions,
                selectedValues: selectedValues,
                onChanged: (values) => changedValues = values,
                showSelectAll: true,
              ),
            ),
          ),
        ),
      );

      // Tap select all to deselect all
      await tester.tap(find.text('Select All'));
      await tester.pump();

      expect(changedValues, isNotNull);
      expect(changedValues?.length, equals(0));
    });

    testWidgets('should respect disabled state', (tester) async {
      List<String> selectedValues = [];
      List<String>? changedValues;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckboxGroup<String>(
                options: testOptions,
                selectedValues: selectedValues,
                onChanged: (values) => changedValues = values,
                enabled: false,
              ),
            ),
          ),
        ),
      );

      // Try to tap an option
      await tester.tap(find.text('Option 1'));
      await tester.pump();

      expect(changedValues, isNull);
    });

    testWidgets('should respect individual option disabled state', (tester) async {
      final optionsWithDisabled = [
        VCheckboxOption(value: 'option1', label: 'Option 1'),
        VCheckboxOption(value: 'option2', label: 'Option 2', enabled: false),
        VCheckboxOption(value: 'option3', label: 'Option 3'),
      ];
      
      List<String> selectedValues = [];
      List<String>? changedValues;
      
      await tester.pumpWidget(
        VThemeProvider(
          theme: VThemeData.defaultTheme(),
          child: MaterialApp(
            home: Scaffold(
              body: VCheckboxGroup<String>(
                options: optionsWithDisabled,
                selectedValues: selectedValues,
                onChanged: (values) => changedValues = values,
              ),
            ),
          ),
        ),
      );

      // Try to tap the disabled option
      await tester.tap(find.text('Option 2'));
      await tester.pump();

      expect(changedValues, isNull);

      // Tap an enabled option
      await tester.tap(find.text('Option 1'));
      await tester.pump();

      expect(changedValues, isNotNull);
      expect(changedValues, contains('option1'));
    });
  });
}
