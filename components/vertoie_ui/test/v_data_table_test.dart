import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

// Sample data class for testing
class Person {
  const Person({
    required this.id,
    required this.name,
    required this.email,
    required this.age,
    required this.department,
  });

  final int id;
  final String name;
  final String email;
  final int age;
  final String department;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Person && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

void main() {
  group('VDataTable Tests', () {
    late List<Person> sampleData;
    late List<VDataColumn<Person>> columns;

    setUp(() {
      sampleData = [
        const Person(id: 1, name: '<PERSON>', email: '<EMAIL>', age: 30, department: 'Engineering'),
        const Person(id: 2, name: '<PERSON>', email: '<EMAIL>', age: 28, department: 'Design'),
        const Person(id: 3, name: '<PERSON>', email: '<EMAIL>', age: 35, department: 'Marketing'),
      ];

      columns = [
        VDataColumn<Person>(
          key: 'name',
          label: 'Name',
          getValue: (person) => person.name,
        ),
        VDataColumn<Person>(
          key: 'email',
          label: 'Email',
          getValue: (person) => person.email,
        ),
        VDataColumn<Person>(
          key: 'age',
          label: 'Age',
          getValue: (person) => person.age,
          alignment: Alignment.centerRight,
        ),
        VDataColumn<Person>(
          key: 'department',
          label: 'Department',
          getValue: (person) => person.department,
        ),
      ];
    });

    testWidgets('renders basic data table', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDataTable<Person>(
                columns: columns,
                data: sampleData,
              ),
            ),
          ),
        ),
      );

      // Verify table headers are displayed
      expect(find.text('Name'), findsOneWidget);
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Age'), findsOneWidget);
      expect(find.text('Department'), findsOneWidget);

      // Verify data is displayed
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('35'), findsOneWidget);
      expect(find.text('Marketing'), findsOneWidget);
    });

    testWidgets('shows loading state', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDataTable<Person>(
                columns: columns,
                data: sampleData,
                loading: true,
              ),
            ),
          ),
        ),
      );

      // Verify loading indicator is shown
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading...'), findsOneWidget);

      // Verify data is not shown when loading
      expect(find.text('John Doe'), findsNothing);
    });

    testWidgets('shows empty state', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDataTable<Person>(
                columns: columns,
                data: [],
              ),
            ),
          ),
        ),
      );

      // Verify empty state is shown
      expect(find.text('No data available'), findsOneWidget);
      expect(find.text('Add some data to see it displayed here'), findsOneWidget);
      expect(find.byIcon(Icons.table_view), findsOneWidget);
    });

    testWidgets('handles selection mode', (WidgetTester tester) async {
      VRowSelection<Person>? lastSelection;
      
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDataTable<Person>(
                columns: columns,
                data: sampleData,
                selectable: true,
                selection: const VRowSelection<Person>(),
                onSelectionChanged: (selection) {
                  lastSelection = selection;
                },
              ),
            ),
          ),
        ),
      );

      // Verify checkboxes are present
      expect(find.byType(Checkbox), findsNWidgets(4)); // 3 data rows + 1 header

      // Tap the first data row checkbox
      await tester.tap(find.byType(Checkbox).at(1));
      await tester.pump();

      // Verify selection callback was called
      expect(lastSelection, isNotNull);
      expect(lastSelection!.selectedItems.length, equals(1));
      expect(lastSelection!.selectedItems.first, equals(sampleData[0]));
    });

    testWidgets('handles sorting', (WidgetTester tester) async {
      VColumnSort? lastSort;
      
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDataTable<Person>(
                columns: columns,
                data: sampleData,
                sortable: true,
                onSort: (sort) {
                  lastSort = sort;
                },
              ),
            ),
          ),
        ),
      );

      // Find and tap the name column header
      await tester.tap(find.text('Name'));
      await tester.pump();

      // Verify sort callback was called
      expect(lastSort, isNotNull);
      expect(lastSort!.columnKey, equals('name'));
      expect(lastSort!.direction, equals(VSortDirection.ascending));
    });

    testWidgets('handles pagination', (WidgetTester tester) async {
      int? lastPage;
      
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDataTable<Person>(
                columns: columns,
                data: sampleData,
                paginated: true,
                pageSize: 2,
                currentPage: 0,
                onPageChanged: (page) {
                  lastPage = page;
                },
              ),
            ),
          ),
        ),
      );

      // Verify pagination controls are present
      expect(find.byIcon(Icons.chevron_left), findsOneWidget);
      expect(find.byIcon(Icons.chevron_right), findsOneWidget);
      expect(find.text('Showing 1-2 of 3'), findsOneWidget);
      expect(find.text('1 of 2'), findsOneWidget);

      // Tap next page button
      await tester.tap(find.byIcon(Icons.chevron_right));
      await tester.pump();

      // Verify page change callback was called
      expect(lastPage, equals(1));
    });

    testWidgets('handles row tap', (WidgetTester tester) async {
      Person? tappedPerson;
      int? tappedIndex;
      
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDataTable<Person>(
                columns: columns,
                data: sampleData,
                onRowTap: (person, index) {
                  tappedPerson = person;
                  tappedIndex = index;
                },
              ),
            ),
          ),
        ),
      );

      // Tap the first data row
      await tester.tap(find.text('John Doe'));
      await tester.pump();

      // Verify row tap callback was called
      expect(tappedPerson, equals(sampleData[0]));
      expect(tappedIndex, equals(0));
    });

    testWidgets('supports custom cell builders', (WidgetTester tester) async {
      final customColumns = [
        VDataColumn<Person>(
          key: 'name',
          label: 'Name',
          builder: (context, person, index) => Text(
            person.name.toUpperCase(),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        VDataColumn<Person>(
          key: 'status',
          label: 'Status',
          builder: (context, person, index) => Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              'Active',
              style: TextStyle(color: Colors.white, fontSize: 12),
            ),
          ),
        ),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VDataTable<Person>(
                columns: customColumns,
                data: sampleData,
              ),
            ),
          ),
        ),
      );

      // Verify custom builders are working
      expect(find.text('JOHN DOE'), findsOneWidget);
      expect(find.text('Active'), findsNWidgets(3)); // One for each person
      expect(find.byType(Container), findsAtLeastNWidgets(3));
    });
  });
}
