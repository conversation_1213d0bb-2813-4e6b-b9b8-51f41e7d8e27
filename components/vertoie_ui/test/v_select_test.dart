import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

void main() {
  group('VSelect Tests', () {
    testWidgets('should render single select with items', (WidgetTester tester) async {
      String? selectedValue;
      
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VSelect<String>(
                label: 'Test Select',
                items: ['Item 1', 'Item 2', 'Item 3']
                    .map((item) => VSelectItem(
                          value: item,
                          label: item,
                        ))
                    .toList(),
                onChanged: (value) {
                  selectedValue = value;
                },
              ),
            ),
          ),
        ),
      );

      expect(find.text('Test Select'), findsOneWidget);
      expect(find.text('Select an option'), findsOneWidget);
    });

    testWidgets('should render multi-select with items', (WidgetTester tester) async {
      List<String> selectedValues = [];
      
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VSelect<String>(
                label: 'Test Multi Select',
                mode: VSelectMode.multiple,
                items: ['Item 1', 'Item 2', 'Item 3']
                    .map((item) => VSelectItem(
                          value: item,
                          label: item,
                        ))
                    .toList(),
                onMultiChanged: (values) {
                  selectedValues = values;
                },
              ),
            ),
          ),
        ),
      );

      expect(find.text('Test Multi Select'), findsOneWidget);
      expect(find.text('Select options'), findsOneWidget);
    });

    testWidgets('should open dropdown when tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VSelect<String>(
                items: ['Item 1', 'Item 2', 'Item 3']
                    .map((item) => VSelectItem(
                          value: item,
                          label: item,
                        ))
                    .toList(),
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      // Tap to open dropdown
      await tester.tap(find.byType(GestureDetector).first);
      await tester.pumpAndSettle();

      // Should show dropdown items
      expect(find.text('Item 1'), findsOneWidget);
      expect(find.text('Item 2'), findsOneWidget);
      expect(find.text('Item 3'), findsOneWidget);
    });

    testWidgets('should select item in single select mode', (WidgetTester tester) async {
      String? selectedValue;
      
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VSelect<String>(
                items: ['Item 1', 'Item 2', 'Item 3']
                    .map((item) => VSelectItem(
                          value: item,
                          label: item,
                        ))
                    .toList(),
                onChanged: (value) {
                  selectedValue = value;
                },
              ),
            ),
          ),
        ),
      );

      // Tap to open dropdown
      await tester.tap(find.byType(GestureDetector).first);
      await tester.pumpAndSettle();

      // Tap on first item
      await tester.tap(find.text('Item 1'));
      await tester.pumpAndSettle();

      expect(selectedValue, equals('Item 1'));
    });

    testWidgets('should show search field when searchable', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VSelect<String>(
                searchable: true,
                items: ['Item 1', 'Item 2', 'Item 3']
                    .map((item) => VSelectItem(
                          value: item,
                          label: item,
                        ))
                    .toList(),
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      // Tap to open dropdown
      await tester.tap(find.byType(GestureDetector).first);
      await tester.pumpAndSettle();

      // Should show search field
      expect(find.byType(TextField), findsOneWidget);
      expect(find.text('Search...'), findsOneWidget);
    });

    testWidgets('should filter items when searching', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VSelect<String>(
                searchable: true,
                items: ['Apple', 'Banana', 'Cherry']
                    .map((item) => VSelectItem(
                          value: item,
                          label: item,
                        ))
                    .toList(),
                onChanged: (value) {},
              ),
            ),
          ),
        ),
      );

      // Tap to open dropdown
      await tester.tap(find.byType(GestureDetector).first);
      await tester.pumpAndSettle();

      // Type in search field
      await tester.enterText(find.byType(TextField), 'app');
      await tester.pumpAndSettle();

      // Should only show Apple
      expect(find.text('Apple'), findsOneWidget);
      expect(find.text('Banana'), findsNothing);
      expect(find.text('Cherry'), findsNothing);
    });

    testWidgets('should handle multi-select with chips', (WidgetTester tester) async {
      List<String> selectedValues = [];
      
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: Scaffold(
              body: VSelect<String>(
                mode: VSelectMode.multiple,
                values: ['Item 1'],
                items: ['Item 1', 'Item 2', 'Item 3']
                    .map((item) => VSelectItem(
                          value: item,
                          label: item,
                        ))
                    .toList(),
                onMultiChanged: (values) {
                  selectedValues = values;
                },
              ),
            ),
          ),
        ),
      );

      // Should show chip for selected item
      expect(find.text('Item 1'), findsOneWidget);
      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('should remove chip when close icon tapped', (WidgetTester tester) async {
      List<String> selectedValues = ['Item 1'];
      
      await tester.pumpWidget(
        MaterialApp(
          home: VThemeProvider(
            theme: VThemeData.defaultTheme(),
            child: StatefulBuilder(
              builder: (context, setState) => Scaffold(
                body: VSelect<String>(
                  mode: VSelectMode.multiple,
                  values: selectedValues,
                  items: ['Item 1', 'Item 2', 'Item 3']
                      .map((item) => VSelectItem(
                            value: item,
                            label: item,
                          ))
                      .toList(),
                  onMultiChanged: (values) {
                    setState(() {
                      selectedValues = values;
                    });
                  },
                ),
              ),
            ),
          ),
        ),
      );

      // Tap close icon
      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      expect(selectedValues, isEmpty);
    });
  });
}
