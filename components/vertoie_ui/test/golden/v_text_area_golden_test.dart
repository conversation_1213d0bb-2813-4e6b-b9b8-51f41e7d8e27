import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

import '../golden_test_helpers.dart';

void main() {
  group('VTextArea Golden Tests', () {
    testGoldens('VTextArea basic rendering', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VTextArea(
            placeholder: 'Enter your message',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_text_area_basic');
    });

    testGoldens('VTextArea with content', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VTextArea(
            controller: TextEditingController(
              text: 'This is a multi-line text area\nwith some sample content\nthat spans multiple lines.',
            ),
            placeholder: 'Enter your message',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_text_area_with_content');
    });

    testGoldens('VTextArea states showcase', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: Column(
            children: [
              // Basic text area
              const VTextArea(
                placeholder: 'Basic text area',
                minLines: 3,
              ),
              const SizedBox(height: 16),
              
              // With character counter
              const VTextArea(
                placeholder: 'With character counter',
                maxLength: 100,
                showCharacterCounter: true,
                minLines: 3,
              ),
              const SizedBox(height: 16),
              
              // Disabled state
              VTextArea(
                placeholder: 'Disabled text area',
                controller: TextEditingController(text: 'Read-only content'),
                enabled: false,
                minLines: 3,
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_text_area_states');
    });

    testGoldens('VTextArea with character counter', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VTextArea(
            controller: TextEditingController(text: 'Sample text'),
            placeholder: 'Enter your message',
            maxLength: 200,
            showCharacterCounter: true,
            minLines: 4,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_text_area_with_counter');
    });

    testGoldens('VTextArea over character limit', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VTextArea(
            controller: TextEditingController(
              text: 'This text is intentionally longer than the maximum allowed length to test the over-limit styling',
            ),
            placeholder: 'Enter your message',
            maxLength: 50,
            showCharacterCounter: true,
            minLines: 3,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_text_area_over_limit');
    });

    testGoldens('VTextArea across themes', (tester) async {
      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_text_area',
        builder: (theme) => VTextArea(
          placeholder: 'Enter your message',
          controller: TextEditingController(text: 'Sample content\nMultiple lines'),
          minLines: 3,
        ),
        devices: [Device.phone],
      );
    });

    testGoldens('VTextArea different sizes', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: Column(
            children: [
              // Small (2 lines)
              const VTextArea(
                placeholder: 'Small (2 lines)',
                minLines: 2,
              ),
              const SizedBox(height: 16),
              
              // Medium (4 lines)
              const VTextArea(
                placeholder: 'Medium (4 lines)',
                minLines: 4,
              ),
              const SizedBox(height: 16),
              
              // Large (6 lines)
              const VTextArea(
                placeholder: 'Large (6 lines)',
                minLines: 6,
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_text_area_sizes');
    });

    testGoldens('VTextArea auto-expand showcase', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: Column(
            children: [
              // Auto-expand enabled
              VTextArea(
                placeholder: 'Auto-expand enabled (2-6 lines)',
                controller: TextEditingController(
                  text: 'Line 1\nLine 2\nLine 3\nLine 4\nLine 5',
                ),
                minLines: 2,
                maxLines: 6,
                autoExpand: true,
              ),
              const SizedBox(height: 16),
              
              // Auto-expand disabled
              VTextArea(
                placeholder: 'Auto-expand disabled (fixed 3 lines)',
                controller: TextEditingController(
                  text: 'Line 1\nLine 2\nLine 3\nLine 4\nLine 5',
                ),
                minLines: 3,
                maxLines: 6,
                autoExpand: false,
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_text_area_auto_expand');
    });
  });
}
