import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

import '../golden_test_helpers.dart';

void main() {
  group('VContainer Golden Tests', () {
    setUpAll(() async {
      await loadAppFonts();
    });

    testGoldens('VContainer basic rendering', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VContainer(
            child: Text('Container Content'),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_container_basic');
    });

    testGoldens('VContainer with custom padding', (tester) async {
      final paddings = [
        const EdgeInsets.all(8.0),
        const EdgeInsets.all(16.0),
        const EdgeInsets.all(24.0),
        const EdgeInsets.symmetric(horizontal: 32.0, vertical: 8.0),
        const EdgeInsets.only(left: 16.0, top: 8.0, right: 24.0, bottom: 12.0),
      ];

      for (int i = 0; i < paddings.length; i++) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            child: VContainer(
              padding: paddings[i],
              child: const Text('Padded Content'),
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_container_padding_$i');
      }
    });

    testGoldens('VContainer with custom margin', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: Container(
            color: Colors.grey[200],
            padding: const EdgeInsets.all(20),
            child: const VContainer(
              margin: EdgeInsets.all(16.0),
              child: Text('Container with Margin'),
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_container_margin');
    });

    testGoldens('VContainer with fixed dimensions', (tester) async {
      final dimensions = [
        {'width': 100.0, 'height': 50.0},
        {'width': 150.0, 'height': 100.0},
        {'width': 200.0, 'height': 80.0},
      ];

      for (int i = 0; i < dimensions.length; i++) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            child: VContainer(
              width: dimensions[i]['width'],
              height: dimensions[i]['height'],
              child: const Center(child: Text('Fixed Size')),
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_container_dimensions_$i');
      }
    });

    testGoldens('VContainer with custom colors', (tester) async {
      final colors = [
        Colors.red[100],
        Colors.blue[100],
        Colors.green[100],
        Colors.orange[100],
        Colors.purple[100],
      ];

      for (int i = 0; i < colors.length; i++) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            child: VContainer(
              color: colors[i],
              child: const Text('Colored Container'),
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_container_color_$i');
      }
    });

    testGoldens('VContainer across themes', (tester) async {
      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_container',
        builder: (theme) => const VContainer(
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Text('Themed Container Content'),
          ),
        ),
        devices: [Device.phone],
      );
    });

    testGoldens('VContainer with complex content', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VContainer(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Container Title',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'This is a container with complex content including multiple text elements and spacing.',
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      color: Colors.blue,
                    ),
                    const SizedBox(width: 8),
                    const Text('Icon with text'),
                  ],
                ),
              ],
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_container_complex_content');
    });

    testGoldens('VContainer nested containers', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VContainer(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: VContainer(
              padding: const EdgeInsets.all(12),
              color: Colors.white,
              child: VContainer(
                padding: const EdgeInsets.all(8),
                color: Colors.blue[50],
                child: const Text('Nested Containers'),
              ),
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_container_nested');
    });

    testGoldens('VContainer responsive behavior', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const SizedBox(
            width: 300,
            child: VContainer(
              child: Text(
                'This container adapts to its parent width and demonstrates responsive behavior.',
              ),
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_container_responsive');
    });
  });
}
