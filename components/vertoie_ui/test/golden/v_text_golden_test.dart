import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

import '../golden_test_helpers.dart';

void main() {
  group('VText Golden Tests', () {
    setUpAll(() async {
      await loadAppFonts();
    });

    testGoldens('VText basic rendering', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VText('Hello, Vertoie UI!'),
        ),
      );

      await screenMatchesGolden(tester, 'v_text_basic');
    });

    testGoldens('VText with custom style', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VText(
            'Styled Text',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_text_custom_style');
    });

    testGoldens('VText with different alignments', (tester) async {
      final alignments = [
        TextAlign.left,
        TextAlign.center,
        TextAlign.right,
        TextAlign.justify,
      ];

      for (final alignment in alignments) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            child: SizedBox(
              width: 200,
              child: VText(
                'This is a longer text that demonstrates text alignment behavior in the VText component.',
                textAlign: alignment,
              ),
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_text_align_${alignment.name}');
      }
    });

    testGoldens('VText with overflow handling', (tester) async {
      final overflows = [
        TextOverflow.clip,
        TextOverflow.ellipsis,
        TextOverflow.fade,
      ];

      for (final overflow in overflows) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            child: SizedBox(
              width: 100,
              child: VText(
                'This is a very long text that will definitely overflow the container',
                overflow: overflow,
                maxLines: 1,
              ),
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_text_overflow_${overflow.name}');
      }
    });

    testGoldens('VText with max lines', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const SizedBox(
            width: 150,
            child: VText(
              'This is a multi-line text that should wrap to multiple lines and demonstrate the maxLines property behavior.',
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_text_max_lines');
    });

    testGoldens('VText across themes', (tester) async {
      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_text',
        builder: (theme) => const VText(
          'Themed Text Content',
          style: TextStyle(fontSize: 18),
        ),
        devices: [Device.phone],
      );
    });

    testGoldens('VText font sizes', (tester) async {
      final fontSizes = [12.0, 16.0, 20.0, 24.0, 32.0];

      for (final fontSize in fontSizes) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            child: VText(
              'Font Size $fontSize',
              style: TextStyle(fontSize: fontSize),
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_text_font_size_${fontSize.toInt()}');
      }
    });

    testGoldens('VText font weights', (tester) async {
      final fontWeights = [
        FontWeight.w300,
        FontWeight.w400,
        FontWeight.w500,
        FontWeight.w600,
        FontWeight.w700,
      ];

      for (final fontWeight in fontWeights) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            child: VText(
              'Weight ${fontWeight.value}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: fontWeight,
              ),
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_text_weight_${fontWeight.value}');
      }
    });
  });
}
