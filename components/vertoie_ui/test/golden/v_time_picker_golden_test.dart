import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

import '../golden_test_helpers.dart';

void main() {
  group('VTimePicker Golden Tests', () {
    setUpAll(() async {
      await loadAppFonts();
    });

    testGoldens('VTimePicker basic rendering', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VTimePicker(
            placeholder: 'Select time',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_time_picker_basic');
    });

    testGoldens('VTimePicker with value (12-hour)', (tester) async {
      const testTime = TimeOfDay(hour: 14, minute: 30);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VTimePicker(
            value: testTime,
            format: VTimeFormat.hour12,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_time_picker_with_value_12h');
    });

    testGoldens('VTimePicker with value (24-hour)', (tester) async {
      const testTime = TimeOfDay(hour: 14, minute: 30);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VTimePicker(
            value: testTime,
            format: VTimeFormat.hour24,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_time_picker_with_value_24h');
    });

    testGoldens('VTimePicker with label', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VTimePicker(
            label: 'Meeting Time',
            placeholder: 'Select time',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_time_picker_with_label');
    });

    testGoldens('VTimePicker with clear button', (tester) async {
      const testTime = TimeOfDay(hour: 14, minute: 30);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VTimePicker(
            value: testTime,
            showClearButton: true,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_time_picker_with_clear_button');
    });

    testGoldens('VTimePicker disabled state', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VTimePicker(
            enabled: false,
            placeholder: 'Select time',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_time_picker_disabled');
    });

    testGoldens('VTimePicker with custom format', (tester) async {
      const testTime = TimeOfDay(hour: 14, minute: 30);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VTimePicker(
            value: testTime,
            timeFormat: (time) => '${time.hour}h${time.minute}m',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_time_picker_custom_format');
    });

    testGoldens('VTimePicker midnight and noon', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              VTimePicker(
                value: TimeOfDay(hour: 0, minute: 0),
                format: VTimeFormat.hour12,
              ),
              SizedBox(height: 16),
              VTimePicker(
                value: TimeOfDay(hour: 12, minute: 0),
                format: VTimeFormat.hour12,
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_time_picker_midnight_noon');
    });

    testGoldens('VTimePicker across themes', (tester) async {
      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_time_picker',
        builder: (theme) => const VTimePicker(
          value: TimeOfDay(hour: 14, minute: 30),
          format: VTimeFormat.hour12,
        ),
        devices: [Device.phone],
      );
    });

    testGoldens('VTimePicker density variations', (tester) async {
      final densities = [
        VDensity.compact,
        VDensity.comfortable,
        VDensity.spacious,
      ];

      for (final density in densities) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            theme: VThemeData.defaultTheme().copyWith(density: density),
            child: const VTimePicker(
              value: TimeOfDay(hour: 14, minute: 30),
              format: VTimeFormat.hour12,
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_time_picker_density_${density.name}');
      }
    });

    testGoldens('VTimePicker corner variations', (tester) async {
      final cornerStyles = [
        VCornerStyle.sharp,
        VCornerStyle.subtle,
        VCornerStyle.rounded,
      ];

      for (final cornerStyle in cornerStyles) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            theme: VThemeData.defaultTheme().copyWith(cornerStyle: cornerStyle),
            child: const VTimePicker(
              value: TimeOfDay(hour: 14, minute: 30),
              format: VTimeFormat.hour12,
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_time_picker_corners_${cornerStyle.name}');
      }
    });

    testGoldens('VTimePicker states comparison', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              VTimePicker(
                placeholder: 'Empty state',
              ),
              SizedBox(height: 16),
              VTimePicker(
                value: TimeOfDay(hour: 14, minute: 30),
                format: VTimeFormat.hour12,
              ),
              SizedBox(height: 16),
              VTimePicker(
                value: TimeOfDay(hour: 14, minute: 30),
                format: VTimeFormat.hour24,
              ),
              SizedBox(height: 16),
              VTimePicker(
                value: TimeOfDay(hour: 14, minute: 30),
                showClearButton: true,
              ),
              SizedBox(height: 16),
              VTimePicker(
                enabled: false,
                placeholder: 'Disabled',
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_time_picker_states_comparison');
    });

    testGoldens('VTimePicker format comparison', (tester) async {
      const testTime = TimeOfDay(hour: 14, minute: 5);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              VTimePicker(
                value: testTime,
                format: VTimeFormat.hour12,
              ),
              SizedBox(height: 16),
              VTimePicker(
                value: testTime,
                format: VTimeFormat.hour24,
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_time_picker_format_comparison');
    });

    testGoldens('VTimePicker with label and value', (tester) async {
      const testTime = TimeOfDay(hour: 9, minute: 30);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VTimePicker(
            label: 'Appointment Time',
            value: testTime,
            showClearButton: true,
            format: VTimeFormat.hour12,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_time_picker_label_and_value');
    });

    testGoldens('VTimePicker edge times', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              VTimePicker(
                value: TimeOfDay(hour: 0, minute: 0),
                format: VTimeFormat.hour12,
              ),
              SizedBox(height: 8),
              VTimePicker(
                value: TimeOfDay(hour: 12, minute: 0),
                format: VTimeFormat.hour12,
              ),
              SizedBox(height: 8),
              VTimePicker(
                value: TimeOfDay(hour: 23, minute: 59),
                format: VTimeFormat.hour12,
              ),
              SizedBox(height: 8),
              VTimePicker(
                value: TimeOfDay(hour: 23, minute: 59),
                format: VTimeFormat.hour24,
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_time_picker_edge_times');
    });
  });
}
