import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';
import '../golden_test_helpers.dart';

void main() {
  group('VSwitch Golden Tests', () {
    testGoldens('VSwitch states', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              VSwitch(
                value: false,
                onChanged: (value) {},
              ),
              const SizedBox(height: 16),
              VSwitch(
                value: true,
                onChanged: (value) {},
              ),
              const SizedBox(height: 16),
              VSwitch(
                value: false,
                onChanged: (value) {},
                enabled: false,
              ),
              const SizedBox(height: 16),
              VSwitch(
                value: true,
                onChanged: (value) {},
                enabled: false,
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_switch_states');
    });

    testGoldens('VSwitch with labels', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              VSwitch(
                value: false,
                onChanged: (value) {},
                label: 'Enable notifications',
              ),
              const SizedBox(height: 16),
              VSwitch(
                value: true,
                onChanged: (value) {},
                label: 'Dark mode',
              ),
              const SizedBox(height: 16),
              VSwitch(
                value: false,
                onChanged: (value) {},
                label: 'Disabled setting',
                enabled: false,
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_switch_with_labels');
    });

    testGoldens('VSwitch adaptive styles', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Material style (adaptive = false)
              VSwitch(
                value: true,
                onChanged: (value) {},
                adaptive: false,
                label: 'Material Style',
              ),
              const SizedBox(height: 16),
              // Default adaptive style
              VSwitch(
                value: true,
                onChanged: (value) {},
                label: 'Adaptive Style',
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_switch_adaptive_styles');
    });

    testGoldens('VSwitch across themes', (tester) async {
      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_switch',
        builder: (theme) => Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            VSwitch(
              value: false,
              onChanged: (value) {},
              label: 'Off',
            ),
            const SizedBox(height: 16),
            VSwitch(
              value: true,
              onChanged: (value) {},
              label: 'On',
            ),
          ],
        ),
        devices: [Device.phone],
      );
    });

    testGoldens('VSwitch density variations', (tester) async {
      final densities = [
        VDensity.compact,
        VDensity.comfortable,
        VDensity.spacious,
      ];

      for (final density in densities) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            theme: VThemeData.defaultTheme().copyWith(density: density),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                VSwitch(
                  value: false,
                  onChanged: (value) {},
                  label: 'Switch ${density.name}',
                ),
                const SizedBox(height: 8),
                VSwitch(
                  value: true,
                  onChanged: (value) {},
                  label: 'Switch ${density.name} On',
                ),
              ],
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_switch_density_${density.name}');
      }
    });

    testGoldens('VSwitch long labels', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: SizedBox(
            width: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                VSwitch(
                  value: false,
                  onChanged: (value) {},
                  label: 'This is a very long label that should wrap properly',
                ),
                const SizedBox(height: 16),
                VSwitch(
                  value: true,
                  onChanged: (value) {},
                  label: 'Another extremely long label to test text wrapping behavior in switches',
                ),
              ],
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_switch_long_labels');
    });

    testGoldens('VSwitch dark mode', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          themeMode: ThemeMode.dark,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              VSwitch(
                value: false,
                onChanged: (value) {},
                label: 'Dark mode off',
              ),
              const SizedBox(height: 16),
              VSwitch(
                value: true,
                onChanged: (value) {},
                label: 'Dark mode on',
              ),
              const SizedBox(height: 16),
              VSwitch(
                value: false,
                onChanged: (value) {},
                label: 'Disabled',
                enabled: false,
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_switch_dark_mode');
    });
  });
}
