import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

import '../golden_test_helpers.dart';

void main() {
  group('VDateTimePicker Golden Tests', () {
    setUpAll(() async {
      await loadAppFonts();
    });

    testGoldens('VDateTimePicker basic rendering (combined)', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VDateTimePicker(
            placeholder: 'Select date & time',
            separateFields: false,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_time_picker_basic_combined');
    });

    testGoldens('VDateTimePicker basic rendering (separate)', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VDateTimePicker(
            separateFields: true,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_time_picker_basic_separate');
    });

    testGoldens('VDateTimePicker with value (combined)', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 14, 30);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDateTimePicker(
            value: testDateTime,
            separateFields: false,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_time_picker_with_value_combined');
    });

    testGoldens('VDateTimePicker with value (separate)', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 14, 30);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDateTimePicker(
            value: testDateTime,
            separateFields: true,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_time_picker_with_value_separate');
    });

    testGoldens('VDateTimePicker with label', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VDateTimePicker(
            label: 'Appointment',
            placeholder: 'Select date & time',
            separateFields: false,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_time_picker_with_label');
    });

    testGoldens('VDateTimePicker with clear button', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 14, 30);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDateTimePicker(
            value: testDateTime,
            showClearButton: true,
            separateFields: false,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_time_picker_with_clear_button');
    });

    testGoldens('VDateTimePicker disabled state', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VDateTimePicker(
            enabled: false,
            placeholder: 'Select date & time',
            separateFields: false,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_time_picker_disabled');
    });

    testGoldens('VDateTimePicker with 24-hour format', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 14, 30);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDateTimePicker(
            value: testDateTime,
            timeFormat: VTimeFormat.hour24,
            separateFields: true,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_time_picker_24h_format');
    });

    testGoldens('VDateTimePicker with custom format', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 14, 30);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDateTimePicker(
            value: testDateTime,
            dateTimeFormat: (dateTime) => '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}',
            separateFields: false,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_time_picker_custom_format');
    });

    testGoldens('VDateTimePicker across themes', (tester) async {
      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_date_time_picker',
        builder: (theme) => VDateTimePicker(
          value: DateTime(2024, 1, 15, 14, 30),
          separateFields: false,
        ),
        devices: [Device.phone],
      );
    });

    testGoldens('VDateTimePicker density variations', (tester) async {
      final densities = [
        VDensity.compact,
        VDensity.comfortable,
        VDensity.spacious,
      ];

      for (final density in densities) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            theme: VThemeData.defaultTheme().copyWith(density: density),
            child: VDateTimePicker(
              value: DateTime(2024, 1, 15, 14, 30),
              separateFields: false,
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_date_time_picker_density_${density.name}');
      }
    });

    testGoldens('VDateTimePicker corner variations', (tester) async {
      final cornerStyles = [
        VCornerStyle.sharp,
        VCornerStyle.subtle,
        VCornerStyle.rounded,
      ];

      for (final cornerStyle in cornerStyles) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            theme: VThemeData.defaultTheme().copyWith(cornerStyle: cornerStyle),
            child: VDateTimePicker(
              value: DateTime(2024, 1, 15, 14, 30),
              separateFields: false,
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_date_time_picker_corners_${cornerStyle.name}');
      }
    });

    testGoldens('VDateTimePicker states comparison', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 14, 30);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const VDateTimePicker(
                placeholder: 'Empty combined',
                separateFields: false,
              ),
              const SizedBox(height: 16),
              const VDateTimePicker(
                separateFields: true,
              ),
              const SizedBox(height: 16),
              VDateTimePicker(
                value: testDateTime,
                separateFields: false,
              ),
              const SizedBox(height: 16),
              VDateTimePicker(
                value: testDateTime,
                separateFields: true,
              ),
              const SizedBox(height: 16),
              const VDateTimePicker(
                enabled: false,
                placeholder: 'Disabled',
                separateFields: false,
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_time_picker_states_comparison');
    });

    testGoldens('VDateTimePicker field comparison', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 14, 30);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              VDateTimePicker(
                value: testDateTime,
                separateFields: false,
              ),
              const SizedBox(height: 16),
              VDateTimePicker(
                value: testDateTime,
                separateFields: true,
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_time_picker_field_comparison');
    });

    testGoldens('VDateTimePicker with label and value', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 9, 30);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDateTimePicker(
            label: 'Meeting Schedule',
            value: testDateTime,
            showClearButton: true,
            separateFields: false,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_time_picker_label_and_value');
    });

    testGoldens('VDateTimePicker separate fields with label', (tester) async {
      final testDateTime = DateTime(2024, 1, 15, 9, 30);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDateTimePicker(
            label: 'Event Schedule',
            value: testDateTime,
            separateFields: true,
            timeFormat: VTimeFormat.hour24,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_time_picker_separate_with_label');
    });

    testGoldens('VDateTimePicker edge times', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              VDateTimePicker(
                value: DateTime(2024, 1, 1, 0, 0),
                timeFormat: VTimeFormat.hour12,
                separateFields: false,
              ),
              const SizedBox(height: 8),
              VDateTimePicker(
                value: DateTime(2024, 12, 31, 23, 59),
                timeFormat: VTimeFormat.hour24,
                separateFields: false,
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_time_picker_edge_times');
    });
  });
}
