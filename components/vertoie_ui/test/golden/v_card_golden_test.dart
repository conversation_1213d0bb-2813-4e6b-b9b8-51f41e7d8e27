import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

import '../golden_test_helpers.dart';

void main() {
  group('VCard Golden Tests', () {
    setUpAll(() async {
      await loadAppFonts();
    });

    testGoldens('VCard elevation variants', (tester) async {
      final showcase = VGoldenTestHelpers.createComponentShowcase(
        title: 'VCard Elevation Variants',
        variants: [
          const VCard(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Text('Basic Card'),
            ),
          ),
          const VCard(
            padding: EdgeInsets.all(24),
            child: Text('Card with Custom Padding'),
          ),
          const VCard(
            margin: EdgeInsets.all(8),
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Text('Card with Margin'),
            ),
          ),
          const VCard(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Text('Another Card'),
            ),
          ),
        ],
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: showcase,
        ),
      );

      await screenMatchesGolden(tester, 'v_card_elevation_variants');
    });

    testGoldens('VCard with complex content', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VCard(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Card Title',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'This is a card with more complex content including multiple text elements and proper spacing.',
                  ),
                  SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      VButton(
                        variant: VButtonVariant.text,
                        onPressed: null,
                        child: Text('Cancel'),
                      ),
                      SizedBox(width: 8),
                      VButton(
                        onPressed: null,
                        child: Text('Save'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_card_complex_content');
    });

    testGoldens('VCard across themes', (tester) async {
      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_card',
        builder: (theme) => const VCard(
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Text('Themed Card Content'),
          ),
        ),
        devices: [Device.phone],
      );
    });
  });
}
