import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';
import '../golden_test_helpers.dart';

void main() {
  group('VCheckbox Golden Tests', () {
    setUpAll(() async {
      await loadAppFonts();
    });

    testGoldens('VCheckbox states showcase', (tester) async {
      final showcase = VGoldenTestHelpers.createComponentShowcase(
        title: 'VCheckbox States',
        columns: 2,
        variants: [
          // Basic states
          VCheckbox(
            value: false,
            onChanged: (value) {},
            label: 'Unchecked',
          ),
          VCheckbox(
            value: true,
            onChanged: (value) {},
            label: 'Checked',
          ),
          
          // Tristate
          VCheckbox(
            value: null,
            tristate: true,
            onChanged: (value) {},
            label: 'Indeterminate',
          ),
          
          // Disabled states
          VCheckbox(
            value: false,
            enabled: false,
            onChanged: (value) {},
            label: 'Disabled Unchecked',
          ),
          VCheckbox(
            value: true,
            enabled: false,
            onChanged: (value) {},
            label: 'Disabled Checked',
          ),
          VCheckbox(
            value: null,
            tristate: true,
            enabled: false,
            onChanged: (value) {},
            label: 'Disabled Indeterminate',
          ),
        ],
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: showcase,
        ),
      );

      await screenMatchesGolden(tester, 'v_checkbox_states_showcase');
    });

    testGoldens('VCheckbox without labels', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              VCheckbox(
                value: false,
                onChanged: (value) {},
              ),
              VCheckbox(
                value: true,
                onChanged: (value) {},
              ),
              VCheckbox(
                value: null,
                tristate: true,
                onChanged: (value) {},
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_checkbox_no_labels');
    });

    testGoldens('VCheckboxGroup vertical layout', (tester) async {
      final options = [
        VCheckboxOption(value: 'option1', label: 'First Option'),
        VCheckboxOption(value: 'option2', label: 'Second Option'),
        VCheckboxOption(value: 'option3', label: 'Third Option'),
      ];

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VCheckboxGroup<String>(
            label: 'Vertical Checkbox Group',
            options: options,
            selectedValues: ['option2'],
            direction: VCheckboxGroupDirection.vertical,
            onChanged: (values) {},
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_checkbox_group_vertical');
    });

    testGoldens('VCheckboxGroup horizontal layout', (tester) async {
      final options = [
        VCheckboxOption(value: 'option1', label: 'Option 1'),
        VCheckboxOption(value: 'option2', label: 'Option 2'),
        VCheckboxOption(value: 'option3', label: 'Option 3'),
      ];

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VCheckboxGroup<String>(
            label: 'Horizontal Checkbox Group',
            options: options,
            selectedValues: ['option1', 'option3'],
            direction: VCheckboxGroupDirection.horizontal,
            onChanged: (values) {},
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_checkbox_group_horizontal');
    });

    testGoldens('VCheckboxGroup with select all', (tester) async {
      final options = [
        VCheckboxOption(value: 'option1', label: 'First Option'),
        VCheckboxOption(value: 'option2', label: 'Second Option'),
        VCheckboxOption(value: 'option3', label: 'Third Option'),
        VCheckboxOption(value: 'option4', label: 'Fourth Option'),
      ];

      final showcase = VGoldenTestHelpers.createComponentShowcase(
        title: 'VCheckboxGroup with Select All',
        columns: 1,
        variants: [
          // None selected
          VCheckboxGroup<String>(
            label: 'None Selected',
            options: options,
            selectedValues: [],
            showSelectAll: true,
            onChanged: (values) {},
          ),
          
          // Partially selected
          VCheckboxGroup<String>(
            label: 'Partially Selected',
            options: options,
            selectedValues: ['option1', 'option3'],
            showSelectAll: true,
            onChanged: (values) {},
          ),
          
          // All selected
          VCheckboxGroup<String>(
            label: 'All Selected',
            options: options,
            selectedValues: ['option1', 'option2', 'option3', 'option4'],
            showSelectAll: true,
            onChanged: (values) {},
          ),
        ],
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: showcase,
        ),
      );

      await screenMatchesGolden(tester, 'v_checkbox_group_select_all');
    });

    testGoldens('VCheckbox across themes', (tester) async {
      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_checkbox',
        builder: (theme) => Column(
          children: [
            VCheckbox(
              value: false,
              onChanged: (value) {},
              label: 'Unchecked',
            ),
            const SizedBox(height: 16),
            VCheckbox(
              value: true,
              onChanged: (value) {},
              label: 'Checked',
            ),
            const SizedBox(height: 16),
            VCheckbox(
              value: null,
              tristate: true,
              onChanged: (value) {},
              label: 'Indeterminate',
            ),
          ],
        ),
        devices: [Device.phone],
      );
    });

    testGoldens('VCheckboxGroup across themes', (tester) async {
      final options = [
        VCheckboxOption(value: 'option1', label: 'First Option'),
        VCheckboxOption(value: 'option2', label: 'Second Option'),
        VCheckboxOption(value: 'option3', label: 'Third Option'),
      ];

      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_checkbox_group',
        builder: (theme) => VCheckboxGroup<String>(
          label: 'Sample Group',
          options: options,
          selectedValues: ['option2'],
          showSelectAll: true,
          onChanged: (values) {},
        ),
        devices: [Device.phone],
      );
    });
  });
}
