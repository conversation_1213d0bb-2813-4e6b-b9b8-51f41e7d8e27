import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

import '../golden_test_helpers.dart';

void main() {
  group('VDateRangePicker Golden Tests', () {
    setUpAll(() async {
      await loadAppFonts();
    });

    testGoldens('VDateRangePicker basic rendering', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VDateRangePicker(
            startPlaceholder: 'Start date',
            endPlaceholder: 'End date',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_range_picker_basic');
    });

    testGoldens('VDateRangePicker with value', (tester) async {
      final testRange = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDateRangePicker(
            value: testRange,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_range_picker_with_value');
    });

    testGoldens('VDateRangePicker with label', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VDateRangePicker(
            label: 'Project Duration',
            startPlaceholder: 'Start date',
            endPlaceholder: 'End date',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_range_picker_with_label');
    });

    testGoldens('VDateRangePicker with clear button', (tester) async {
      final testRange = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDateRangePicker(
            value: testRange,
            showClearButton: true,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_range_picker_with_clear_button');
    });

    testGoldens('VDateRangePicker disabled state', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VDateRangePicker(
            enabled: false,
            startPlaceholder: 'Start date',
            endPlaceholder: 'End date',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_range_picker_disabled');
    });

    testGoldens('VDateRangePicker with custom format', (tester) async {
      final testRange = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDateRangePicker(
            value: testRange,
            dateFormat: (date) => '${date.day}/${date.month}',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_range_picker_custom_format');
    });

    testGoldens('VDateRangePicker across themes', (tester) async {
      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_date_range_picker',
        builder: (theme) => VDateRangePicker(
          value: VDateRange(
            start: DateTime(2024, 1, 15),
            end: DateTime(2024, 1, 20),
          ),
        ),
        devices: [Device.phone],
      );
    });

    testGoldens('VDateRangePicker density variations', (tester) async {
      final densities = [
        VDensity.compact,
        VDensity.comfortable,
        VDensity.spacious,
      ];

      for (final density in densities) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            theme: VThemeData.defaultTheme().copyWith(density: density),
            child: VDateRangePicker(
              value: VDateRange(
                start: DateTime(2024, 1, 15),
                end: DateTime(2024, 1, 20),
              ),
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_date_range_picker_density_${density.name}');
      }
    });

    testGoldens('VDateRangePicker corner variations', (tester) async {
      final cornerStyles = [
        VCornerStyle.sharp,
        VCornerStyle.subtle,
        VCornerStyle.rounded,
      ];

      for (final cornerStyle in cornerStyles) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            theme: VThemeData.defaultTheme().copyWith(cornerStyle: cornerStyle),
            child: VDateRangePicker(
              value: VDateRange(
                start: DateTime(2024, 1, 15),
                end: DateTime(2024, 1, 20),
              ),
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_date_range_picker_corners_${cornerStyle.name}');
      }
    });

    testGoldens('VDateRangePicker states comparison', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const VDateRangePicker(
                startPlaceholder: 'Start',
                endPlaceholder: 'End',
              ),
              const SizedBox(height: 16),
              VDateRangePicker(
                value: VDateRange(
                  start: DateTime(2024, 1, 15),
                  end: DateTime(2024, 1, 20),
                ),
              ),
              const SizedBox(height: 16),
              VDateRangePicker(
                value: VDateRange(
                  start: DateTime(2024, 1, 15),
                  end: DateTime(2024, 1, 20),
                ),
                showClearButton: true,
              ),
              const SizedBox(height: 16),
              const VDateRangePicker(
                enabled: false,
                startPlaceholder: 'Start',
                endPlaceholder: 'End',
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_range_picker_states_comparison');
    });

    testGoldens('VDateRangePicker with long range', (tester) async {
      final testRange = VDateRange(
        start: DateTime(2024, 1, 1),
        end: DateTime(2024, 12, 31),
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDateRangePicker(
            value: testRange,
            dateFormat: (date) => '${date.day}/${date.month}/${date.year}',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_range_picker_long_range');
    });

    testGoldens('VDateRangePicker with same month range', (tester) async {
      final testRange = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 25),
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDateRangePicker(
            value: testRange,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_range_picker_same_month');
    });

    testGoldens('VDateRangePicker with label and value', (tester) async {
      final testRange = VDateRange(
        start: DateTime(2024, 1, 15),
        end: DateTime(2024, 1, 20),
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDateRangePicker(
            label: 'Vacation Period',
            value: testRange,
            showClearButton: true,
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_range_picker_label_and_value');
    });
  });
}
