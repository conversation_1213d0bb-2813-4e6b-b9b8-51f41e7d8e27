import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';
import '../golden_test_helpers.dart';

void main() {
  group('VSlider Golden Tests', () {
    testGoldens('VSlider single value states', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: SizedBox(
            width: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                VSlider(
                  type: VSliderType.single,
                  value: 0.0,
                  onChanged: (value) {},
                ),
                const SizedBox(height: 24),
                VSlider(
                  type: VSliderType.single,
                  value: 0.5,
                  onChanged: (value) {},
                ),
                const SizedBox(height: 24),
                VSlider(
                  type: VSliderType.single,
                  value: 1.0,
                  onChanged: (value) {},
                ),
                const Sized<PERSON>ox(height: 24),
                VSlider(
                  type: VSliderType.single,
                  value: 0.5,
                  onChanged: (value) {},
                  enabled: false,
                ),
              ],
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_slider_single_states');
    });

    testGoldens('VSlider range value states', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: SizedBox(
            width: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                VSlider(
                  type: VSliderType.range,
                  rangeValues: const RangeValues(0.0, 1.0),
                  onRangeChanged: (values) {},
                ),
                const SizedBox(height: 24),
                VSlider(
                  type: VSliderType.range,
                  rangeValues: const RangeValues(0.2, 0.8),
                  onRangeChanged: (values) {},
                ),
                const SizedBox(height: 24),
                VSlider(
                  type: VSliderType.range,
                  rangeValues: const RangeValues(0.3, 0.7),
                  onRangeChanged: (values) {},
                  enabled: false,
                ),
              ],
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_slider_range_states');
    });

    testGoldens('VSlider with labels', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: SizedBox(
            width: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                VSlider(
                  type: VSliderType.single,
                  value: 0.7,
                  onChanged: (value) {},
                  label: 'Volume',
                ),
                const SizedBox(height: 32),
                VSlider(
                  type: VSliderType.range,
                  rangeValues: const RangeValues(0.2, 0.8),
                  onRangeChanged: (values) {},
                  label: 'Price Range',
                ),
                const SizedBox(height: 32),
                VSlider(
                  type: VSliderType.single,
                  value: 0.3,
                  onChanged: (value) {},
                  label: 'Disabled Setting',
                  enabled: false,
                ),
              ],
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_slider_with_labels');
    });

    testGoldens('VSlider stepped values', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: SizedBox(
            width: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                VSlider(
                  type: VSliderType.single,
                  value: 5.0,
                  onChanged: (value) {},
                  min: 0.0,
                  max: 10.0,
                  divisions: 10,
                  label: 'Rating (0-10)',
                ),
                const SizedBox(height: 32),
                VSlider(
                  type: VSliderType.range,
                  rangeValues: const RangeValues(2.0, 8.0),
                  onRangeChanged: (values) {},
                  min: 0.0,
                  max: 10.0,
                  divisions: 10,
                  label: 'Age Range',
                ),
              ],
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_slider_stepped_values');
    });

    testGoldens('VSlider across themes', (tester) async {
      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_slider',
        builder: (theme) => SizedBox(
          width: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              VSlider(
                type: VSliderType.single,
                value: 0.6,
                onChanged: (value) {},
                label: 'Single Slider',
              ),
              const SizedBox(height: 32),
              VSlider(
                type: VSliderType.range,
                rangeValues: const RangeValues(0.3, 0.7),
                onRangeChanged: (values) {},
                label: 'Range Slider',
              ),
            ],
          ),
        ),
        devices: [Device.phone],
      );
    });

    testGoldens('VSlider density variations', (tester) async {
      final densities = [
        VDensity.compact,
        VDensity.comfortable,
        VDensity.spacious,
      ];

      for (final density in densities) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            theme: VThemeData.defaultTheme().copyWith(density: density),
            child: SizedBox(
              width: 300,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  VSlider(
                    type: VSliderType.single,
                    value: 0.5,
                    onChanged: (value) {},
                    label: 'Single ${density.name}',
                  ),
                  const SizedBox(height: 24),
                  VSlider(
                    type: VSliderType.range,
                    rangeValues: const RangeValues(0.3, 0.7),
                    onRangeChanged: (values) {},
                    label: 'Range ${density.name}',
                  ),
                ],
              ),
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_slider_density_${density.name}');
      }
    });

    testGoldens('VSlider custom ranges', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: SizedBox(
            width: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                VSlider(
                  type: VSliderType.single,
                  value: 50.0,
                  onChanged: (value) {},
                  min: 0.0,
                  max: 100.0,
                  label: 'Percentage (0-100)',
                ),
                const SizedBox(height: 32),
                VSlider(
                  type: VSliderType.range,
                  rangeValues: const RangeValues(-10.0, 10.0),
                  onRangeChanged: (values) {},
                  min: -20.0,
                  max: 20.0,
                  label: 'Temperature Range (-20 to 20)',
                ),
              ],
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_slider_custom_ranges');
    });

    testGoldens('VSlider dark mode', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          themeMode: ThemeMode.dark,
          child: SizedBox(
            width: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                VSlider(
                  type: VSliderType.single,
                  value: 0.6,
                  onChanged: (value) {},
                  label: 'Dark mode single',
                ),
                const SizedBox(height: 32),
                VSlider(
                  type: VSliderType.range,
                  rangeValues: const RangeValues(0.2, 0.8),
                  onRangeChanged: (values) {},
                  label: 'Dark mode range',
                ),
                const SizedBox(height: 32),
                VSlider(
                  type: VSliderType.single,
                  value: 0.4,
                  onChanged: (value) {},
                  label: 'Disabled',
                  enabled: false,
                ),
              ],
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_slider_dark_mode');
    });
  });
}
