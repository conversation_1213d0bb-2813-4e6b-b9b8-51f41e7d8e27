import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';
import '../golden_test_helpers.dart';

void main() {
  group('VRadio Golden Tests', () {
    setUpAll(() async {
      await loadAppFonts();
    });

    testGoldens('VRadio states showcase', (tester) async {
      final showcase = VGoldenTestHelpers.createComponentShowcase(
        title: 'VRadio States',
        columns: 2,
        variants: [
          // Basic states
          VRadio<String>(
            value: 'option1',
            groupValue: null,
            onChanged: (value) {},
            label: 'Unselected',
          ),
          VRadio<String>(
            value: 'option1',
            groupValue: 'option1',
            onChanged: (value) {},
            label: 'Selected',
          ),
          
          // Disabled states
          VRadio<String>(
            value: 'option1',
            groupValue: null,
            enabled: false,
            onChanged: (value) {},
            label: 'Disabled Unselected',
          ),
          VRadio<String>(
            value: 'option1',
            groupValue: 'option1',
            enabled: false,
            onChanged: (value) {},
            label: 'Disabled Selected',
          ),
        ],
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: showcase,
        ),
      );

      await screenMatchesGolden(tester, 'v_radio_states_showcase');
    });

    testGoldens('VRadio without labels', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              VRadio<String>(
                value: 'option1',
                groupValue: null,
                onChanged: (value) {},
              ),
              VRadio<String>(
                value: 'option2',
                groupValue: 'option2',
                onChanged: (value) {},
              ),
              VRadio<String>(
                value: 'option3',
                groupValue: 'option2',
                onChanged: (value) {},
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_radio_no_labels');
    });

    testGoldens('VRadioGroup vertical layout', (tester) async {
      final options = [
        VRadioOption(value: 'option1', label: 'First Option'),
        VRadioOption(value: 'option2', label: 'Second Option'),
        VRadioOption(value: 'option3', label: 'Third Option'),
      ];

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VRadioGroup<String>(
            label: 'Vertical Radio Group',
            options: options,
            value: 'option2',
            direction: VRadioGroupDirection.vertical,
            onChanged: (value) {},
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_radio_group_vertical');
    });

    testGoldens('VRadioGroup horizontal layout', (tester) async {
      final options = [
        VRadioOption(value: 'option1', label: 'Option 1'),
        VRadioOption(value: 'option2', label: 'Option 2'),
        VRadioOption(value: 'option3', label: 'Option 3'),
      ];

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VRadioGroup<String>(
            label: 'Horizontal Radio Group',
            options: options,
            value: 'option1',
            direction: VRadioGroupDirection.horizontal,
            onChanged: (value) {},
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_radio_group_horizontal');
    });

    testGoldens('VRadioGroup with disabled options', (tester) async {
      final options = [
        VRadioOption(value: 'option1', label: 'Enabled Option'),
        VRadioOption(value: 'option2', label: 'Disabled Option', enabled: false),
        VRadioOption(value: 'option3', label: 'Another Enabled'),
        VRadioOption(value: 'option4', label: 'Another Disabled', enabled: false),
      ];

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VRadioGroup<String>(
            label: 'Mixed Enabled/Disabled Options',
            options: options,
            value: 'option1',
            onChanged: (value) {},
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_radio_group_disabled_options');
    });

    testGoldens('VRadioGroup selection states', (tester) async {
      final options = [
        VRadioOption(value: 'option1', label: 'First Option'),
        VRadioOption(value: 'option2', label: 'Second Option'),
        VRadioOption(value: 'option3', label: 'Third Option'),
      ];

      final showcase = VGoldenTestHelpers.createComponentShowcase(
        title: 'VRadioGroup Selection States',
        columns: 1,
        variants: [
          // No selection
          VRadioGroup<String>(
            label: 'No Selection',
            options: options,
            value: null,
            onChanged: (value) {},
          ),
          
          // First option selected
          VRadioGroup<String>(
            label: 'First Selected',
            options: options,
            value: 'option1',
            onChanged: (value) {},
          ),
          
          // Last option selected
          VRadioGroup<String>(
            label: 'Last Selected',
            options: options,
            value: 'option3',
            onChanged: (value) {},
          ),
        ],
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: showcase,
        ),
      );

      await screenMatchesGolden(tester, 'v_radio_group_selection_states');
    });

    testGoldens('VRadio across themes', (tester) async {
      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_radio',
        builder: (theme) => Column(
          children: [
            VRadio<String>(
              value: 'option1',
              groupValue: null,
              onChanged: (value) {},
              label: 'Unselected',
            ),
            const SizedBox(height: 16),
            VRadio<String>(
              value: 'option2',
              groupValue: 'option2',
              onChanged: (value) {},
              label: 'Selected',
            ),
          ],
        ),
        devices: [Device.phone],
      );
    });

    testGoldens('VRadioGroup across themes', (tester) async {
      final options = [
        VRadioOption(value: 'option1', label: 'First Option'),
        VRadioOption(value: 'option2', label: 'Second Option'),
        VRadioOption(value: 'option3', label: 'Third Option'),
      ];

      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_radio_group',
        builder: (theme) => VRadioGroup<String>(
          label: 'Sample Group',
          options: options,
          value: 'option2',
          onChanged: (value) {},
        ),
        devices: [Device.phone],
      );
    });
  });
}
