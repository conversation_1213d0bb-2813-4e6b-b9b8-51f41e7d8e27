import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

import '../golden_test_helpers.dart';

void main() {
  group('VDatePicker Golden Tests', () {
    setUpAll(() async {
      await loadAppFonts();
    });

    testGoldens('VDatePicker basic rendering', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VDatePicker(
            placeholder: 'Select date',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_picker_basic');
    });

    testGoldens('VDatePicker with value', (tester) async {
      final testDate = DateTime(2024, 1, 15);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDatePicker(
            value: testDate,
            placeholder: 'Select date',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_picker_with_value');
    });

    testGoldens('VDatePicker with label', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VDatePicker(
            label: 'Birth Date',
            placeholder: 'Select date',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_picker_with_label');
    });

    testGoldens('VDatePicker with clear button', (tester) async {
      final testDate = DateTime(2024, 1, 15);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDatePicker(
            value: testDate,
            showClearButton: true,
            placeholder: 'Select date',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_picker_with_clear_button');
    });

    testGoldens('VDatePicker disabled state', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VDatePicker(
            enabled: false,
            placeholder: 'Select date',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_picker_disabled');
    });

    testGoldens('VDatePicker with custom format', (tester) async {
      final testDate = DateTime(2024, 1, 15);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDatePicker(
            value: testDate,
            dateFormat: (date) => '${date.day}/${date.month}/${date.year}',
            placeholder: 'Select date',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_picker_custom_format');
    });

    testGoldens('VDatePicker across themes', (tester) async {
      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_date_picker',
        builder: (theme) => VDatePicker(
          value: DateTime(2024, 1, 15),
          placeholder: 'Select date',
        ),
        devices: [Device.phone],
      );
    });

    testGoldens('VDatePicker density variations', (tester) async {
      final densities = [
        VDensity.compact,
        VDensity.comfortable,
        VDensity.spacious,
      ];

      for (final density in densities) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            theme: VThemeData.defaultTheme().copyWith(density: density),
            child: VDatePicker(
              value: DateTime(2024, 1, 15),
              placeholder: 'Select date',
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_date_picker_density_${density.name}');
      }
    });

    testGoldens('VDatePicker corner variations', (tester) async {
      final cornerStyles = [
        VCornerStyle.sharp,
        VCornerStyle.subtle,
        VCornerStyle.rounded,
      ];

      for (final cornerStyle in cornerStyles) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            theme: VThemeData.defaultTheme().copyWith(cornerStyle: cornerStyle),
            child: VDatePicker(
              value: DateTime(2024, 1, 15),
              placeholder: 'Select date',
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_date_picker_corners_${cornerStyle.name}');
      }
    });

    testGoldens('VDatePicker states comparison', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const VDatePicker(
                placeholder: 'Empty state',
              ),
              const SizedBox(height: 16),
              VDatePicker(
                value: DateTime(2024, 1, 15),
                placeholder: 'With value',
              ),
              const SizedBox(height: 16),
              VDatePicker(
                value: DateTime(2024, 1, 15),
                showClearButton: true,
                placeholder: 'With clear button',
              ),
              const SizedBox(height: 16),
              const VDatePicker(
                enabled: false,
                placeholder: 'Disabled',
              ),
            ],
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_picker_states_comparison');
    });

    testGoldens('VDatePicker with long date format', (tester) async {
      final testDate = DateTime(2024, 12, 25);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDatePicker(
            value: testDate,
            dateFormat: (date) => 'Monday, December 25th, 2024',
            placeholder: 'Select date',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_picker_long_format');
    });

    testGoldens('VDatePicker with label and value', (tester) async {
      final testDate = DateTime(2024, 1, 15);

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: VDatePicker(
            label: 'Appointment Date',
            value: testDate,
            showClearButton: true,
            placeholder: 'Select date',
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_date_picker_label_and_value');
    });
  });
}
