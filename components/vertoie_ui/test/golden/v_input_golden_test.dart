import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

import '../golden_test_helpers.dart';

void main() {
  group('VInput Golden Tests', () {
    setUpAll(() async {
      await loadAppFonts();
    });

    testGoldens('VInput states showcase', (tester) async {
      final showcase = VGoldenTestHelpers.createComponentShowcase(
        title: 'VInput States',
        columns: 1,
        variants: [
          const VInput(
            placeholder: 'Enter some text',
          ),
          VInput(
            placeholder: 'Enter some text',
            controller: TextEditingController(text: 'Sample text content'),
          ),
          const VInput(
            placeholder: 'This is disabled',
            enabled: false,
          ),
          const VInput(
            placeholder: 'Enter some text',
            enabled: true,
          ),
        ],
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: showcase,
        ),
      );

      await screenMatchesGolden(tester, 'v_input_states_showcase');
    });

    testGoldens('VInput across themes', (tester) async {
      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_input',
        builder: (theme) => VInput(
          placeholder: 'Enter text',
          controller: TextEditingController(text: 'Sample text'),
        ),
        devices: [Device.phone],
      );
    });

    testGoldens('VInput focused state', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: const VInput(
            placeholder: 'This input is focused',
          ),
        ),
      );

      // Allow focus to take effect
      await tester.pump();

      await screenMatchesGolden(tester, 'v_input_focused');
    });

    testGoldens('VInput with different states', (tester) async {
      final variants = VGoldenTestHelpers.createComponentShowcase(
        title: 'VInput Different States',
        columns: 1,
        variants: [
          VInput(
            placeholder: 'Search...',
            onChanged: (value) {},
          ),
          VInput(
            placeholder: 'Enter password',
            onChanged: (value) {},
          ),
        ],
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: variants,
        ),
      );

      await screenMatchesGolden(tester, 'v_input_different_states');
    });
  });
}
