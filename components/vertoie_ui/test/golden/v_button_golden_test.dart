import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:vertoie_ui/vertoie_ui.dart';

import '../golden_test_helpers.dart';

void main() {
  group('VButton Golden Tests', () {
    setUpAll(() async {
      await loadAppFonts();
    });

    testGoldens('VButton variants showcase', (tester) async {
      final showcase = VGoldenTestHelpers.createComponentShowcase(
        title: 'VButton Variants',
        variants: [
          VButton(
            onPressed: () {},
            child: const Text('Primary'),
          ),
          VButton(
            onPressed: () {},
            variant: VButtonVariant.secondary,
            child: const Text('Secondary'),
          ),
          VButton(
            onPressed: () {},
            variant: VButtonVariant.text,
            child: const Text('Text Button'),
          ),
          VButton(
            onPressed: () {},
            variant: VButtonVariant.outlined,
            child: const Text('Outlined'),
          ),
        ],
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: showcase,
        ),
      );

      await screenMatchesGolden(tester, 'v_button_variants_showcase');
    });

    testGoldens('VButton states', (tester) async {
      await VGoldenTestHelpers.testComponentStates(
        tester: tester,
        componentName: 'v_button',
        normalBuilder: () => VButton(
          onPressed: () {},
          child: const Text('Normal'),
        ),
        disabledBuilder: () => const VButton(
          onPressed: null,
          child: Text('Disabled'),
        ),
        loadingBuilder: () => VButton(
          onPressed: () {},
          isLoading: true,
          child: const Text('Loading'),
        ),
      );
    });

    testGoldens('VButton sizes', (tester) async {
      final sizes = [
        VButtonSize.small,
        VButtonSize.medium,
        VButtonSize.large,
      ];

      for (final size in sizes) {
        await tester.pumpWidgetBuilder(
          VGoldenTestHelpers.createThemedTestWidget(
            child: VButton(
              onPressed: () {},
              size: size,
              child: Text('Button ${size.name}'),
            ),
          ),
        );

        await screenMatchesGolden(tester, 'v_button_size_${size.name}');
      }
    });

    testGoldens('VButton across themes', (tester) async {
      await VGoldenTestHelpers.testComponentAcrossThemes(
        tester: tester,
        componentName: 'v_button',
        builder: (theme) => VButton(
          onPressed: () {},
          child: const Text('Themed Button'),
        ),
        devices: [Device.phone],
      );
    });

    testGoldens('VButton with icons', (tester) async {
      final iconVariants = VGoldenTestHelpers.createComponentShowcase(
        title: 'VButton with Icons',
        variants: [
          VButton(
            onPressed: () {},
            icon: const Icon(Icons.star),
            child: const Text('Icon Left'),
          ),
          VButton(
            onPressed: () {},
            icon: const Icon(Icons.favorite),
            child: const Text(''),
          ),
        ],
      );

      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: iconVariants,
        ),
      );

      await screenMatchesGolden(tester, 'v_button_with_icons');
    });

    testGoldens('VButton long text handling', (tester) async {
      await tester.pumpWidgetBuilder(
        VGoldenTestHelpers.createThemedTestWidget(
          child: SizedBox(
            width: 200,
            child: VButton(
              onPressed: () {},
              child: const Text('This is a very long button text that should be handled properly'),
            ),
          ),
        ),
      );

      await screenMatchesGolden(tester, 'v_button_long_text');
    });
  });
}
