# Vertoie

> AI-Powered Business Management Platform

Vertoie is a comprehensive business management platform that combines the power of AI with intuitive design to help businesses manage their operations, data, and growth.

## 🏗️ Project Structure

```
vertoie/
├── 🚀 platform/          # Core platform services
│   ├── backend/          # Go API server
│   ├── frontend/         # Flutter mobile/web app
│   └── reference/        # Platform documentation
├── 🌐 web/              # Go web server & static site
├── 🧩 components/       # Flutter UI component library
│   └── vertoie_ui/      # Reusable Flutter components
├── 🎨 brand/           # Brand assets & guidelines
├── flake.nix           # Nix development environment
├── .envrc              # Direnv configuration
└── vertoie.code-workspace  # VS Code workspace
```

## 🚀 Quick Start

### Prerequisites

- [Nix](https://nixos.org/download.html) with flakes enabled
- [direnv](https://direnv.net/) (optional but recommended)

### Setup

1. **Clone and enter the repository:**
   ```bash
   git clone <repository-url>
   cd vertoie
   ```

2. **Enter the development environment:**
   ```bash
   nix develop
   ```
   
   Or with direnv (after allowing):
   ```bash
   direnv allow
   ```

3. **Run the setup script:**
   ```bash
   nix run .#setup
   ```

4. **Open in VS Code:**
   ```bash
   code vertoie.code-workspace
   ```

### Development

Start all development servers:
```bash
nix run .#dev
```

Or start services individually:

**Platform Backend:**
```bash
cd platform/backend
go run .
# Available at: http://localhost:8000
```

**Web Server:**
```bash
cd web
air  # Hot reload enabled
# Available at: http://localhost:3000
```

**Flutter App:**
```bash
cd platform/frontend
flutter run
```

**Components Demo:**
```bash
cd components/vertoie_ui/example
flutter run
```

## 🧩 Components

### Platform
- **Backend**: Go-based API server with authentication, data management, and AI integration
- **Frontend**: Flutter cross-platform application (iOS, Android, Web, Desktop)

### Web
- **Server**: Go-based web server for static content and web-specific features
- **Templates**: HTML templates with modern CSS and JavaScript

### Components
- **vertoie_ui**: Comprehensive Flutter component library
  - Themeable design system
  - Accessibility features
  - Comprehensive testing
  - Storybook-style examples

### Brand
- **Assets**: Logos, colors, typography guidelines
- **Guidelines**: Brand usage and design standards

## 🛠️ Development Environment

The project uses Nix flakes to provide a reproducible development environment with:

### Languages & Runtimes
- **Go 1.23** - Backend and web server development
- **Dart & Flutter** - Mobile and web frontend development
- **Node.js 22** - Web tooling and build processes
- **Python 3** - AI/ML features and scripting

### Development Tools
- **VS Code** - Primary IDE with workspace configuration
- **Language Servers** - Go (gopls), Dart, TypeScript, Nix
- **Formatters** - goimports, prettier, nixpkgs-fmt
- **Linters** - golangci-lint, eslint, dart analyzer

### Databases & Services
- **PostgreSQL 16** - Primary database
- **Redis** - Caching and session storage
- **SQLite** - Local development and testing

### Build & Deploy Tools
- **Docker & Docker Compose** - Containerization
- **Air** - Go hot reload development
- **Flutter Build Tools** - Cross-platform app building
- **Android SDK** - Mobile development (API 33, 34)

### Utilities
- **Git tooling** - gitui, gh (GitHub CLI)
- **Network tools** - httpie, curl, wget
- **File tools** - ripgrep, fd, bat, exa
- **Monitoring** - htop, bottom, hyperfine
- **Documentation** - mdbook

## 📝 VS Code Workspace

The workspace is configured with:
- **Multi-root setup** for different project components
- **Integrated tasks** for building, running, and testing
- **Launch configurations** for debugging
- **Recommended extensions** for optimal development experience
- **Intelligent file nesting** and exclusions

### Workspace Folders
- 🏠 **Root** - Overall project coordination
- 🚀 **Platform Backend** - API server development
- 📱 **Platform Frontend** - Mobile app development  
- 🌐 **Web Server** - Web server development
- 🧩 **UI Components** - Component library development
- 🎨 **Brand Assets** - Design resources
- 📚 **Reference** - Documentation and guides

## 🧪 Testing

Run tests across all components:

```bash
# Run all tests
nix run .#test

# Run specific component tests
nix run .#backend-test
cd components/vertoie_ui && flutter test
cd platform/frontend && flutter test
```

## 🏗️ Building

**All Applications:**
```bash
# Build everything
nix run .#build

# Build specific components
nix run .#backend-build
cd web && go build -o web .
cd platform/frontend && flutter build apk
cd components/vertoie_ui && flutter build
```

**Individual Components:**

**Platform Backend:**
```bash
nix run .#backend-build
```

**Web Server:**
```bash
cd web
go build -o web .
```

**Flutter Apps:**
```bash
cd platform/frontend
flutter build apk      # Android
flutter build ios      # iOS
flutter build web      # Web
```

**Components Library:**
```bash
cd components/vertoie_ui
flutter packages pub publish --dry-run
```

## 🚀 Development

### Quick Start

```bash
# Setup environment
nix run .#setup

# Start all development servers
nix run .#dev

# Or run individual services
nix run .#backend
nix run .#web
nix run .#flutter
nix run .#components
```

### Database Management

```bash
# Run migrations
nix run .#migrate-up

# Check migration status
nix run .#migrate-status

# Create new migration
nix run .#migrate-new add_user_settings

# Reset database (⚠️ DANGER)
nix run .#migrate-reset
```

### Development Commands

```bash
# Code quality
nix run .#format
nix run .#lint
nix run .#backend-lint

# Dependencies
nix run .#deps

# Clean build artifacts
nix run .#clean

# Check status
nix run .#status
nix run .#check-ports
```

## 🚀 Deployment

### Using Nix
```bash
# Build platform backend
nix run .#backend-build

# Build web server
cd web && go build -o web .
```

### Using Docker
```bash
# Backend
docker build -f platform/backend/Dockerfile -t vertoie-backend .

# Web
docker build -f web/Dockerfile -t vertoie-web .
```

## 🔧 Configuration

### Environment Variables

All environment variables are configured in `.envrc`:

```bash
# Database URLs
export DATABASE_URL="*******************************************/vertoie_dev?sslmode=disable"
export WEB_DATABASE_URL="********************************************/vertoie_dev?sslmode=disable"
export REDIS_URL="redis://192.168.1.41:6379"

# Development settings
export GO_ENV="development"
export PORT="8000"
export WEB_PORT="8001"
```

**Production:**
Set appropriate values for your deployment environment.

### Database Setup

1. **Database is managed separately** - no local setup required
2. **Run migrations:**
   ```bash
   nix run .#migrate-up
   ```
3. **Check status:**
   ```bash
   nix run .#migrate-status
   ```

## 📚 Documentation

- [Platform Architecture](platform/reference/BackendArchitecture.md)
- [Component Library Guide](components/vertoie_ui/README.md)
- [Theme Integration](components/vertoie_ui/THEME_INTEGRATION.md)
- [Brand Guidelines](brand/README.md)

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch** (`git checkout -b feature/amazing-feature`)
3. **Make changes** following the established patterns
4. **Test thoroughly** across all affected components
5. **Submit a pull request**

### Code Style

- **Go**: Follow `goimports` formatting and `golangci-lint` rules
- **Dart**: Follow `dart format` and `dart analyze` recommendations
- **TypeScript/JavaScript**: Follow `prettier` and `eslint` configuration
- **Nix**: Follow `nixpkgs-fmt` formatting

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with modern development tools and best practices
- Powered by Nix for reproducible development environments
- Designed with accessibility and user experience in mind
