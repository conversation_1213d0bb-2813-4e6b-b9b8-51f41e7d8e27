---
description: 
globs: **/*.dart
alwaysApply: false
---
# Dart/Flutter Rules for Vertoie

Dart version: 3.8.1
Flutter version: 3.32

## Null Safety
- Always use null safety
- Avoid `!` operator when possible - use `??` and `?.` operators instead
- Prefer nullable types over late variables when appropriate

## Widget Design
- Use const constructors whenever possible
- Prefer StatelessWidget over StatefulWidget when state isn't needed
- Keep widgets focused and under 20 lines
- Use composition over inheritance
- Extract complex widgets into separate files

## Dynamic Rendering for LLM Integration
- Design components to work with LLM-generated JSONB data models
- Use Map<String, dynamic> for flexible data structures
- Implement runtime validation for dynamic schemas

```dart
// ✅ Good: Component that adapts to LLM-generated data
class D<PERSON>F<PERSON><PERSON><PERSON> extends StatelessWidget {
  const DynamicFormField({
    super.key,
    required this.fieldDefinition, // LLM-generated field spec
    required this.onChanged,
  });

  final Map<String, dynamic> fieldDefinition;
  final ValueChanged<dynamic> onChanged;

  @override
  Widget build(BuildContext context) {
    return _buildFieldByType(fieldDefinition);
  }
}
```

## State Management
- Use Provider/Riverpod patterns consistently
- Keep state as local as possible
- Avoid global state when not necessary
- Use proper disposal for controllers and listeners

## Vertoie UI Library Standards
- All components must support the adaptive theming system
- Use discrete theme options, not infinite customization
- Implement accessibility support (screen readers, keyboard navigation)
- Follow the VButton, VTextField naming convention

## Testing Requirements
- Write widget tests for all components
- Use golden tests for visual regression testing
- Mock LLM responses for consistent testing
- Test with dynamic data structures