---
description: 
globs: 
alwaysApply: true
---
# Chat Interaction Rules for Vertoie

## Response Requirements
- Always ask for clarification if requirements are unclear
- Provide specific, actionable code examples when explaining concepts
- Reference the Vertoie architecture (LLM integration, multi-schema DB, Flutter UI)
- Explain WHY architectural decisions are made, not just WHAT to do

## Code Examples Standards
- Always show complete, working examples that follow Vertoie patterns
- Include proper error handling in all code examples
- Use Vertoie naming conventions (VButton, VTextField, etc.)
- Show both good and bad examples when explaining concepts

## LLM-Specific Guidance
- When discussing LLM integration, always mention validation and error handling
- Provide examples of prompt engineering for business analysis
- Explain how to handle dynamic JSONB schemas from LLM responses
- Show proper caching strategies for LLM responses

## Architecture Explanations
- Always explain how code fits into the overall Vertoie architecture
- Mention multi-schema database implications when relevant
- Explain Flutter component design for dynamic rendering
- Reference the vertoie_ui library patterns

## Task Management
- Remind about updating STATUS.md and TASKS.md when discussing task completion
- Break down complex tasks into smaller, manageable steps
- Provide clear acceptance criteria for each suggested solution
- Always mention testing requirements for new features

## Problem-Solving Approach
- Start by understanding the business context
- Consider how the solution works with LLM-generated modules
- Think about scalability across different business types
- Always consider security implications, especially with user-generated content

## Communication Style
- Be direct and specific - avoid vague suggestions
- Use Vertoie terminology consistently
- Provide concrete next steps
- Ask follow-up questions when context is missing