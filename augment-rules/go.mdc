---
description: 
globs: **/*.go
alwaysApply: false
---
# Go Backend Rules for Vertoie

Go version: 1.24

## Error Handling
- Always check and handle errors explicitly - never ignore them
- Use fmt.Errorf with %w verb for error wrapping
- Return errors as the last return value
- Provide meaningful error context

```go
// ✅ Good
func (s *llmService) AnalyzeBusiness(ctx context.Context, description string) (*BusinessAnalysis, error) {
    if description == "" {
        return nil, fmt.Errorf("business description cannot be empty")
    }
    
    response, err := s.llmClient.Generate(ctx, prompt)
    if err != nil {
        return nil, fmt.Errorf("failed to analyze business: %w", err)
    }
    
    return s.parseBusinessAnalysis(response)
}
```

## Context Usage
- Use context.Context for all operations with timeouts
- Pass context as the first parameter
- Handle context cancellation appropriately
- Set reasonable timeouts for LLM operations

## LLM Integration Standards
- Design interfaces for testability with LLM services
- Implement proper prompt engineering patterns
- Cache LLM responses to reduce costs and improve performance
- Validate and sanitize all LLM-generated content before use

## Database Operations
- Use JSONB for flexible schemas generated by LLMs
- Use UUIDs for primary keys
- Implement multi-schema design (vertoie schema + user schemas)
- All migrations must be wrapped in BEGIN...COMMIT transactions
- Use parameterized queries to prevent SQL injection

```go
// ✅ Good: Multi-schema database operations
func (s *userService) CreateUserSchema(ctx context.Context, userID uuid.UUID) error {
    tx, err := s.db.BeginTx(ctx, nil)
    if err != nil {
        return fmt.Errorf("failed to begin transaction: %w", err)
    }
    defer tx.Rollback()
    
    schemaName := fmt.Sprintf("user_%s", userID.String())
    query := fmt.Sprintf("CREATE SCHEMA IF NOT EXISTS %s", schemaName)
    
    if _, err := tx.ExecContext(ctx, query); err != nil {
        return fmt.Errorf("failed to create user schema: %w", err)
    }
    
    return tx.Commit()
}
```

## Interface Design
- Define interfaces for all services for testability
- Keep interfaces small and focused
- Use dependency injection patterns
- Mock interfaces for unit testing

## Performance Standards
- Implement connection pooling for database operations
- Use proper indexing for JSONB queries
- Implement rate limiting for LLM API calls
- Cache frequently accessed data

## Security Requirements
- Validate all inputs, especially from LLM responses
- Use proper authentication middleware
- Implement RBAC (role-based access control)
- Log security-relevant events for audit trails