{"folders": [{"name": "🏠 Root", "path": "."}, {"name": "🧩 Components", "path": "./components"}, {"name": "🚀 Platform Backend", "path": "./platform/backend"}, {"name": "📱 Platform Frontend", "path": "./platform/frontend"}, {"name": "🌐 Web Server", "path": "./web"}, {"name": "🎨 Brand Assets", "path": "./brand"}, {"name": "📚 Reference", "path": "./reference"}, {"name": "🔄 Migrations", "path": "./migrations"}], "settings": {"files.exclude": {"**/node_modules": true, "**/.git": true, "**/.DS_Store": true, "**/build": true, "**/dist": true, "**/.flutter-plugins": true, "**/.flutter-plugins-dependencies": true, "**/pubspec.lock": true, "**/tmp": true, "**/.direnv": true, "**/result": true, "**/result-*": true, "**/.go": true}, "search.exclude": {"**/node_modules": true, "**/build": true, "**/dist": true, "**/tmp": true, "**/.direnv": true, "**/result": true, "**/result-*": true, "**/.go": true}, "go.toolsManagement.checkForUpdates": "local", "go.useLanguageServer": true, "dart.flutterSdkPath": null, "dart.sdkPath": null, "nix.enableLanguageServer": true, "nix.serverPath": "nixd"}, "extensions": {"recommendations": ["golang.go", "dart-code.flutter", "dart-code.dart-code", "jnoortheen.nix-ide", "ms-vscode.vscode-json", "bradlc.vscode-tailwindcss"]}}