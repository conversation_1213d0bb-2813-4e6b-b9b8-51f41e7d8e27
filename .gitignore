# macOS
.DS_Store
.AppleDouble
.LSOverride

# VSCode
.vscode/

# JetBrains IDEs
.idea/
*.iml

# Nix
/result
/result-
*.drv
*.out
*.log
*.tmp
*.bak
*.swp

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
*.a
*.o
*.obj
*.cover
*.pb.go
vendor/
bin/

# Flutter/Dart
.dart_tool/
.packages
.pub-cache/
build/
.flutter-plugins
.flutter-plugins-dependencies
.flutter-plugins
.flutter-plugins-dependencies
pubspec.lock

# Node
node_modules/

# General temp files
*.tmp
*.temp
*.log
*.pid
*.seed
*.pid.lock

# System files
Thumbs.db
Desktop.ini

# Env files
.env
.env.*

# Project-specific
.go/
tmp/

# Ignore local database and cache
*.sqlite3
*.db
.cache/

# Ignore migration logs
migrations/*.log

.direnv/