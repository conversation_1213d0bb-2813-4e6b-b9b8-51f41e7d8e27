# GitHub Copilot Instructions for Vertoie

## Project Overview
Vertoie generates custom business software for any industry in minutes. AI analyzes your business and creates tailored modules, workflows, and processes instantly.

## Core Principles

### AI-First Development
- Build systems that AI can generate modules for
- Design composable, configurable components
- Use schema-driven approaches over hard-coding

### Industry-Agnostic Code
- Write code that works for any business type
- Handle variations through configuration, not separate codebases
- Think: "How does this work for pool service AND consulting AND landscaping?"

### Simple & Maintainable
- One responsibility per function
- Clear naming that explains purpose
- Suggest approaches before implementing

## Development Guidelines

```
// ✅ Good: Flexible and configurable
function generateModule(businessType, requirements) → customModule
function processWorkItem(item, businessConfig) → billingData

// ❌ Bad: Hard-coded for specific industry
function cleanPool() → poolReport
function hvacMaintenance() → hvacReport

// ✅ Good: Generic patterns
businessConfig = {
  workTypes: [...],
  requiredFields: [...],
  workflows: [...]
}

// ❌ Bad: Industry-specific logic
if (business === 'pool') { doPoolThing() }
else if (business === 'hvac') { doHvacThing() }
```

## When Asked for Help
- **Suggest architectural patterns** before coding
- **Ask: "Does this work for all business types?"**
- **Propose multiple approaches** with trade-offs
- **Question assumptions** that limit flexibility

## Remember
Main goal: **Any business gets custom software in minutes.**
Everything else (invoicing, voice, etc.) are just features that support this vision.