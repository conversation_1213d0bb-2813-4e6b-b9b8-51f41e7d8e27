#!/bin/bash

# Initialize Vertoie Development Database

echo "🗄️  Initializing Vertoie development database..."

# Create additional databases for testing
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Create test database
    CREATE DATABASE vertoie_test;
    
    -- Create core schema for business data
    CREATE SCHEMA IF NOT EXISTS business_core;
    
    -- Create sample business for development
    CREATE TABLE IF NOT EXISTS business_core.businesses (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        type VARCHAR(100) NOT NULL,
        owner_name VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Insert sample business
    INSERT INTO business_core.businesses (id, name, type, owner_name) 
    VALUES ('123e4567-e89b-12d3-a456-************', 'Pool Cleaning Pro', 'pool_service', 'John <PERSON>')
    ON CONFLICT (id) DO NOTHING;
    
    -- Create schema versions tracking
    CREATE TABLE IF NOT EXISTS business_core.schema_versions (
        business_id UUID REFERENCES business_core.businesses(id),
        module_name VARCHAR(100) NOT NULL,
        version INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT true,
        rollback_available BOOLEAN DEFAULT true,
        PRIMARY KEY (business_id, module_name, version)
    );
    
    -- Create sample versioned schema for pool service business
    CREATE SCHEMA IF NOT EXISTS business_123_pool_service_v1;
    
    -- Sample customers table in versioned schema
    CREATE TABLE IF NOT EXISTS business_123_pool_service_v1.customers (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        address TEXT,
        service_frequency VARCHAR(50) DEFAULT 'weekly',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Sample invoices table
    CREATE TABLE IF NOT EXISTS business_123_pool_service_v1.invoices (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        customer_id UUID REFERENCES business_123_pool_service_v1.customers(id),
        amount DECIMAL(10,2) NOT NULL,
        status VARCHAR(50) DEFAULT 'pending',
        service_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- Insert sample data
    INSERT INTO business_123_pool_service_v1.customers (id, name, email, address) VALUES
        ('456e7890-e89b-12d3-a456-************', 'Jane Smith', '<EMAIL>', '123 Main St'),
        ('456e7890-e89b-12d3-a456-************', 'Bob Johnson', '<EMAIL>', '456 Oak Ave')
    ON CONFLICT (id) DO NOTHING;
    
    INSERT INTO business_123_pool_service_v1.invoices (customer_id, amount, status, service_date) VALUES
        ('456e7890-e89b-12d3-a456-************', 150.00, 'paid', '2025-05-30'),
        ('456e7890-e89b-12d3-a456-************', 200.00, 'pending', '2025-05-31')
    ON CONFLICT (id) DO NOTHING;
    
    -- Insert schema version tracking
    INSERT INTO business_core.schema_versions (business_id, module_name, version) 
    VALUES ('123e4567-e89b-12d3-a456-************', 'pool_service', 1)
    ON CONFLICT (business_id, module_name, version) DO NOTHING;
    
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA business_core TO vertoie;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA business_123_pool_service_v1 TO vertoie;
    GRANT USAGE ON ALL SEQUENCES IN SCHEMA business_core TO vertoie;
    GRANT USAGE ON ALL SEQUENCES IN SCHEMA business_123_pool_service_v1 TO vertoie;
EOSQL

echo "✅ Database initialization complete!"
