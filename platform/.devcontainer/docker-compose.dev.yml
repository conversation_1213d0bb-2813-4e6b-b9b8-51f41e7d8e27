services:
  # Platform development service (Go + Flutter)
  vertoie-platform:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    volumes:
      - ..:/workspace:cached
      - /var/run/docker.sock:/var/run/docker.sock
      - go-modules:/go/pkg/mod
    working_dir: /workspace
    ports:
      - "8000:8000"
    environment:
      - GO_ENV=development
      - PORT=8000
      - DATABASE_URL=************************************************/vertoie_dev?sslmode=disable
      - REDIS_URL=redis://vertoie-redis:6379
    depends_on:
      - vertoie-postgres
      - vertoie-redis
    networks:
      - vertoie-dev
    command: sleep infinity

  # PostgreSQL database
  vertoie-postgres:
    image: postgres:17-alpine
    restart: unless-stopped
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    environment:
      POSTGRES_DB: vertoie_dev
      POSTGRES_USER: vertoie
      POSTGRES_PASSWORD: vertoie
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5433:5432"
    networks:
      - vertoie-dev
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U vertoie -d vertoie_dev"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis cache
  vertoie-redis:
    image: redis:8-alpine
    restart: unless-stopped
    volumes:
      - redis-data:/data
    ports:
      - "6380:6379"
    networks:
      - vertoie-dev
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres-data:
  redis-data:
  go-modules:

networks:
  vertoie-dev:
    driver: bridge
