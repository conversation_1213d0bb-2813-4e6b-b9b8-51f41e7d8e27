FROM golang:1.24-bullseye

# Install system dependencies
RUN apt-get update && apt-get install -y \
  git \
  curl \
  wget \
  vim \
  nano \
  postgresql-client \
  redis-tools \
  jq \
  unzip \
  && rm -rf /var/lib/apt/lists/*

# Install Air for hot reloading (moved to air-verse organization)
RUN go install github.com/air-verse/air@latest

# Install golangci-lint for linting
RUN curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.54.2

# Install goimports for formatting
RUN go install golang.org/x/tools/cmd/goimports@latest

# Install delve for debugging
RUN go install github.com/go-delve/delve/cmd/dlv@latest

# Install Docker CLI for Docker-in-Docker
RUN curl -fsSL https://get.docker.com | sh

# Set up Go environment
ENV GO111MODULE=on
ENV GOPROXY=https://proxy.golang.org,direct
ENV GOSUMDB=sum.golang.org

# Create workspace directory
WORKDIR /workspace

# Copy go mod files for dependency caching
COPY backend/go.mod backend/go.sum ./backend/
WORKDIR /workspace/backend
RUN go mod download

# Set working directory back to workspace root
WORKDIR /workspace

# Default command
CMD ["sleep", "infinity"]
