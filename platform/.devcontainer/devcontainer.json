{"name": "Vertoie Platform Development", "dockerComposeFile": "docker-compose.dev.yml", "service": "vertoie-platform", "workspaceFolder": "/workspace", "shutdownAction": "stopCompose", "features": {"ghcr.io/devcontainers/features/go:1": {"version": "1.24"}, "ghcr.io/devcontainers/features/docker-in-docker:2": {}, "ghcr.io/devcontainers/features/github-cli:1": {}}, "customizations": {"vscode": {"extensions": ["golang.go", "ms-vscode.vscode-html", "ms-vscode.vscode-css", "ms-vscode.vscode-javascript", "ms-vscode.vscode-typescript-next", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-azuretools.vscode-docker", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-markdown", "GitHub.copilot", "GitHub.copilot-chat"], "settings": {"go.useLanguageServer": true, "go.gopath": "/go", "go.goroot": "/usr/local/go", "go.toolsManagement.checkForUpdates": "local", "go.lintTool": "golangci-lint", "go.formatTool": "goimports", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "files.eol": "\n", "terminal.integrated.defaultProfile.linux": "bash", "terminal.integrated.cwd": "/workspace/backend", "task.autoDetect": "on", "task.quickOpen.history": 10}}}, "postCreateCommand": "cd /workspace/backend && go mod tidy && go install github.com/air-verse/air@latest", "postStartCommand": "cd /workspace/backend && nohup bash -c '/go/bin/air -c .air.toml &'", "mounts": ["source=${localWorkspaceFolder}/platform,target=/workspace/platform,type=bind,consistency=cached", "source=${localWorkspaceFolder}/brand,target=/workspace/brand,type=bind,consistency=cached", "source=${localWorkspaceFolder}/web,target=/workspace/web,type=bind,consistency=cached"], "forwardPorts": [8000, 5432, 6379], "portsAttributes": {"8000": {"label": "Go Backend API", "onAutoForward": "notify"}, "5432": {"label": "PostgreSQL", "onAutoForward": "silent"}, "6379": {"label": "Redis", "onAutoForward": "silent"}}, "containerEnv": {"GO_ENV": "development", "PORT": "8000", "DATABASE_URL": "************************************************/vertoie_dev?sslmode=disable", "REDIS_URL": "redis://vertoie-redis:6379"}}