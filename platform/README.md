# Vertoie Platform

This is the core Vertoie platform containing the backend API and mobile frontend.

## Project Structure

### 🚀 Backend (Go + FastHTTP)

- **Location**: `./backend/`
- **Tech Stack**: Go, FastHTTP
- **Purpose**: Internal and public REST APIs and WebSocket services

### 📱 Frontend (Flutter)

- **Location**: `./frontend/`
- **Tech Stack**: Flutter, Dart
- **Purpose**: Cross-platform mobile application (iOS & Android)

### 📚 Reference

- **Location**: `./reference/`
- **Purpose**: Architecture documentation and tracking docs

## Quick Start Commands

### Backend Development

```bash
cd backend
go mod tidy
# Install Air for hot reload: go install github.com/cosmtrek/air@latest
air
```

### Frontend Development

```bash
cd frontend
flutter create . --org com.vertoie
flutter pub get
flutter run
```

## Development Workflow

1. **Open Workspace**: Open `../vertoie.code-workspace` in VS Code
2. **Install Extensions**: Accept extension recommendations when prompted
3. **Start Development**: Use debug configurations or tasks to run projects

## VS Code Tasks Available

- **Go Backend**: Build, Run, Test
- **Flutter**: Get Dependencies, Run, Test, Build APK/iOS
- **Docker**: Start/Stop development services

## Environment Configuration

The platform is pre-configured for:

- ✅ Go development with proper formatting and linting
- ✅ Flutter development with hot reload
- ✅ Git integration with enhanced features
- ✅ DevContainer development environment

Happy coding! 🚀

#### 🐛 Debug Configurations

- **Go Backend**: Debug with breakpoints, attach to running process
- **Flutter**: Debug mobile app with hot reload
- **Full Stack**: Debug both backend and frontend simultaneously

#### 📦 Recommended Extensions

Auto-install prompts for essential extensions:

- Go development tools
- Flutter/Dart support
- Web development tools
- Git enhancements
- Productivity tools

## Quick Start Commands

### Backend Development

```bash
cd backend
go mod init vertoie-backend
go mod tidy
# Install Air for hot reload: go install github.com/cosmtrek/air@latest
air
```

### Frontend Development

```bash
cd frontend
flutter create . --org com.vertoie
flutter pub get
flutter run
```

### Quick Web Development

```bash
# Landing page
cd landing && python3 -m http.server 3000

# Marketing site
cd web && python3 -m http.server 3001
```

## Development Workflow

1. **Open Workspace**: Open `vertoie.code-workspace` in VS Code
2. **Install Extensions**: Accept extension recommendations when prompted
3. **Setup Dependencies**: Run "🏗️ Setup Project Dependencies" task
4. **Start Development**: Use debug configurations or tasks to run projects

## File Organization Tips

- Keep each project self-contained in its folder
- Use the `reference/` folder for shared documentation
- Brand assets in `colors/` and `logos/` are available to all projects
- VS Code will help you navigate between projects with the multi-root setup

## Environment Configuration

The workspace is pre-configured for:

- ✅ Go development with proper formatting and linting
- ✅ Flutter development with hot reload
- ✅ Web development with live serving
- ✅ Git integration with enhanced features
- ✅ Cross-project search and navigation

Happy coding! 🚀
