# Vertoie Workspace .gitignore

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE and Editor files
.vscode/settings.json
.idea/
*.swp
*.swo
*~

# Go specific
backend/bin/
backend/*.exe
backend/*.dll
backend/*.so
backend/*.dylib
backend/vendor/
backend/coverage.out
backend/.env
backend/tmp/

# Flutter/Dart specific
frontend/.dart_tool/
frontend/.flutter-plugins
frontend/.flutter-plugins-dependencies
frontend/.packages
frontend/.pub-cache/
frontend/.pub/
frontend/build/
frontend/ios/Flutter/Generated.xcconfig
frontend/ios/Flutter/flutter_export_environment.sh
frontend/ios/Runner/GeneratedPluginRegistrant.*
frontend/android/app/debug
frontend/android/app/profile
frontend/android/app/release
frontend/.metadata

# Node.js (if used in web projects)
web/node_modules/
web/npm-debug.log*
web/yarn-debug.log*
web/yarn-error.log*
web/dist/
web/build/

landing/node_modules/
landing/npm-debug.log*
landing/dist/
landing/build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/

# Documentation build
docs/_build/

# Test coverage
.nyc_output/

# Air (Go hot reload) temporary files
backend/tmp/
