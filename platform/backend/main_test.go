package main

import (
	"testing"
	"time"

	"github.com/google/uuid"
)

func TestMessage(t *testing.T) {
	msg := &Message{
		ID:        uuid.New().String(),
		Channel:   "test-channel",
		Type:      "test.message",
		Payload:   map[string]interface{}{"test": "data"},
		Timestamp: time.Now(),
	}

	if msg.ID == "" {
		t.<PERSON>rror("Expected message ID to be set")
	}

	if msg.Channel != "test-channel" {
		t.<PERSON>rror("Expected channel to be set correctly")
	}

	if msg.Type != "test.message" {
		t.<PERSON>rror("Expected message type to be set correctly")
	}
}

func TestChannelManager(t *testing.T) {
	cm := &ChannelManager{
		channels: make(map[string]*Channel),
	}

	// Test channel creation
	channel := cm.CreateChannel("test-type", "conn-123")
	if channel == nil {
		t.Error("Expected channel to be created")
	}

	if channel.Type != "test-type" {
		t.<PERSON>rror("Expected channel type to be set correctly")
	}

	if channel.ConnID != "conn-123" {
		t.<PERSON>rror("Expected connection ID to be set correctly")
	}

	// Test channel exists in manager
	if _, exists := cm.channels[channel.ID]; !exists {
		t.Error("Expected channel to exist in manager")
	}
}

func TestConnectionManager(t *testing.T) {
	channelMgr := &ChannelManager{
		channels: make(map[string]*Channel),
	}

	cm := &ConnectionManager{
		connections: make(map[string]*WebSocketContext),
		channels:    channelMgr,
	}

	// Test connection registration would require a real websocket.Conn
	// For now, just test the structure
	if cm.connections == nil {
		t.Error("Expected connections map to be initialized")
	}

	if cm.channels == nil {
		t.Error("Expected channels manager to be set")
	}
}

func TestRouteMatching(t *testing.T) {
	tests := []struct {
		pattern string
		key     string
		matches bool
	}{
		{"test.message", "test.message", true},
		{"test.*", "test.message", true},
		{"test.*", "test.command", true},
		{"test.*", "other.message", false},
		{"exact", "exact", true},
		{"exact", "not-exact", false},
	}

	for _, test := range tests {
		result := matchPattern(test.pattern, test.key)
		if result != test.matches {
			t.Errorf("Pattern '%s' with key '%s': expected %v, got %v", 
				test.pattern, test.key, test.matches, result)
		}
	}
}

func TestVoiceCommandSimulation(t *testing.T) {
	result := processVoiceCommandSimulation("Bill John for 3 hours", "business-123")
	if result.Action != "create_invoice" {
		t.Errorf("Expected action 'create_invoice', got '%s'", result.Action)
	}

	if result.Confidence <= 0 {
		t.Error("Expected confidence to be positive")
	}

	// Test scheduling command
	result = processVoiceCommandSimulation("Schedule appointment with Sarah", "business-123")
	if result.Action != "schedule_appointment" {
		t.Errorf("Expected action 'schedule_appointment', got '%s'", result.Action)
	}

	// Test general command
	result = processVoiceCommandSimulation("Random command", "business-123")
	if result.Action != "general_query" {
		t.Errorf("Expected action 'general_query', got '%s'", result.Action)
	}
}