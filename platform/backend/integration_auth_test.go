package main

import (
	"encoding/json"
	"log"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"github.com/valyala/fasthttp"
	"github.com/vertoie/models"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// AuthIntegrationTestSuite contains all integration tests for authentication
type AuthIntegrationTestSuite struct {
	suite.Suite
	testDB *gorm.DB
	server *fasthttp.Server
}

// SetupSuite runs once before all tests in the suite
func (suite *AuthIntegrationTestSuite) SetupSuite() {
	// Set up test database connection
	testDatabaseURL := getEnv("DATABASE_TEST_URL", "postgres://vertoie:vertoie@localhost:5432/vertoie_test?sslmode=disable")

	db, err := gorm.Open(postgres.Open(testDatabaseURL), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // Silent for tests
	})
	require.NoError(suite.T(), err, "Failed to connect to test database")

	suite.testDB = db

	// Set global DB for handlers to use
	DB = db

	log.Printf("✅ Connected to test database")
}

// SetupTest runs before each individual test
func (suite *AuthIntegrationTestSuite) SetupTest() {
	// Clean up test data before each test
	suite.cleanupTestData()
}

// TearDownTest runs after each individual test
func (suite *AuthIntegrationTestSuite) TearDownTest() {
	// Clean up test data after each test
	suite.cleanupTestData()
}

// TearDownSuite runs once after all tests in the suite
func (suite *AuthIntegrationTestSuite) TearDownSuite() {
	if suite.testDB != nil {
		sqlDB, _ := suite.testDB.DB()
		sqlDB.Close()
	}
}

// cleanupTestData removes all test data from relevant tables
func (suite *AuthIntegrationTestSuite) cleanupTestData() {
	// Delete in order to respect foreign key constraints
	suite.testDB.Exec("DELETE FROM vertoie.organization_addon_subscriptions WHERE created_at > NOW() - INTERVAL '1 hour'")
	suite.testDB.Exec("DELETE FROM vertoie.organization_subscriptions WHERE created_at > NOW() - INTERVAL '1 hour'")
	suite.testDB.Exec("DELETE FROM vertoie.organization_users WHERE created_at > NOW() - INTERVAL '1 hour'")
	suite.testDB.Exec("DELETE FROM vertoie.auth_tokens WHERE created_at > NOW() - INTERVAL '1 hour'")
	suite.testDB.Exec("DELETE FROM vertoie.users WHERE created_at > NOW() - INTERVAL '1 hour'")
	suite.testDB.Exec("DELETE FROM vertoie.organizations WHERE created_at > NOW() - INTERVAL '1 hour'")
	suite.testDB.Exec("DELETE FROM vertoie.plan_addons WHERE created_at > NOW() - INTERVAL '1 hour'")
	suite.testDB.Exec("DELETE FROM vertoie.plans WHERE created_at > NOW() - INTERVAL '1 hour'")
}

// Helper function to make HTTP requests
func (suite *AuthIntegrationTestSuite) makeRequest(method, path string, body interface{}) (*fasthttp.Response, error) {
	req := fasthttp.AcquireRequest()
	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(req)

	req.SetRequestURI("http://localhost:8000" + path)
	req.Header.SetMethod(method)
	req.Header.SetContentType("application/json")

	if body != nil {
		bodyBytes, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		req.SetBody(bodyBytes)
	}

	// Use the HTTP router directly instead of making actual HTTP calls
	ctx := &fasthttp.RequestCtx{}
	req.CopyTo(&ctx.Request)

	// Route the request
	switch path {
	case "/auth/signup":
		HandleSignup(ctx)
	case "/auth/login":
		HandleLogin(ctx)
	case "/auth/verify":
		HandleVerifyToken(ctx)
	case "/auth/invite":
		HandleInviteUser(ctx)
	default:
		ctx.SetStatusCode(fasthttp.StatusNotFound)
	}

	// Copy response
	resp.SetStatusCode(ctx.Response.StatusCode())
	resp.SetBody(ctx.Response.Body())
	ctx.Response.Header.VisitAll(func(key, value []byte) {
		resp.Header.SetBytesKV(key, value)
	})

	return resp, nil
}

// Helper function to create a test user
func (suite *AuthIntegrationTestSuite) createTestUser(email string) *models.User {
	user := &models.User{
		Email:         email,
		FirstName:     "Test",
		LastName:      "User",
		EmailVerified: true,
		Settings:      models.GetDefaultUserSettings(),
	}
	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()
	now := time.Now()
	user.EmailVerifiedAt = &now
	// Set an initial last login time (1 hour ago) so we can test updates
	lastLogin := time.Now().Add(-1 * time.Hour)
	user.LastLoginAt = &lastLogin

	err := suite.testDB.Create(user).Error
	require.NoError(suite.T(), err)

	return user
}

// Helper function to create a test organization
func (suite *AuthIntegrationTestSuite) createTestOrganization(name, slug string) *models.Organization {
	org := &models.Organization{
		Name:     name,
		Slug:     slug,
		Status:   models.StatusActive,
		Settings: models.GetDefaultOrganizationSettings(),
	}
	org.CreatedAt = time.Now()
	org.UpdatedAt = time.Now()

	err := suite.testDB.Create(org).Error
	require.NoError(suite.T(), err)

	return org
}

// Helper function to create a test plan
func (suite *AuthIntegrationTestSuite) createTestPlan(name, slug string, priceCents int) *models.Plan {
	plan := &models.Plan{
		Name:         name,
		Slug:         slug,
		Description:  nil,
		PriceCents:   priceCents,
		BillingCycle: "monthly",
		IsActive:     true,
		Features: models.VersionedSettings{
			Version: 1,
			Data: models.JSONB{
				"modules":        10,
				"users":          5,
				"ai_generations": 100,
				"storage_gb":     10,
			},
		},
	}
	plan.CreatedAt = time.Now()
	plan.UpdatedAt = time.Now()

	err := suite.testDB.Create(plan).Error
	require.NoError(suite.T(), err)

	return plan
}

// Helper function to create a test addon
func (suite *AuthIntegrationTestSuite) createTestAddon(name, slug, addonType string, quantity, priceCents int) *models.PlanAddon {
	addon := &models.PlanAddon{
		Name:         name,
		Slug:         slug,
		Description:  nil,
		AddonType:    addonType,
		Quantity:     quantity,
		PriceCents:   priceCents,
		BillingCycle: "monthly",
		IsActive:     true,
	}
	addon.CreatedAt = time.Now()
	addon.UpdatedAt = time.Now()

	err := suite.testDB.Create(addon).Error
	require.NoError(suite.T(), err)

	return addon
}

// Test that login fails for non-existing users (new behavior)
func (suite *AuthIntegrationTestSuite) TestLoginFailsForNonExistingUser() {
	// Test data
	email := "<EMAIL>"

	// Step 1: Try to request magic link for non-existing user
	loginReq := LoginRequest{Email: email}
	resp, err := suite.makeRequest("POST", "/auth/login", loginReq)
	require.NoError(suite.T(), err)

	// Verify response - should fail
	assert.Equal(suite.T(), fasthttp.StatusUnauthorized, resp.StatusCode())

	var errorResp map[string]interface{}
	err = json.Unmarshal(resp.Body(), &errorResp)
	require.NoError(suite.T(), err)
	assert.False(suite.T(), errorResp["success"].(bool))
	assert.Contains(suite.T(), errorResp["error"].(string), "User not found")

	// Verify no token was created in database
	var tokenCount int64
	suite.testDB.Model(&models.AuthToken{}).Where("email = ?", email).Count(&tokenCount)
	assert.Equal(suite.T(), int64(0), tokenCount)
}

// Test login flow for existing user
func (suite *AuthIntegrationTestSuite) TestLoginFlow() {
	// Create existing user
	email := "<EMAIL>"
	existingUser := suite.createTestUser(email)

	// Step 1: Request magic link (login)
	loginReq := LoginRequest{Email: email}
	resp, err := suite.makeRequest("POST", "/auth/login", loginReq)
	require.NoError(suite.T(), err)

	// Verify response
	assert.Equal(suite.T(), fasthttp.StatusOK, resp.StatusCode())

	// Verify token was created with user ID
	var token models.AuthToken
	err = suite.testDB.Where("email = ? AND type = ?", email, models.TokenTypeMagicLink).First(&token).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), email, token.Email)
	assert.NotNil(suite.T(), token.UserID)
	assert.Equal(suite.T(), existingUser.ID, *token.UserID)

	// Step 2: Verify magic link token (complete login)
	verifyReq := VerifyTokenRequest{Token: token.Token}
	resp, err = suite.makeRequest("POST", "/auth/verify", verifyReq)
	require.NoError(suite.T(), err)

	// Verify response
	assert.Equal(suite.T(), fasthttp.StatusOK, resp.StatusCode())

	var verifyResp VerifyTokenResponse
	err = json.Unmarshal(resp.Body(), &verifyResp)
	require.NoError(suite.T(), err)
	assert.True(suite.T(), verifyResp.Success)
	assert.Equal(suite.T(), existingUser.ID, verifyResp.User.ID)

	// Verify user's last login was updated
	var updatedUser models.User
	err = suite.testDB.Where("id = ?", existingUser.ID).First(&updatedUser).Error
	require.NoError(suite.T(), err)
	assert.True(suite.T(), updatedUser.LastLoginAt.After(*existingUser.LastLoginAt))
}

// Test email verification edge cases
func (suite *AuthIntegrationTestSuite) TestEmailVerificationEdgeCases() {
	// Test invalid email format
	loginReq := LoginRequest{Email: "invalid-email"}
	resp, err := suite.makeRequest("POST", "/auth/login", loginReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusBadRequest, resp.StatusCode())

	// Test empty email
	loginReq = LoginRequest{Email: ""}
	resp, err = suite.makeRequest("POST", "/auth/login", loginReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusBadRequest, resp.StatusCode())

	// Test invalid token
	verifyReq := VerifyTokenRequest{Token: "invalid-token"}
	resp, err = suite.makeRequest("POST", "/auth/verify", verifyReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusUnauthorized, resp.StatusCode())

	// Test empty token
	verifyReq = VerifyTokenRequest{Token: ""}
	resp, err = suite.makeRequest("POST", "/auth/verify", verifyReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusBadRequest, resp.StatusCode())
}

// Test expired token
func (suite *AuthIntegrationTestSuite) TestExpiredToken() {
	email := "<EMAIL>"

	// Create an expired token manually
	expiredToken := &models.AuthToken{
		Token:     "expired-token-12345",
		TokenType: models.TokenTypeMagicLink,
		Email:     email,
		ExpiresAt: time.Now().Add(-1 * time.Hour), // Expired 1 hour ago
		Settings:  models.VersionedSettings{Version: 1, Data: models.JSONB{}},
	}
	expiredToken.CreatedAt = time.Now().Add(-2 * time.Hour)
	expiredToken.UpdatedAt = time.Now().Add(-2 * time.Hour)

	err := suite.testDB.Create(expiredToken).Error
	require.NoError(suite.T(), err)

	// Try to verify expired token
	verifyReq := VerifyTokenRequest{Token: expiredToken.Token}
	resp, err := suite.makeRequest("POST", "/auth/verify", verifyReq)
	require.NoError(suite.T(), err)

	assert.Equal(suite.T(), fasthttp.StatusUnauthorized, resp.StatusCode())
}

// Test already used token
func (suite *AuthIntegrationTestSuite) TestAlreadyUsedToken() {
	email := "<EMAIL>"

	// Create a used token manually
	usedToken := &models.AuthToken{
		Token:     "used-token-12345",
		TokenType: models.TokenTypeMagicLink,
		Email:     email,
		ExpiresAt: time.Now().Add(15 * time.Minute),
		UsedAt:    &[]time.Time{time.Now().Add(-5 * time.Minute)}[0], // Used 5 minutes ago
		Settings:  models.VersionedSettings{Version: 1, Data: models.JSONB{}},
	}
	usedToken.CreatedAt = time.Now().Add(-10 * time.Minute)
	usedToken.UpdatedAt = time.Now().Add(-10 * time.Minute)

	err := suite.testDB.Create(usedToken).Error
	require.NoError(suite.T(), err)

	// Try to verify already used token
	verifyReq := VerifyTokenRequest{Token: usedToken.Token}
	resp, err := suite.makeRequest("POST", "/auth/verify", verifyReq)
	require.NoError(suite.T(), err)

	assert.Equal(suite.T(), fasthttp.StatusUnauthorized, resp.StatusCode())
}

// Test organization invitation flow
func (suite *AuthIntegrationTestSuite) TestOrganizationInvitationFlow() {
	// Create test organization
	org := suite.createTestOrganization("Test Company", "test-company")

	// Test data
	email := "<EMAIL>"
	role := "member"

	// Step 1: Send invitation
	inviteReq := InviteUserRequest{
		Email:          email,
		OrganizationID: org.ID,
		Role:           role,
	}
	resp, err := suite.makeRequest("POST", "/auth/invite", inviteReq)
	require.NoError(suite.T(), err)

	// Verify response
	assert.Equal(suite.T(), fasthttp.StatusOK, resp.StatusCode())

	// Verify invitation token was created
	var token models.AuthToken
	err = suite.testDB.Where("email = ? AND type = ? AND organization_id = ?",
		email, models.TokenTypeInvitation, org.ID).First(&token).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), email, token.Email)
	assert.Equal(suite.T(), org.ID, *token.OrganizationID)
	assert.False(suite.T(), token.IsExpired())

	// Step 2: Accept invitation (verify token)
	verifyReq := VerifyTokenRequest{Token: token.Token}
	resp, err = suite.makeRequest("POST", "/auth/verify", verifyReq)
	require.NoError(suite.T(), err)

	// Verify response
	assert.Equal(suite.T(), fasthttp.StatusOK, resp.StatusCode())

	var verifyResp VerifyTokenResponse
	err = json.Unmarshal(resp.Body(), &verifyResp)
	require.NoError(suite.T(), err)
	assert.True(suite.T(), verifyResp.Success)
	assert.NotNil(suite.T(), verifyResp.User)
	assert.Equal(suite.T(), email, verifyResp.User.Email)

	// Verify user was created
	var user models.User
	err = suite.testDB.Where("email = ?", email).First(&user).Error
	require.NoError(suite.T(), err)

	// Verify organization membership was created
	var membership models.OrganizationUser
	err = suite.testDB.Where("user_id = ? AND organization_id = ?", user.ID, org.ID).First(&membership).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), models.UserRole(role), membership.Role)
	assert.NotNil(suite.T(), membership.JoinedAt)
}

// Test invitation validation
func (suite *AuthIntegrationTestSuite) TestInvitationValidation() {
	org := suite.createTestOrganization("Test Company", "test-company")

	// Test invalid email
	inviteReq := InviteUserRequest{
		Email:          "invalid-email",
		OrganizationID: org.ID,
		Role:           "member",
	}
	resp, err := suite.makeRequest("POST", "/auth/invite", inviteReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusBadRequest, resp.StatusCode())

	// Test invalid role
	inviteReq = InviteUserRequest{
		Email:          "<EMAIL>",
		OrganizationID: org.ID,
		Role:           "invalid-role",
	}
	resp, err = suite.makeRequest("POST", "/auth/invite", inviteReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusBadRequest, resp.StatusCode())

	// Test empty organization ID
	inviteReq = InviteUserRequest{
		Email:          "<EMAIL>",
		OrganizationID: "",
		Role:           "member",
	}
	resp, err = suite.makeRequest("POST", "/auth/invite", inviteReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusBadRequest, resp.StatusCode())
}

// Test email normalization
func (suite *AuthIntegrationTestSuite) TestEmailNormalization() {
	// Test with uppercase and whitespace
	email := "  <EMAIL>  "
	normalizedEmail := "<EMAIL>"

	// Create user with normalized email first
	suite.createTestUser(normalizedEmail)

	// Request magic link with non-normalized email
	loginReq := LoginRequest{Email: email}
	resp, err := suite.makeRequest("POST", "/auth/login", loginReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusOK, resp.StatusCode())

	// Verify token was created with normalized email
	var token models.AuthToken
	err = suite.testDB.Where("email = ?", normalizedEmail).First(&token).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), normalizedEmail, token.Email)
}

// Test proper signup flow
func (suite *AuthIntegrationTestSuite) TestSignupFlow() {
	// Create test plan and addons
	plan := suite.createTestPlan("Starter Plan", "starter", 2999)                       // $29.99
	addon1 := suite.createTestAddon("Extra User", "extra-user", "users", 1, 999)        // $9.99 for 1 user
	addon2 := suite.createTestAddon("AI Boost", "ai-boost", "ai_generations", 50, 1999) // $19.99 for 50 generations

	// Test data
	signupReq := SignupRequest{
		Email:                   "<EMAIL>",
		FirstName:               "John",
		LastName:                "Doe",
		OrganizationName:        "Acme Corp",
		OrganizationSlug:        "acme-corp",
		OrganizationDescription: stringPtr("A test company"),
		OrganizationWebsite:     stringPtr("https://acme.com"),
		OrganizationIndustry:    stringPtr("Technology"),
		OrganizationSize:        stringPtr("10-50"),
		OrganizationCountry:     stringPtr("US"),
		OrganizationTimezone:    stringPtr("America/New_York"),
		PlanID:                  plan.ID,
		AddonIDs:                []string{addon1.ID, addon2.ID},
	}

	// Step 1: Submit signup request
	resp, err := suite.makeRequest("POST", "/auth/signup", signupReq)
	require.NoError(suite.T(), err)

	// Verify response
	assert.Equal(suite.T(), fasthttp.StatusCreated, resp.StatusCode())

	var signupResp SignupResponse
	err = json.Unmarshal(resp.Body(), &signupResp)
	require.NoError(suite.T(), err)
	assert.True(suite.T(), signupResp.Success)
	assert.Contains(suite.T(), signupResp.Message, "Account created successfully")
	assert.NotNil(suite.T(), signupResp.User)
	assert.NotNil(suite.T(), signupResp.Organization)

	// Verify user was created correctly
	assert.Equal(suite.T(), "<EMAIL>", signupResp.User.Email)
	assert.Equal(suite.T(), "John", signupResp.User.FirstName)
	assert.Equal(suite.T(), "Doe", signupResp.User.LastName)
	assert.False(suite.T(), signupResp.User.EmailVerified) // Should not be verified yet

	// Verify organization was created correctly
	assert.Equal(suite.T(), "Acme Corp", signupResp.Organization.Name)
	assert.Equal(suite.T(), "acme-corp", signupResp.Organization.Slug)
	assert.Equal(suite.T(), "A test company", *signupResp.Organization.Description)
	assert.Equal(suite.T(), "https://acme.com", *signupResp.Organization.Website)
	assert.Equal(suite.T(), models.StatusActive, signupResp.Organization.Status)

	// Verify user exists in database
	var user models.User
	err = suite.testDB.Where("email = ?", "<EMAIL>").First(&user).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), signupResp.User.ID, user.ID)

	// Verify organization exists in database
	var org models.Organization
	err = suite.testDB.Where("slug = ?", "acme-corp").First(&org).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), signupResp.Organization.ID, org.ID)

	// Verify organization membership was created (user is owner)
	var membership models.OrganizationUser
	err = suite.testDB.Where("user_id = ? AND organization_id = ?", user.ID, org.ID).First(&membership).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), models.RoleOwner, membership.Role)
	assert.NotNil(suite.T(), membership.JoinedAt)

	// Verify plan subscription was created
	var subscription models.OrganizationSubscription
	err = suite.testDB.Where("organization_id = ? AND plan_id = ?", org.ID, plan.ID).First(&subscription).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), "active", subscription.Status)

	// Verify addon subscriptions were created
	var addonSubscriptions []models.OrganizationAddonSubscription
	err = suite.testDB.Where("organization_id = ?", org.ID).Find(&addonSubscriptions).Error
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), addonSubscriptions, 2)

	// Check that both addons are subscribed
	addonIDs := make([]string, len(addonSubscriptions))
	for i, sub := range addonSubscriptions {
		addonIDs[i] = sub.AddonID
		assert.Equal(suite.T(), "active", sub.Status)
		assert.Equal(suite.T(), 1, sub.Quantity) // Default quantity
	}
	assert.Contains(suite.T(), addonIDs, addon1.ID)
	assert.Contains(suite.T(), addonIDs, addon2.ID)

	// Step 2: Verify that login fails before email verification
	loginReq := LoginRequest{Email: "<EMAIL>"}
	resp, err = suite.makeRequest("POST", "/auth/login", loginReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusUnauthorized, resp.StatusCode()) // Should fail because email not verified
}

// Test signup validation
func (suite *AuthIntegrationTestSuite) TestSignupValidation() {
	plan := suite.createTestPlan("Test Plan", "test", 0)

	// Test missing email
	signupReq := SignupRequest{
		FirstName:        "John",
		LastName:         "Doe",
		OrganizationName: "Test Corp",
		OrganizationSlug: "test-corp",
		PlanID:           plan.ID,
	}
	resp, err := suite.makeRequest("POST", "/auth/signup", signupReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusBadRequest, resp.StatusCode())

	// Test invalid email format
	signupReq.Email = "invalid-email"
	resp, err = suite.makeRequest("POST", "/auth/signup", signupReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusBadRequest, resp.StatusCode())

	// Test missing organization name
	signupReq.Email = "<EMAIL>"
	signupReq.OrganizationName = ""
	resp, err = suite.makeRequest("POST", "/auth/signup", signupReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusBadRequest, resp.StatusCode())

	// Test invalid plan ID
	signupReq.OrganizationName = "Test Corp"
	signupReq.PlanID = "invalid-plan-id"
	resp, err = suite.makeRequest("POST", "/auth/signup", signupReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusBadRequest, resp.StatusCode())
}

// Test signup with duplicate email
func (suite *AuthIntegrationTestSuite) TestSignupDuplicateEmail() {
	plan := suite.createTestPlan("Test Plan", "test", 0)

	// Create existing user
	suite.createTestUser("<EMAIL>")

	signupReq := SignupRequest{
		Email:            "<EMAIL>",
		FirstName:        "John",
		LastName:         "Doe",
		OrganizationName: "Test Corp",
		OrganizationSlug: "test-corp",
		PlanID:           plan.ID,
	}

	resp, err := suite.makeRequest("POST", "/auth/signup", signupReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusConflict, resp.StatusCode())

	var errorResp map[string]interface{}
	err = json.Unmarshal(resp.Body(), &errorResp)
	require.NoError(suite.T(), err)
	assert.Contains(suite.T(), errorResp["error"].(string), "already exists")
}

// Test signup with duplicate organization slug
func (suite *AuthIntegrationTestSuite) TestSignupDuplicateOrgSlug() {
	plan := suite.createTestPlan("Test Plan", "test", 0)

	// Create existing organization
	suite.createTestOrganization("Existing Corp", "existing-corp")

	signupReq := SignupRequest{
		Email:            "<EMAIL>",
		FirstName:        "John",
		LastName:         "Doe",
		OrganizationName: "New Corp",
		OrganizationSlug: "existing-corp", // Duplicate slug
		PlanID:           plan.ID,
	}

	resp, err := suite.makeRequest("POST", "/auth/signup", signupReq)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), fasthttp.StatusConflict, resp.StatusCode())

	var errorResp map[string]interface{}
	err = json.Unmarshal(resp.Body(), &errorResp)
	require.NoError(suite.T(), err)
	assert.Contains(suite.T(), errorResp["error"].(string), "slug is already taken")
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}

// Run the test suite
func TestAuthIntegrationSuite(t *testing.T) {
	// Skip if no test database is available
	testDatabaseURL := getEnv("DATABASE_TEST_URL", "")
	if testDatabaseURL == "" {
		t.Skip("DATABASE_TEST_URL not set, skipping integration tests")
	}

	suite.Run(t, new(AuthIntegrationTestSuite))
}
