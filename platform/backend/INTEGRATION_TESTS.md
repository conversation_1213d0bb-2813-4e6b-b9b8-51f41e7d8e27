# Integration Tests for Vertoie Backend

This document describes the comprehensive integration test suite for the Vertoie backend authentication system.

## Overview

The integration test suite validates the complete authentication flow from HTTP requests through database operations, ensuring that all components work together correctly.

## Test Coverage

### ✅ Authentication Flows (8 test cases)

1. **User Signup Flow** (`TestSignupFlow`)
   - Request magic link for new user
   - Verify token creation in database
   - Complete signup via token verification
   - Validate user creation and email verification

2. **User Login Flow** (`TestLoginFlow`)
   - Request magic link for existing user
   - Verify token creation with user ID
   - Complete login via token verification
   - Validate last login time update

3. **Email Verification Edge Cases** (`TestEmailVerificationEdgeCases`)
   - Invalid email format validation
   - Empty email validation
   - Invalid token handling
   - Empty token handling

4. **Token Expiration** (`TestExpiredToken`)
   - Create expired token
   - Verify rejection of expired tokens

5. **Token Reuse Prevention** (`TestAlreadyUsedToken`)
   - Create used token
   - Verify rejection of already used tokens

6. **Organization Invitation Flow** (`TestOrganizationInvitationFlow`)
   - Send organization invitation
   - Verify invitation token creation
   - Accept invitation via token verification
   - Validate user creation and organization membership

7. **Invitation Validation** (`TestInvitationValidation`)
   - Invalid email format in invitations
   - Invalid role validation
   - Missing organization ID validation

8. **Email Normalization** (`TestEmailNormalization`)
   - Uppercase and whitespace handling
   - Consistent email storage format

## Running the Tests

### Quick Start

```bash
# Run all integration tests
./run_integration_tests.sh
```

### Manual Execution

```bash
# Set test database URL
export DATABASE_TEST_URL="************************************************/vertoie_test?sslmode=disable"

# Run specific test suite
go test -v ./... -run TestAuthIntegrationSuite

# Run specific test case
go test -v ./... -run TestAuthIntegrationSuite/TestSignupFlow
```

## Test Infrastructure

### Database Setup

The tests use a dedicated test database (`vertoie_test`) with the same schema as the development database. The test suite:

- Automatically connects to the test database
- Runs migrations to ensure schema compatibility
- Cleans up test data between tests
- Uses transactions for data isolation

### Test Utilities

- **Database Cleanup**: Removes test data after each test
- **HTTP Request Simulation**: Direct handler invocation without network overhead
- **Test Data Factories**: Helper functions for creating test users and organizations
- **Assertion Helpers**: Comprehensive validation of responses and database state

### Schema Compatibility

The integration tests include a migration (`003_add_missing_auth_columns.sql`) that adds missing columns to align the database schema with the Go models:

- `auth_tokens.email` - Email address for token lookup
- `auth_tokens.organization_id` - Organization reference for invitations
- `users.email_verified` - Email verification status
- `users.last_login_at` - Last login timestamp
- `organizations.status` - Organization status field

## Test Results

All 8 integration tests pass consistently:

```
✅ TestAlreadyUsedToken
✅ TestEmailNormalization  
✅ TestEmailVerificationEdgeCases
✅ TestExpiredToken
✅ TestInvitationValidation
✅ TestLoginFlow
✅ TestOrganizationInvitationFlow
✅ TestSignupFlow
```

**Execution Time**: ~40ms for the complete suite

## Architecture Validation

These integration tests validate:

- **HTTP Layer**: FastHTTP request/response handling
- **Authentication Logic**: Magic link generation and verification
- **Database Layer**: GORM model operations and queries
- **Business Logic**: User creation, organization membership, token lifecycle
- **Data Integrity**: Proper foreign key relationships and constraints
- **Security**: Token expiration, reuse prevention, input validation

## Future Enhancements

Potential additions to the test suite:

- [ ] Concurrent user operations
- [ ] Rate limiting validation
- [ ] Email delivery simulation
- [ ] Performance benchmarking
- [ ] Multi-organization scenarios
- [ ] Role-based access control testing

## Troubleshooting

### Common Issues

1. **Database Connection**: Ensure PostgreSQL is running and accessible
2. **Schema Mismatch**: Run migrations if tests fail with column errors
3. **Port Conflicts**: Check that test database port (5432) is available
4. **Environment Variables**: Verify `DATABASE_TEST_URL` is set correctly

### Debug Mode

Add verbose logging to see detailed test execution:

```bash
go test -v ./... -run TestAuthIntegrationSuite -args -test.v
```

---

**Last Updated**: June 2025  
**Test Suite Version**: 1.0  
**Coverage**: 8/8 authentication flows ✅
