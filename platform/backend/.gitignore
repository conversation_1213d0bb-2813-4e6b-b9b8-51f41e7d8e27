# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build artifacts
bin/
tmp/
dist/
backend/

# Air live reloading
.air/

# Environment files
.env
.env.local
.env.development
.env.production
.env.test

# Logs
logs/
*.log

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker volumes
docker-data/

# Database files
*.db
*.sqlite
*.sqlite3

# Test coverage
coverage.out
coverage.html

# Certificates (development)
*.pem
*.key
*.crt

# Temporary files
*.tmp
*.temp