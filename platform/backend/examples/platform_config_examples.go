package examples

// Real-world examples of how platform_config helps Vertoie

import (
	"fmt"
	"log"
)

// Example 1: Dynamic AI Rate Limiting
func handleAIGenerationRequest(userID string, businessType string) error {
	// Get current rate limit from config (can be changed without deployment)
	maxPerHour := getPlatformConfig("max_ai_generations_per_hour", 10)

	if userExceedsRateLimit(userID, maxPerHour) {
		return fmt.Errorf("rate limit exceeded: %d generations per hour", maxPerHour)
	}

	// Check if AI generation is enabled globally
	if !getPlatformConfigBool("ai_generation_enabled", true) {
		return fmt.Errorf("AI generation temporarily disabled for maintenance")
	}

	return generateModule(businessType)
}

// Example 2: Feature Rollout Control
func checkFeatureAccess(userID string, feature string) bool {
	// Gradually roll out new features
	switch feature {
	case "voice_assistant":
		return getPlatformConfigBool("feature_voice_assistant", false)
	case "advanced_workflows":
		return getPlatformConfigBool("feature_advanced_workflows", false)
	case "ai_suggestions":
		// Could be percentage-based rollout
		rolloutPercent := getPlatformConfig("feature_ai_suggestions_rollout", 0)
		return userInRolloutGroup(userID, rolloutPercent)
	}
	return false
}

// Example 3: Business Plan Limits
func checkModuleLimits(organizationID string, planType string) (int, error) {
	// Get limits from config (easily adjustable)
	limitsConfig := getPlatformConfigJSON("max_modules_per_business")

	switch planType {
	case "starter":
		return limitsConfig["starter"].(int), nil
	case "professional":
		return limitsConfig["professional"].(int), nil
	case "enterprise":
		return -1, nil // unlimited
	}

	return 0, fmt.Errorf("unknown plan type: %s", planType)
}

// Example 4: Emergency Controls
func handleEmergencyShutdown() {
	// Instantly disable expensive operations without deployment
	updatePlatformConfig("ai_generation_enabled", false)
	updatePlatformConfig("bulk_operations_enabled", false)
	updatePlatformConfig("maintenance_mode", true)

	log.Println("Emergency shutdown activated - expensive operations disabled")
}

// Example 5: Market-Specific Configuration
func getRegionalSettings(country string) map[string]interface{} {
	// Different rules for different markets
	settings := make(map[string]interface{})

	if country == "EU" {
		settings["gdpr_mode"] = getPlatformConfigBool("eu_gdpr_strict_mode", true)
		settings["data_retention_days"] = getPlatformConfig("eu_data_retention_days", 365)
	} else {
		settings["gdpr_mode"] = false
		settings["data_retention_days"] = getPlatformConfig("default_data_retention_days", 2555) // 7 years
	}

	return settings
}

// Helper functions (would be implemented in your config service)
func getPlatformConfig(key string, defaultValue int) int {
	// Query platform_config table
	return defaultValue
}

func getPlatformConfigBool(key string, defaultValue bool) bool {
	// Query platform_config table
	return defaultValue
}

func getPlatformConfigJSON(key string) map[string]interface{} {
	// Query platform_config table and parse JSONB
	return make(map[string]interface{})
}

func updatePlatformConfig(key string, value interface{}) {
	// Update platform_config table
}

func userExceedsRateLimit(userID string, maxPerHour int) bool {
	// Check rate limiting logic
	return false
}

func generateModule(businessType string) error {
	// AI module generation logic
	return nil
}

func userInRolloutGroup(userID string, percentage int) bool {
	// Hash userID to determine if they're in rollout group
	return false
}
