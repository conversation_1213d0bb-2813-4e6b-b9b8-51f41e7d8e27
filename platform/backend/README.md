# Vertoie Backend

AI-driven business management platform backend built with Go, fasthttp, and WebSockets.

## 🚀 Quick Start

You have **two development options**:

### Option 1: Full Docker Development (Recommended)

Everything runs in Docker containers - no local Go installation needed.

```bash
# Start full Docker development environment
./dev-docker.sh start

# Open in VS Code and reopen in container
# VS Code will prompt: "Reopen in Container"

# Inside container terminal, start hot reload
air -c .air.toml
```

**Benefits**: Consistent environment, no local dependencies, easy cleanup

### Option 2: Local Development

Backend runs locally, services in Docker.

```bash
# Setup development environment (installs Go tools locally)
./dev.sh setup

# Start services (PostgreSQL, Redis)
./dev.sh start

# Run backend with hot reload
./dev.sh dev
```

**Requirements**: Go 1.24+, Docker, Air installed locally

## 📝 Development Scripts

### Docker Development (`./dev-docker.sh`)

Manages the full Docker-based development environment:

```bash
./dev-docker.sh start          # Start all services
./dev-docker.sh stop           # Stop all services
./dev-docker.sh status         # Show service status
./dev-docker.sh logs [service] # Show logs
./dev-docker.sh exec bash      # Open shell in container
./dev-docker.sh exec 'air -c .air.toml'  # Start hot reload
./dev-docker.sh test           # Run tests in container
./dev-docker.sh clean          # Remove everything
```

### Local Development (`./dev.sh`)

For running backend locally with Docker services:

```bash
./dev.sh setup       # Install Go tools locally
./dev.sh start       # Start Docker services only
./dev.sh dev         # Run backend with hot reload
./dev.sh test        # Run tests locally
./dev.sh build       # Build binary locally
./dev.sh stop        # Stop Docker services
```

## 🔧 VS Code Tasks

Use VS Code's task runner (Cmd+Shift+P → "Tasks: Run Task"):

- **🚀 Build Go Backend** - Build the application
- **🏃 Run Go Backend (Hot Reload)** - Start with Air hot reload
- **🧪 Test Go Backend** - Run test suite
- **🔧 Go Mod Tidy** - Clean up dependencies

## 📁 Project Structure

```
backend/
├── main.go                 # Application entry point
├── websocket_router.go     # WebSocket routing system
├── websocket_handlers.go   # WebSocket message handlers
├── http_router.go          # REST API endpoints
├── main_test.go           # Unit tests
├── .air.toml              # Hot reload configuration
├── .env.development       # Development environment
├── tmp/                   # Air hot reload build artifacts
├── .devcontainer/         # VS Code dev container config
│   ├── Dockerfile
│   ├── devcontainer.json
│   └── docker-compose.dev.yml
├── dev.sh                 # Local development scripts
└── dev-docker.sh          # Docker development scripts
```

### `/tmp` Folder Purpose

The `/tmp` folder is used by Air (hot reload tool) to store compiled binaries during development:

- **Air builds** your Go application to `./tmp/main`
- **Automatically restarts** when you modify source files
- **Keeps workspace clean** by separating build artifacts
- **Git ignored** to prevent committing temporary files

This enables instant feedback during development without cluttering your workspace.

## 🏗️ Architecture

Our backend follows the architecture described in `BackendArchitecture.md`:

- **Language**: Go 1.21+
- **HTTP Framework**: fasthttp (high performance)
- **WebSockets**: Real-time communication with channel multiplexing
- **Database**: PostgreSQL with schema versioning
- **Cache**: Redis
- **Security**: mTLS (development uses simplified auth)

### WebSocket Message Format

```json
{
  "id": "uuid-correlation-id",
  "channel": "voice|ai-gen-{id}|invoice-{id}|business-sync|notifications",
  "type": "voice.command.process|ai.module.generate|invoice.create",
  "payload": { "command": "Bill John for 3 hours" },
  "timestamp": "2025-05-31T12:00:00Z"
}
```

### Supported Message Types

- **Voice Commands**: `voice.command.process`, `voice.command.result`
- **AI Module Generation**: `ai.module.generate`, `ai.module.progress`, `ai.module.complete`
- **Invoice Operations**: `invoice.create`, `invoice.update`, `invoice.subscribe`
- **Business Sync**: `business.sync`, `business.sync.complete`
- **Notifications**: `notification.*`
- **Connection Management**: `connection.ping`, `connection.pong`, `connection.subscribe`

## 🛠️ Development Commands

```bash
# Development Scripts (all commands via ./dev.sh)
./dev.sh setup          # Initial setup
./dev.sh start          # Start services (PostgreSQL, Redis, pgAdmin, etc.)
./dev.sh stop           # Stop all services
./dev.sh dev            # Run with hot reload (uses Air)
./dev.sh build          # Build binary
./dev.sh test           # Run tests
./dev.sh lint           # Run linting
./dev.sh format         # Format code
./dev.sh clean          # Clean build artifacts
./dev.sh logs [service] # View logs
./dev.sh status         # Service status
./dev.sh db:reset       # Reset database
./dev.sh help           # Show all commands
```

## 🌐 Development URLs

When running in development mode:

- **Backend API**: http://localhost:8000
- **WebSocket**: ws://localhost:8000/ws
- **Health Check**: http://localhost:8000/health
- **PostgreSQL**: postgresql://vertoie:vertoie@localhost:5432/vertoie_dev
- **Redis**: redis://localhost:6379

## 📡 API Endpoints

### REST API

```bash
# Health and Info
GET /health
GET /api

# Authentication (placeholder)
POST /api/v1/auth/login
POST /api/v1/auth/refresh
POST /api/v1/auth/logout

# Business
GET /api/v1/business/profile
PUT /api/v1/business/profile
GET /api/v1/business/modules

# Modules
GET /api/v1/modules
POST /api/v1/modules/generate

# Invoices
GET /api/v1/invoices
POST /api/v1/invoices

# Voice
POST /api/v1/voice/process
GET /api/v1/voice/commands
```

### WebSocket Examples

```bash
# Connect to WebSocket
ws://localhost:8000/ws?business_id=dev-business-123&user_id=dev-user-456

# Send voice command
{
  "id": "cmd-001",
  "channel": "voice",
  "type": "voice.command.process",
  "payload": { "command": "Bill John for 3 hours of pool cleaning" }
}

# Start AI module generation
{
  "id": "gen-001",
  "channel": "main",
  "type": "ai.module.generate",
  "payload": { "business_type": "pool_service" }
}
```

## 🗄️ Database Schema

The database uses a versioned schema approach:

```sql
-- Core business data
business_core.businesses
business_core.schema_versions

-- Versioned business modules (example)
business_123_pool_service_v1.customers
business_123_pool_service_v1.invoices
```

### Sample Data

Development environment includes:

- Sample business: "Pool Cleaning Pro"
- Sample customers: Jane Smith, Bob Johnson
- Sample invoices with different statuses

## 🔧 Configuration

Environment variables (see `.env.development`):

```bash
# Core
GO_ENV=development
PORT=8000

# Database
DATABASE_URL=postgres://vertoie:vertoie@localhost:5432/vertoie_dev?sslmode=disable
REDIS_URL=redis://localhost:6379

# Development
HOT_RELOAD=true
DEBUG_MODE=true
LOG_LEVEL=debug
```

## 🧪 Testing

```bash
# Run all tests
nix run .#backend-test

# Run specific test
go test ./handlers -v

# Run with coverage
go test ./... -cover
```

## 📝 Code Organization

```
backend/
├── main.go                 # Application entry point
├── websocket_router.go     # WebSocket routing system
├── websocket_handlers.go   # WebSocket message handlers
├── http_router.go          # REST API router
├── .devcontainer/          # Development container config
│   ├── devcontainer.json
│   ├── docker-compose.dev.yml
│   ├── Dockerfile
│   └── init-db/
├── .air.toml              # Hot reload configuration
├── database.go            # Database connection and models
├── models/                # Data models
├── migrations/            # Database migrations
└── go.mod                 # Go dependencies
```

## 🐳 DevContainer Features

The DevContainer provides:

- **Go 1.21+** with all development tools
- **Docker-in-Docker** for container workflows
- **PostgreSQL + Redis** with admin interfaces
- **VS Code Extensions**: Go, Docker, GitHub Copilot
- **Hot Reload** with Air
- **Debugging** with Delve
- **Linting** with golangci-lint

## 🔄 Multi-Root Workspace

This backend works seamlessly with the multi-root Vertoie workspace:

1. **Backend DevContainer**: Isolated Go development environment
2. **Frontend Access**: Connect to backend via exposed ports
3. **Shared Resources**: Common configuration in workspace root
4. **Independent Development**: Each component can be developed separately

## 📊 Technical Architecture

### Framework Choice: fasthttp

Chosen for superior performance over net/http:

- Zero-allocation request handling
- Built-in connection pooling
- Memory-efficient request/response handling
- 10x faster than standard net/http in benchmarks

### WebSocket Architecture

- **Channel Multiplexing**: Multiple logical channels over single connection
- **Message Correlation**: UUID-based request/response tracking
- **Subscription Model**: Channel-based pub/sub for real-time updates
- **Connection Pooling**: Efficient connection management and cleanup

### Data Layer Design

- **Versioned Schemas**: Each business module gets its own versioned schema
- **Dynamic Tables**: Tables created on-demand for new business types
- **Core + Module Pattern**: Shared core tables + business-specific modules
- **Schema Migration**: Automated versioning and migration system

### Security Model (Production)

- **mTLS Authentication**: Certificate-based client authentication
- **Token Validation**: JWT tokens for session management
- **Rate Limiting**: Per-connection and per-business-module limits
- **Audit Logging**: Comprehensive security event logging

## 🚧 Development Status

### ✅ Completed Features

- ✅ Complete Go backend project structure
- ✅ FastHTTP server with WebSocket support
- ✅ Custom WebSocket routing with channel multiplexing
- ✅ REST API router with sample endpoints
- ✅ Unit test suite (all tests passing)
- ✅ DevContainer environment
- ✅ Docker Compose development stack
- ✅ Air hot-reload configuration
- ✅ Development workflow automation
- ✅ Port migration from 8080 to 8000
- ✅ Air installation completed

### 🔄 Current Priority Tasks

- [ ] Database models and ORM integration (GORM)
- [ ] mTLS authentication system implementation
- [ ] AI model integration (OpenAI/Local LLM)
- [ ] Real voice command processing
- [ ] Production Docker configuration
- [ ] Comprehensive integration testing

### 🎯 Future Enhancements

- [ ] Kubernetes deployment manifests
- [ ] API documentation with OpenAPI/Swagger
- [ ] Performance monitoring and metrics
- [ ] Distributed caching strategies
- [ ] Load balancing configuration

## 🤝 Contributing

1. Make changes in the DevContainer environment
2. Use `nix run .#format` before committing
3. Ensure `nix run .#backend-test` passes
4. Run `nix run .#backend-lint` to check code quality

## 📚 Documentation

This README contains all the essential information for backend development. Additional documentation will be added as needed:

- API Documentation (TODO) - OpenAPI/Swagger specifications
- WebSocket Protocol (TODO) - Detailed message format specifications
- Deployment Guide (TODO) - Production deployment instructions
