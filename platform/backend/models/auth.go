package models

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"
)

// TokenType represents the type of authentication token
type TokenType string

const (
	TokenTypeMagicLink     TokenType = "magic_link"
	TokenTypeInvitation    TokenType = "invitation"
	TokenTypePasswordReset TokenType = "password_reset"
	TokenTypeAPIKey        TokenType = "api_key"
	TokenTypeRefresh       TokenType = "refresh"
)

// AuthToken represents an authentication token for passwordless login
type AuthToken struct {
	BaseModel
	TokenHash      string     `json:"token_hash" gorm:"column:token_hash;uniqueIndex;size:255" db:"token_hash"`
	Type           TokenType  `json:"type" gorm:"column:type;type:varchar(50);not null" db:"type"`
	UserID         *string    `json:"user_id,omitempty" gorm:"type:uuid;index" db:"user_id"`
	Email          string     `json:"email" gorm:"size:255;not null;index" db:"email"`
	OrganizationID *string    `json:"organization_id,omitempty" gorm:"type:uuid;index" db:"organization_id"`
	ExpiresAt      time.Time  `json:"expires_at" gorm:"not null;index" db:"expires_at"`
	UsedAt         *time.Time `json:"used_at,omitempty" gorm:"index" db:"used_at"`
	IPAddress      *string    `json:"ip_address,omitempty" gorm:"size:45" db:"ip_address"`
	UserAgent      *string    `json:"user_agent,omitempty" gorm:"size:500" db:"user_agent"`
	Metadata       JSONB      `json:"metadata" gorm:"type:jsonb" db:"metadata"`
}

// TableName specifies the table name for AuthToken model in vertoie schema
func (AuthToken) TableName() string {
	return "vertoie.auth_tokens"
}

// LoginToken is an alias for AuthToken for backward compatibility
type LoginToken = AuthToken

// Plan represents a pricing plan/tier
type Plan struct {
	BaseModel
	Name         string  `json:"name" db:"name"`
	Slug         string  `json:"slug" db:"slug"`
	Description  *string `json:"description,omitempty" db:"description"`
	PriceCents   int     `json:"price_cents" db:"price_cents"`
	BillingCycle string  `json:"billing_cycle" db:"billing_cycle"` // monthly, yearly, custom
	IsActive     bool    `json:"is_active" db:"is_active"`
	Features     JSONB   `json:"features" db:"features"`
}

// TableName specifies the table name for Plan model in vertoie schema
func (Plan) TableName() string {
	return "vertoie.plans"
}

// PlanAddon represents an add-on product that can be purchased in addition to a base plan
type PlanAddon struct {
	BaseModel
	Name         string  `json:"name" db:"name"`
	Slug         string  `json:"slug" db:"slug"`
	Description  *string `json:"description,omitempty" db:"description"`
	AddonType    string  `json:"addon_type" db:"addon_type"` // users, ai_generations, storage
	Quantity     int     `json:"quantity" db:"quantity"`     // Amount provided by this addon
	PriceCents   int     `json:"price_cents" db:"price_cents"`
	BillingCycle string  `json:"billing_cycle" db:"billing_cycle"` // monthly, yearly
	IsActive     bool    `json:"is_active" db:"is_active"`
}

// TableName specifies the table name for PlanAddon model in vertoie schema
func (PlanAddon) TableName() string {
	return "vertoie.plan_addons"
}

// OrganizationSubscription represents an organization's subscription to a plan
type OrganizationSubscription struct {
	BaseModel
	OrganizationID         string     `json:"organization_id" db:"organization_id"`
	PlanID                 string     `json:"plan_id" db:"plan_id"`
	Status                 string     `json:"status" db:"status"` // active, past_due, canceled, paused
	StartsAt               time.Time  `json:"starts_at" db:"starts_at"`
	EndsAt                 *time.Time `json:"ends_at,omitempty" db:"ends_at"`
	ExternalSubscriptionID *string    `json:"external_subscription_id,omitempty" db:"external_subscription_id"`
	Metadata               JSONB      `json:"metadata" db:"metadata"`
}

// TableName specifies the table name for OrganizationSubscription model in vertoie schema
func (OrganizationSubscription) TableName() string {
	return "vertoie.organization_subscriptions"
}

// OrganizationAddonSubscription represents an organization's subscription to an add-on
type OrganizationAddonSubscription struct {
	BaseModel
	OrganizationID string     `json:"organization_id" db:"organization_id"`
	AddonID        string     `json:"addon_id" db:"addon_id"`
	Quantity       int        `json:"quantity" db:"quantity"` // How many of this addon
	Status         string     `json:"status" db:"status"`     // active, canceled
	StartsAt       time.Time  `json:"starts_at" db:"starts_at"`
	EndsAt         *time.Time `json:"ends_at,omitempty" db:"ends_at"`
	Metadata       JSONB      `json:"metadata" db:"metadata"`
}

// TableName specifies the table name for OrganizationAddonSubscription model in vertoie schema
func (OrganizationAddonSubscription) TableName() string {
	return "vertoie.organization_addon_subscriptions"
}

// Activity represents an audit log entry
type Activity struct {
	BaseModel
	UserID         *string `json:"user_id,omitempty" db:"user_id"`
	OrganizationID *string `json:"organization_id,omitempty" db:"organization_id"`
	Action         string  `json:"action" db:"action"`
	ResourceType   string  `json:"resource_type" db:"resource_type"`
	ResourceID     *string `json:"resource_id,omitempty" db:"resource_id"`
	IPAddress      *string `json:"ip_address,omitempty" db:"ip_address"`
	UserAgent      *string `json:"user_agent,omitempty" db:"user_agent"`
	Metadata       JSONB   `json:"metadata" db:"metadata"`
}

// TableName specifies the table name for Activity model in vertoie schema
func (Activity) TableName() string {
	return "vertoie.activities"
}

// PlatformConfig represents platform-wide configuration
type PlatformConfig struct {
	Key         string    `json:"key" db:"key"`
	Value       JSONB     `json:"value" db:"value"`
	Description *string   `json:"description,omitempty" db:"description"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// TableName specifies the table name for PlatformConfig model in vertoie schema
func (PlatformConfig) TableName() string {
	return "vertoie.platform_config"
}

// GetConfigString safely retrieves a string configuration value
func (pc *PlatformConfig) GetConfigString() string {
	if pc.Value == nil {
		return ""
	}
	// Handle direct string values stored in JSONB
	if len(pc.Value) == 1 {
		for _, v := range pc.Value {
			if str, ok := v.(string); ok {
				return str
			}
		}
	}
	return ""
}

// GetFeatureInt safely retrieves an integer feature value from plan features
func (p *Plan) GetFeatureInt(key string) int {
	if p.Features == nil {
		return 0
	}
	if val, exists := p.Features[key]; exists {
		switch v := val.(type) {
		case int:
			return v
		case float64:
			return int(v)
		}
	}
	return 0
}

// GetFeatureBool safely retrieves a boolean feature value from plan features
func (p *Plan) GetFeatureBool(key string) bool {
	if p.Features == nil {
		return false
	}
	if val, exists := p.Features[key]; exists {
		if b, ok := val.(bool); ok {
			return b
		}
	}
	return false
}

// IsUnlimited checks if a feature is unlimited (-1 value)
func (p *Plan) IsUnlimited(key string) bool {
	return p.GetFeatureInt(key) == -1
}

// GetAddonTypeDisplay returns a human-readable version of the addon type
func (pa *PlanAddon) GetAddonTypeDisplay() string {
	switch pa.AddonType {
	case "users":
		return "Additional Users"
	case "ai_generations":
		return "AI Generations"
	case "storage":
		return "Storage"
	default:
		return pa.AddonType
	}
}

// GetQuantityDisplay returns a formatted display of the addon quantity
func (pa *PlanAddon) GetQuantityDisplay() string {
	switch pa.AddonType {
	case "users":
		if pa.Quantity == 1 {
			return "1 User"
		}
		return fmt.Sprintf("%d Users", pa.Quantity)
	case "ai_generations":
		return fmt.Sprintf("%d Generations", pa.Quantity)
	case "storage":
		if pa.Quantity < 1024 {
			return fmt.Sprintf("%d GB", pa.Quantity)
		}
		return fmt.Sprintf("%.1f TB", float64(pa.Quantity)/1024)
	default:
		return fmt.Sprintf("%d", pa.Quantity)
	}
}

// GetPriceDisplay returns a formatted price display
func (p *Plan) GetPriceDisplay() string {
	if p.PriceCents == 0 {
		return "Free"
	}
	return fmt.Sprintf("$%.2f", float64(p.PriceCents)/100)
}

// GetPriceDisplay returns a formatted price display for add-ons
func (pa *PlanAddon) GetPriceDisplay() string {
	return fmt.Sprintf("$%.2f", float64(pa.PriceCents)/100)
}

// IsActive checks if the subscription is currently active
func (os *OrganizationSubscription) IsActive() bool {
	return os.Status == "active" && (os.EndsAt == nil || time.Now().Before(*os.EndsAt))
}

// IsActive checks if the addon subscription is currently active
func (oas *OrganizationAddonSubscription) IsActive() bool {
	return oas.Status == "active" && (oas.EndsAt == nil || time.Now().Before(*oas.EndsAt))
}

// GetTotalQuantity calculates total quantity across multiple addon subscriptions
func GetTotalAddonQuantity(subscriptions []OrganizationAddonSubscription) int {
	total := 0
	for _, sub := range subscriptions {
		if sub.IsActive() {
			total += sub.Quantity
		}
	}
	return total
}

// GetConfigBool safely retrieves a boolean configuration value
func (pc *PlatformConfig) GetConfigBool() bool {
	if pc.Value == nil {
		return false
	}
	// Handle direct boolean values stored in JSONB
	if len(pc.Value) == 1 {
		for _, v := range pc.Value {
			if b, ok := v.(bool); ok {
				return b
			}
		}
	}
	return false
}

// GetConfigInt safely retrieves an integer configuration value
func (pc *PlatformConfig) GetConfigInt() int {
	if pc.Value == nil {
		return 0
	}
	// Handle direct numeric values stored in JSONB
	if len(pc.Value) == 1 {
		for _, v := range pc.Value {
			switch val := v.(type) {
			case int:
				return val
			case float64:
				return int(val)
			}
		}
	}
	return 0
}

// GenerateSecureToken generates a cryptographically secure random token
func GenerateSecureToken(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// CreateMagicLinkToken creates a new magic link token for passwordless login
func CreateMagicLinkToken(email string, userID *string) (*AuthToken, error) {
	token, err := GenerateSecureToken(32) // 64 character hex string
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %v", err)
	}

	return &AuthToken{
		BaseModel: BaseModel{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		TokenHash: token,
		Type:      TokenTypeMagicLink,
		UserID:    userID,
		Email:     email,
		ExpiresAt: time.Now().Add(15 * time.Minute), // Magic links expire in 15 minutes
		Metadata:  JSONB{},
	}, nil
}

// CreateInvitationToken creates a new invitation token for organization membership
func CreateInvitationToken(email, organizationID string) (*AuthToken, error) {
	token, err := GenerateSecureToken(32)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %v", err)
	}

	return &AuthToken{
		BaseModel: BaseModel{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		TokenHash:      token,
		Type:           TokenTypeInvitation,
		Email:          email,
		OrganizationID: &organizationID,
		ExpiresAt:      time.Now().Add(7 * 24 * time.Hour), // Invitations expire in 7 days
		Metadata:       JSONB{},
	}, nil
}

// IsExpired checks if the token has expired
func (t *AuthToken) IsExpired() bool {
	return time.Now().After(t.ExpiresAt)
}

// IsUsed checks if the token has been used
func (t *AuthToken) IsUsed() bool {
	return t.UsedAt != nil
}

// MarkAsUsed marks the token as used
func (t *AuthToken) MarkAsUsed(ipAddress, userAgent string) {
	now := time.Now()
	t.UsedAt = &now
	t.IPAddress = &ipAddress
	t.UserAgent = &userAgent
	t.UpdatedAt = now
}
