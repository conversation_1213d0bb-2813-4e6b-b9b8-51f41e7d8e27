package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// Base model with common fields for all entities
type BaseModel struct {
	ID        string     `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()" db:"id"`
	CreatedAt time.Time  `json:"created_at" gorm:"autoCreateTime" db:"created_at"`
	UpdatedAt time.Time  `json:"updated_at" gorm:"autoUpdateTime" db:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty" gorm:"index" db:"deleted_at"`
}

// JSONB type for handling PostgreSQL JSONB columns
type JSONB map[string]interface{}

// Value implements the driver.Valuer interface for JSONB
func (j JSONB) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// <PERSON>an implements the sql.Scanner interface for JSONB
func (j *JSONB) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSONB", value)
	}

	return json.Unmarshal(bytes, j)
}

// VersionedSettings represents JSONB settings with schema versioning
type VersionedSettings struct {
	Version int   `json:"version"`
	Data    JSONB `json:"data"`
}

// Value implements the driver.Valuer interface for VersionedSettings
func (vs VersionedSettings) Value() (driver.Value, error) {
	return json.Marshal(vs)
}

// Scan implements the sql.Scanner interface for VersionedSettings
func (vs *VersionedSettings) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into VersionedSettings", value)
	}

	return json.Unmarshal(bytes, vs)
}

// GetString safely retrieves a string value from JSONB
func (j JSONB) GetString(key string) string {
	if val, ok := j[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

// GetBool safely retrieves a boolean value from JSONB
func (j JSONB) GetBool(key string) bool {
	if val, ok := j[key]; ok {
		if b, ok := val.(bool); ok {
			return b
		}
	}
	return false
}

// GetInt safely retrieves an integer value from JSONB
func (j JSONB) GetInt(key string) int {
	if val, ok := j[key]; ok {
		switch v := val.(type) {
		case int:
			return v
		case float64:
			return int(v)
		}
	}
	return 0
}

// GetFloat safely retrieves a float value from JSONB
func (j JSONB) GetFloat(key string) float64 {
	if val, ok := j[key]; ok {
		if f, ok := val.(float64); ok {
			return f
		}
	}
	return 0.0
}

// SetValue safely sets a value in JSONB
func (j JSONB) SetValue(key string, value interface{}) {
	if j == nil {
		j = make(JSONB)
	}
	j[key] = value
}
