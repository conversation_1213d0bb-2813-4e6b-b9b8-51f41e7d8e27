package models

import (
	"time"
)

// UserRole represents possible roles in organization membership
type UserRole string

const (
	RoleOwner  UserRole = "owner"
	RoleAdmin  UserRole = "admin"
	RoleMember UserRole = "member"
)

// Organization represents a business/company in the platform
type Organization struct {
	ID           string     `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()" db:"id"`
	Name         string     `json:"name" gorm:"size:255;not null" db:"name"`
	Slug         string     `json:"slug" gorm:"uniqueIndex;size:100;not null" db:"slug"`
	CustomDomain *string    `json:"custom_domain,omitempty" gorm:"uniqueIndex;size:255" db:"custom_domain"`
	Description  *string    `json:"description,omitempty" gorm:"type:text" db:"description"`
	CreatedAt    time.Time  `json:"created_at" gorm:"autoCreateTime" db:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at" gorm:"autoUpdateTime" db:"updated_at"`
	DeletedAt    *time.Time `json:"deleted_at,omitempty" gorm:"index" db:"deleted_at"`
}

// TableName specifies the table name for Organization model in vertoie schema
func (Organization) TableName() string {
	return "vertoie.organizations"
}

// OrganizationUser represents user membership in an organization
type OrganizationUser struct {
	ID             string    `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()" db:"id"`
	UserID         string    `json:"user_id" gorm:"type:uuid;not null;index;uniqueIndex:idx_user_org" db:"user_id"`
	OrganizationID string    `json:"organization_id" gorm:"type:uuid;not null;index;uniqueIndex:idx_user_org" db:"organization_id"`
	Role           UserRole  `json:"role" gorm:"type:varchar(50);not null;default:member" db:"role"`
	CreatedAt      time.Time `json:"created_at" gorm:"autoCreateTime" db:"created_at"`
	UpdatedAt      time.Time `json:"updated_at" gorm:"autoUpdateTime" db:"updated_at"`
}

// TableName specifies the table name for OrganizationUser model in vertoie schema
func (OrganizationUser) TableName() string {
	return "vertoie.organization_users"
}
