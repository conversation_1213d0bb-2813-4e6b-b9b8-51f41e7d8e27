package models

// Import shared models - no need to re-export, just use them directly
import (
	"time"

	_ "github.com/vertoie/models"
)

// User represents a business owner in the platform
type User struct {
	ID        string     `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()" db:"id"`
	Email     string     `json:"email" gorm:"uniqueIndex;size:255;not null" db:"email"`
	FirstName string     `json:"first_name" gorm:"size:100" db:"first_name"`
	LastName  string     `json:"last_name" gorm:"size:100" db:"last_name"`
	CreatedAt time.Time  `json:"created_at" gorm:"autoCreateTime" db:"created_at"`
	UpdatedAt time.Time  `json:"updated_at" gorm:"autoUpdateTime" db:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty" gorm:"index" db:"deleted_at"`
}

// TableName specifies the table name for User model in vertoie schema
func (User) TableName() string {
	return "vertoie.users"
}
