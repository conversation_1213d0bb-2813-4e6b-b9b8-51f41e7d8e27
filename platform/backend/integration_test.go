package main

import (
	"log"
	"testing"

	"github.com/vertoie/models"
)

// TestDatabaseIntegration tests that GORM models work with the vertoie schema
func TestDatabaseIntegration(t *testing.T) {
	// Initialize database connection
	if err := InitDatabase(); err != nil {
		t.Fatalf("Failed to initialize database: %v", err)
	}

	// Test User model - should query vertoie.users table
	var userCount int64
	err := DB.Model(&models.User{}).Count(&userCount).Error
	if err != nil {
		t.Fatalf("Failed to count users: %v", err)
	}
	log.Printf("✅ User model connected to vertoie.users - found %d users", userCount)

	// Test AuthToken model - should query vertoie.auth_tokens table
	var tokenCount int64
	err = DB.Model(&models.AuthToken{}).Count(&tokenCount).Error
	if err != nil {
		t.Fatalf("Failed to count auth tokens: %v", err)
	}
	log.Printf("✅ AuthToken model connected to vertoie.auth_tokens - found %d tokens", tokenCount)

	// Test Organization model - should query vertoie.organizations table
	var orgCount int64
	err = DB.Model(&models.Organization{}).Count(&orgCount).Error
	if err != nil {
		t.Fatalf("Failed to count organizations: %v", err)
	}
	log.Printf("✅ Organization model connected to vertoie.organizations - found %d organizations", orgCount)

	// Test Plan model - should query vertoie.plans table
	var planCount int64
	err = DB.Model(&models.Plan{}).Count(&planCount).Error
	if err != nil {
		t.Fatalf("Failed to count plans: %v", err)
	}
	log.Printf("✅ Plan model connected to vertoie.plans - found %d plans", planCount)

	t.Log("🎉 All GORM models successfully connected to vertoie schema!")
}
