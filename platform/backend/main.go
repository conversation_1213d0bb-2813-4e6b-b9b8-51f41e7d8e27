package main

import (
	"fmt"
	"log"
	"os"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/websocket/v2"
)

func main() {
	port := os.Getenv("PORT")
	if port == "" {
		port = "8000"
	}

	// Initialize database connection
	if err := InitDatabase(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Create Fiber app
	app := fiber.New(fiber.Config{
		AppName:      "Vertoie Backend",
		ServerHeader: "Vertoie",
	})

	// Add middleware
	app.Use(recover.New())
	app.Use(logger.New())
	app.Use(cors.New())

	// Health check endpoint
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":  "healthy",
			"service": "vertoie-backend",
			"version": "1.0.0",
		})
	})

	// API info endpoint
	app.Get("/api", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"name":        "Vertoie Backend API",
			"version":     "1.0.0",
			"description": "AI-driven business management platform",
		})
	})

	// Initialize handlers
	userHandler := NewUserHandler(DB)
	orgHandler := NewOrganizationHandler(DB)

	// API routes
	api := app.Group("/api")

	// User routes
	users := api.Group("/users")
	users.Post("/register", userHandler.Register)
	users.Post("/login", userHandler.Login)
	users.Get("/", userHandler.GetUsers)
	users.Get("/:id", userHandler.GetUser)
	users.Put("/:id", userHandler.UpdateUser)
	users.Delete("/:id", userHandler.DeleteUser)

	// Organization routes
	orgs := api.Group("/organizations")
	orgs.Post("/", orgHandler.CreateOrganization)
	orgs.Get("/", orgHandler.GetOrganizations)
	orgs.Get("/:id", orgHandler.GetOrganization)
	orgs.Put("/:id", orgHandler.UpdateOrganization)

	// WebSocket endpoint
	app.Use("/ws", websocket.New(handleWebSocket))

	fmt.Printf("🚀 Vertoie Backend starting on port %s\n", port)
	fmt.Printf("📡 WebSocket endpoint: ws://localhost:%s/ws\n", port)
	fmt.Printf("🌐 HTTP API endpoint: http://localhost:%s/api\n", port)
	fmt.Printf("👥 User API: http://localhost:%s/api/users\n", port)
	fmt.Printf("🏢 Organization API: http://localhost:%s/api/organizations\n", port)

	log.Fatal(app.Listen(":" + port))
}

// handleWebSocket handles WebSocket connections
func handleWebSocket(c *websocket.Conn) {
	defer c.Close()

	businessID := c.Query("business_id", "dev-business-123")
	userID := c.Query("user_id", "dev-user-456")

	fmt.Printf("📱 WebSocket connected: Business=%s, User=%s\n", businessID, userID)

	// Simple echo for now
	for {
		var msg map[string]interface{}
		if err := c.ReadJSON(&msg); err != nil {
			fmt.Printf("❌ WebSocket read error: %v\n", err)
			break
		}

		fmt.Printf("📨 Received message: %+v\n", msg)

		// Echo back
		response := map[string]interface{}{
			"echo":    true,
			"message": msg,
		}
		c.WriteJSON(response)
	}

	fmt.Printf("🔌 WebSocket disconnected\n")
}
