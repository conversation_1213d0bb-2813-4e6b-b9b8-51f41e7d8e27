package main

import (
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"github.com/vertoie/models"
	backendModels "github.com/vertoie/platform/backend/models"
	"gorm.io/gorm"
)

// UserHandler handles user-related API endpoints
type UserHandler struct {
	db *gorm.DB
}

// NewUserHandler creates a new user handler
func NewUserHandler(db *gorm.DB) *UserHandler {
	return &UserHandler{db: db}
}

// RegisterRequest represents a user registration request
type RegisterRequest struct {
	Email     string `json:"email" validate:"required,email"`
	FirstName string `json:"first_name" validate:"required"`
	LastName  string `json:"last_name" validate:"required"`
	Password  string `json:"password" validate:"required,min=8"`
}

// LoginRequest represents a user login request
type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

// Register handles user registration
func (h *UserHandler) Register(c *fiber.Ctx) error {
	var req RegisterRequest
	if err := c.<PERSON>er(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Check if user already exists
	var existingUser models.User
	if err := h.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"error": "User with this email already exists",
		})
	}

	// Create user (password hashing will be implemented later)
	user := models.User{
		ID:        uuid.New(),
		Email:     req.Email,
		FirstName: req.FirstName,
		LastName:  req.LastName,
	}

	if err := h.db.Create(&user).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create user",
		})
	}

	// Create auth token for magic link login
	userIDStr := user.ID.String()
	token, err := backendModels.CreateMagicLinkToken(req.Email, &userIDStr)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create login token",
		})
	}

	if err := h.db.Create(token).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to save login token",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"message": "User registered successfully",
		"user":    user,
		"token":   token.TokenHash, // In production, send this via email
	})
}

// Login handles user login
func (h *UserHandler) Login(c *fiber.Ctx) error {
	var req LoginRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Find user by email
	var user models.User
	if err := h.db.Where("email = ?", req.Email).First(&user).Error; err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "Invalid credentials",
		})
	}

	// For now, we'll use magic link tokens instead of password verification
	// In a real implementation, you'd verify the password here

	// Create new auth token
	userIDStr := user.ID.String()
	token, err := backendModels.CreateMagicLinkToken(req.Email, &userIDStr)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create login token",
		})
	}

	if err := h.db.Create(token).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to save login token",
		})
	}

	return c.JSON(fiber.Map{
		"message": "Login successful",
		"user":    user,
		"token":   token.TokenHash, // In production, send this via email
	})
}

// GetUsers returns a list of users
func (h *UserHandler) GetUsers(c *fiber.Ctx) error {
	var users []models.User

	// Add pagination
	page := c.QueryInt("page", 1)
	limit := c.QueryInt("limit", 10)
	offset := (page - 1) * limit

	if err := h.db.Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to fetch users",
		})
	}

	return c.JSON(fiber.Map{
		"users": users,
		"page":  page,
		"limit": limit,
	})
}

// GetUser returns a specific user by ID
func (h *UserHandler) GetUser(c *fiber.Ctx) error {
	userID := c.Params("id")

	var user models.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "User not found",
		})
	}

	return c.JSON(user)
}

// UpdateUser updates a user
func (h *UserHandler) UpdateUser(c *fiber.Ctx) error {
	userID := c.Params("id")

	var user models.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "User not found",
		})
	}

	var updateData map[string]interface{}
	if err := c.BodyParser(&updateData); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Update only allowed fields
	allowedFields := []string{"first_name", "last_name"}
	for _, field := range allowedFields {
		if value, exists := updateData[field]; exists {
			h.db.Model(&user).Update(field, value)
		}
	}

	return c.JSON(user)
}

// DeleteUser soft deletes a user
func (h *UserHandler) DeleteUser(c *fiber.Ctx) error {
	userID := c.Params("id")

	var user models.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "User not found",
		})
	}

	// Soft delete
	now := time.Now()
	if err := h.db.Model(&user).Update("deleted_at", now).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to delete user",
		})
	}

	return c.JSON(fiber.Map{
		"message": "User deleted successfully",
	})
}

// OrganizationHandler handles organization-related API endpoints
type OrganizationHandler struct {
	db *gorm.DB
}

// NewOrganizationHandler creates a new organization handler
func NewOrganizationHandler(db *gorm.DB) *OrganizationHandler {
	return &OrganizationHandler{db: db}
}

// CreateOrganizationRequest represents an organization creation request
type CreateOrganizationRequest struct {
	Name        string  `json:"name" validate:"required"`
	Slug        string  `json:"slug" validate:"required"`
	Description *string `json:"description"`
}

// CreateOrganization creates a new organization
func (h *OrganizationHandler) CreateOrganization(c *fiber.Ctx) error {
	var req CreateOrganizationRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Check if organization with slug already exists
	var existingOrg models.Organization
	if err := h.db.Where("slug = ?", req.Slug).First(&existingOrg).Error; err == nil {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"error": "Organization with this slug already exists",
		})
	}

	// Create organization
	description := ""
	if req.Description != nil {
		description = *req.Description
	}
	org := models.Organization{
		ID:          uuid.New(),
		Name:        req.Name,
		Slug:        req.Slug,
		Description: description,
	}

	if err := h.db.Create(&org).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create organization",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(org)
}

// GetOrganizations returns a list of organizations
func (h *OrganizationHandler) GetOrganizations(c *fiber.Ctx) error {
	var orgs []models.Organization

	// Add pagination
	page := c.QueryInt("page", 1)
	limit := c.QueryInt("limit", 10)
	offset := (page - 1) * limit

	if err := h.db.Offset(offset).Limit(limit).Find(&orgs).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to fetch organizations",
		})
	}

	return c.JSON(fiber.Map{
		"organizations": orgs,
		"page":          page,
		"limit":         limit,
	})
}

// GetOrganization returns a specific organization by ID
func (h *OrganizationHandler) GetOrganization(c *fiber.Ctx) error {
	orgID := c.Params("id")

	var org models.Organization
	if err := h.db.Where("id = ?", orgID).First(&org).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Organization not found",
		})
	}

	return c.JSON(org)
}

// UpdateOrganization updates an organization
func (h *OrganizationHandler) UpdateOrganization(c *fiber.Ctx) error {
	orgID := c.Params("id")

	var org models.Organization
	if err := h.db.Where("id = ?", orgID).First(&org).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Organization not found",
		})
	}

	var updateData map[string]interface{}
	if err := c.BodyParser(&updateData); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Update only allowed fields
	allowedFields := []string{"name", "description", "custom_domain"}
	for _, field := range allowedFields {
		if value, exists := updateData[field]; exists {
			h.db.Model(&org).Update(field, value)
		}
	}

	return c.JSON(org)
}
