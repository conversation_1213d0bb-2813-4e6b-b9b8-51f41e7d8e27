use flake

# Vertoie Development Environment Variables
export DATABASE_URL="postgres://codedmart:pass@127.0.0.1:5432/vertoie_dev?sslmode=disable"
export REDIS_URL="redis://127.0.0.1:6379"
export GO_ENV="development"
export PORT="8000"

# Web server specific (different port to avoid conflicts)
export WEB_PORT="8001"

# Test database
export DATABASE_TEST_URL="postgres://codedmart:pass@127.0.0.1:5432/vertoie_test?sslmode=disable"

# Mailgun configuration for email integration
export MAILGUN_DOMAIN="mail.vertoie.com"
export MAILGUN_API_KEY="**************************************************"
export MAILGUN_SENDER="<EMAIL>"

# Groq API configuration for LLM integration (<EMAIL>)
# export GROQ_API_KEY="********************************************************"

# Groq API configuration for LLM integration (<EMAIL>)
export GROQ_API_KEY="********************************************************"

# Google Gemini API configuration for LLM integration
# export GOOGLE_GEMINI_API_KEY="AIzaSyCxmtlqRAZbl27RGTMomAL-ZOXOc3Jn6bg"

# Go environment
export GOPATH="$PWD/.go"
export PATH="$GOPATH/bin:$PATH"
export GO111MODULE="on"
export CGO_ENABLED="1"

# Create necessary directories
mkdir -p .go/bin
mkdir -p tmp
