-- Migration 002 Down: Authorization System for Customer Applications
-- Rollback authorization system schema

BEGIN;

-- Drop indexes
DROP INDEX IF EXISTS vertoie.idx_auth_permission_audit_created_at;
DROP INDEX IF EXISTS vertoie.idx_auth_permission_audit_user_id;
DROP INDEX IF EXISTS vertoie.idx_auth_user_overrides_expires_at;
DROP INDEX IF EXISTS vertoie.idx_auth_user_overrides_module;
DROP INDEX IF EXISTS vertoie.idx_auth_user_overrides_user_id;
DROP INDEX IF EXISTS vertoie.idx_auth_user_roles_expires_at;
DROP INDEX IF EXISTS vertoie.idx_auth_user_roles_role_id;
DROP INDEX IF EXISTS vertoie.idx_auth_user_roles_user_id;
DROP INDEX IF EXISTS vertoie.idx_auth_role_permissions_module;
DROP INDEX IF EXISTS vertoie.idx_auth_role_permissions_role_id;
DROP INDEX IF EXISTS vertoie.idx_auth_roles_is_active;
DROP INDEX IF EXISTS vertoie.idx_auth_roles_is_template;
DROP INDEX IF EXISTS vertoie.idx_auth_roles_name;

-- Drop tables (in reverse order due to foreign key constraints)
DROP TABLE IF EXISTS vertoie.auth_permission_audit;
DROP TABLE IF EXISTS vertoie.auth_override_templates;
DROP TABLE IF EXISTS vertoie.auth_user_overrides;
DROP TABLE IF EXISTS vertoie.auth_user_roles;
DROP TABLE IF EXISTS vertoie.auth_role_permissions;
DROP TABLE IF EXISTS vertoie.auth_roles;

COMMIT;
