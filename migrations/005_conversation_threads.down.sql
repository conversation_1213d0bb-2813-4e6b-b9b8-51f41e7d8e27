BEGIN;

-- Rollback migration: Remove conversation threads system
-- This removes the thread_id columns and drops the conversation_threads table

-- Remove thread_id and token_count from conversations table
ALTER TABLE vertoie.organization_business_context_conversations 
DROP COLUMN IF EXISTS thread_id,
DROP COLUMN IF EXISTS token_count;

-- Drop the conversation_threads table (cascades will handle foreign key constraints)
DROP TABLE IF EXISTS vertoie.conversation_threads;

COMMIT; 