-- Migration 008: Clean up organization onboarding and add tier-based pricing
-- Remove messy onboarding fields and add proper app lifecycle management

BEGIN;

-- Clean up organization table
ALTER TABLE vertoie.organizations DROP COLUMN IF EXISTS onboarding_step;
ALTER TABLE vertoie.organizations DROP COLUMN IF EXISTS onboarding_complete;

-- Add proper app lifecycle management
ALTER TABLE vertoie.organizations 
ADD COLUMN app_status VARCHAR(20) DEFAULT 'setup', -- setup, draft, active, paused
ADD COLUMN current_version INTEGER DEFAULT 1;

-- Update recommended modules table for selection tracking
ALTER TABLE vertoie.organization_recommended_modules 
ADD COLUMN selected BOOLEAN DEFAULT FALSE,
ADD COLUMN is_custom BOOLEAN DEFAULT FALSE,
ADD COLUMN generation_prompt TEXT, -- What user asked for when custom module
ADD COLUMN ai_reasoning TEXT; -- AI's reasoning for the module structure

-- Create app versions table for schema versioning (using existing plans)
CREATE TABLE IF NOT EXISTS vertoie.organization_app_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES vertoie.organizations(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL DEFAULT 1,
    schema_definition JSONB NOT NULL, -- Complete schema snapshot
    selected_modules JSONB NOT NULL, -- Snapshot of selected modules
    plan_id UUID REFERENCES vertoie.plans(id), -- Use existing plans table
    is_active BOOLEAN DEFAULT FALSE,
    is_draft BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(organization_id, version_number)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_organization_app_status ON vertoie.organizations(app_status);
CREATE INDEX IF NOT EXISTS idx_recommended_modules_selected ON vertoie.organization_recommended_modules(organization_id, selected);
CREATE INDEX IF NOT EXISTS idx_app_versions_org_active ON vertoie.organization_app_versions(organization_id, is_active);

COMMIT; 