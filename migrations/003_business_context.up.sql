-- Migration 003: Business Context System
-- Add tables for storing business context and conversation history

BEGIN;

-- Business context for organizations
CREATE TABLE IF NOT EXISTS vertoie.organization_business_context (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES vertoie.organizations(id) ON DELETE CASCADE,
    
    -- Essential Information (Required for completion)
    business_type VARCHAR(255),
    industry VARCHAR(100),
    key_processes JSONB, -- Array of strings: ["scheduling", "invoicing", "inventory"]
    customer_types JSONB, -- Array of strings: ["residential", "commercial"]
    
    -- Helpful Additional Context (Optional)
    business_size VARCHAR(50), -- "solo_operator", "small_team", "medium_business"
    revenue_model VARCHAR(50), -- "service_based", "product_sales", "subscription", "mixed"
    geographic_scope VARCHAR(50), -- "local", "regional", "national", "online_only"
    key_pain_points JSONB, -- Array of strings: ["hard_to_track_appointments", "difficult_invoicing"]
    
    -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    -- Constraints
    UNIQUE(organization_id)
);

-- Business context conversation history
CREATE TABLE IF NOT EXISTS vertoie.organization_business_context_conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES vertoie.organizations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES vertoie.users(id),
    
    -- Conversation tracking
    conversation_session_id UUID NOT NULL, -- Groups related messages in a session
    message_role VARCHAR(20) NOT NULL, -- "user" or "assistant"
    message_content TEXT NOT NULL,
    message_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_business_context_org_id ON vertoie.organization_business_context(organization_id);
CREATE INDEX IF NOT EXISTS idx_business_context_deleted_at ON vertoie.organization_business_context(deleted_at);
CREATE INDEX IF NOT EXISTS idx_business_conversations_org_id ON vertoie.organization_business_context_conversations(organization_id);
CREATE INDEX IF NOT EXISTS idx_business_conversations_session ON vertoie.organization_business_context_conversations(conversation_session_id);
CREATE INDEX IF NOT EXISTS idx_business_conversations_timestamp ON vertoie.organization_business_context_conversations(organization_id, message_timestamp);

COMMIT; 