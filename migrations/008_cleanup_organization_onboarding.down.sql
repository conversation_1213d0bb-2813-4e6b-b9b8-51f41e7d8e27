-- Migration 008 Down: Rollback organization onboarding cleanup

BEGIN;

-- Drop new indexes
DROP INDEX IF EXISTS vertoie.idx_app_versions_org_active;
DROP INDEX IF EXISTS vertoie.idx_recommended_modules_selected;
DROP INDEX IF EXISTS vertoie.idx_organization_app_status;

-- Drop new tables
DROP TABLE IF EXISTS vertoie.organization_app_versions;

-- Remove new columns from recommended modules
ALTER TABLE vertoie.organization_recommended_modules 
DROP COLUMN IF EXISTS ai_reasoning,
DROP COLUMN IF EXISTS generation_prompt,
DROP COLUMN IF EXISTS is_custom,
DROP COLUMN IF EXISTS selected;

-- Remove new columns from organizations
ALTER TABLE vertoie.organizations 
DROP COLUMN IF EXISTS current_version,
DROP COLUMN IF EXISTS app_status;

-- Restore old onboarding columns
ALTER TABLE vertoie.organizations 
ADD COLUMN onboarding_step INTEGER DEFAULT 1,
ADD COLUMN onboarding_complete BOOLEAN DEFAULT FALSE;

COMMIT; 