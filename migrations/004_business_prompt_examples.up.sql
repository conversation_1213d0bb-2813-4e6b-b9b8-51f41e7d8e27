-- Migration: Add business_prompt_examples table
-- This table stores example prompts that can be fuzzy matched against business types

BEGIN;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE vertoie.business_prompt_examples (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    prompt TEXT NOT NULL,
    tags TEXT[] NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on tags for faster searching
CREATE INDEX idx_business_prompt_examples_tags ON vertoie.business_prompt_examples USING GIN(tags);
CREATE INDEX idx_business_prompt_examples_name ON vertoie.business_prompt_examples(name);

-- Add example_prompt_id to organizations table
ALTER TABLE vertoie.organizations 
ADD COLUMN example_prompt_id UUID REFERENCES vertoie.business_prompt_examples(id);

-- Add all business examples from industry_examples.md (102 examples total)
INSERT INTO vertoie.business_prompt_examples (name, prompt, tags) VALUES('Pool Service Company',
$prompt$You are helping a pool service company set up their software modules. Focus on:
- Chemical tracking and dosing calculations
- Route optimization for service calls
- Equipment maintenance records (pumps, filters, heaters)
- Water quality history and trends
- Seasonal service changes (openings, closings)
- Customer pool profiles and preferences
- Weather impact on chemical balance
- Photo documentation for liability
- Inventory management for chemicals and parts
- Recurring service scheduling
- EPA compliance tracking

Ask specific questions about their pool service, pool cleaning, pool maintenance operations, workflows, and specific business needs.$prompt$,
ARRAY['pool service', 'pool cleaning', 'pool maintenance', 'pool', 'pool guy', 'pool care', 'swimming pool']),

('MedSpa',
$prompt$You are helping a medspa set up their software modules. Focus on:
- Patient treatment history and outcomes
- Before/after photo management
- HIPAA compliance and consent forms
- Product inventory with lot numbers and expiration dates
- Treatment protocols and contraindications
- Provider certifications and training records
- Membership and package tracking
- Commission calculations
- Appointment scheduling with room/equipment allocation
- Automated follow-up communications
- Multi-location inventory management
- Patient intake and assessment forms

Ask specific questions about their medspa, medical spa, med spa operations, workflows, and specific business needs.$prompt$,
ARRAY['medspa', 'medical spa', 'med spa', 'aesthetic', 'botox', 'cosmetic', 'aesthetics', 'medical aesthetics']),

('Bakery',
$prompt$You are helping a bakery set up their software modules. Focus on:
- Recipe scaling and yield calculations
- Production planning and prep lists
- Ingredient inventory with expiration tracking
- Allergen tracking and labeling
- Custom order management
- Daily production schedules
- Waste tracking and analysis
- Temperature logs for HACCP compliance
- Wholesale customer accounts
- Standing order management
- Labor scheduling based on production needs
- Cost calculations per item
- Seasonal menu planning

Ask specific questions about their bakery, baker, bread operations, workflows, and specific business needs.$prompt$,
ARRAY['bakery', 'baker', 'bread', 'pastry', 'bake shop', 'patisserie', 'baking', 'cake shop']),

('Software Consulting',
$prompt$You are helping a software consulting set up their software modules. Focus on:
- Time tracking by project and task
- Project milestone management
- Retainer balance tracking
- Scope change documentation
- Code snippet library
- Client portals for project visibility
- Invoice narrative generation
- Resource capacity planning
- Contract and SOW management
- Fixed price vs hourly billing
- Sprint planning and retrospectives
- Technical documentation
- Client feedback collection

Ask specific questions about their software consulting, IT consulting, developer operations, workflows, and specific business needs.$prompt$,
ARRAY['software consulting', 'IT consulting', 'developer', 'programmer', 'software development', 'tech consulting', 'coding']),

('HVAC Service Company',
$prompt$You are helping a hvac service company set up their software modules. Focus on:
- Equipment database with service history
- Parts inventory and compatibility tracking
- Diagnostic code reference library
- Maintenance contract management
- Seasonal demand planning
- Technician skill matching
- Warranty and recall tracking
- Van stock management
- Flat rate pricing calculations
- Photo documentation of work
- Emergency vs scheduled service routing
- Manufacturer specification lookup
- Energy efficiency recommendations

Ask specific questions about their HVAC, heating and cooling, air conditioning operations, workflows, and specific business needs.$prompt$,
ARRAY['HVAC', 'heating and cooling', 'air conditioning', 'AC repair', 'furnace', 'heating', 'cooling', 'HVAC service']),

('Property Management',
$prompt$You are helping a property management set up their software modules. Focus on:
- Tenant portals and communication
- Maintenance request routing
- Lease tracking and renewals
- Rent collection and late fees
- Move-in/out inspections
- Vendor performance tracking
- Property-level financial reporting
- Owner distribution calculations
- Vacancy marketing
- Document storage (leases, insurance)
- Preventive maintenance scheduling
- Security deposit management
- Multi-property portfolio analytics

Ask specific questions about their property management, property manager, rental management operations, workflows, and specific business needs.$prompt$,
ARRAY['property management', 'property manager', 'rental management', 'landlord', 'real estate management', 'HOA', 'apartment management']),

('Restaurant',
$prompt$You are helping a restaurant set up their software modules. Focus on:
- Recipe costing and menu engineering
- Inventory par levels and ordering
- Labor scheduling and forecasting
- Table management and reservations
- Kitchen order flow coordination
- Food waste tracking
- Supplier price comparison
- Daily cash reconciliation
- Health inspection checklists
- Temperature logging
- Server performance metrics
- Customer feedback management
- Seasonal menu planning

Ask specific questions about their restaurant, dining, eatery operations, workflows, and specific business needs.$prompt$,
ARRAY['restaurant', 'dining', 'eatery', 'food service', 'bistro', 'diner', 'eating establishment']),

('Cleaning Services',
$prompt$You are helping a cleaning services set up their software modules. Focus on:
- Recurring client schedules
- Supply inventory and usage tracking
- Employee route optimization
- Time tracking per location
- Quality checklists and inspections
- Before/after photo documentation
- Special requests and preferences
- Key/access code management
- Insurance and bonding documentation
- Equipment maintenance schedules
- Chemical safety compliance
- Client property notes

Ask specific questions about their cleaning, janitorial, maid operations, workflows, and specific business needs.$prompt$,
ARRAY['cleaning', 'janitorial', 'maid', 'housekeeping', 'commercial cleaning', 'office cleaning', 'house cleaning', 'cleaner']),

('Lawn Care/Landscaping',
$prompt$You are helping a lawn care/landscaping set up their software modules. Focus on:
- Seasonal service schedules
- Route optimization for crews
- Equipment maintenance tracking
- Chemical application records
- Weather-based scheduling
- Property measurements and maps
- Plant health tracking
- Irrigation system management
- Leaf/snow removal scheduling
- Material quantity calculations
- Before/after photo galleries
- Crew assignment and tracking

Ask specific questions about their lawn, landscaping, grass operations, workflows, and specific business needs.$prompt$,
ARRAY['lawn', 'landscaping', 'grass', 'mowing', 'yard', 'garden', 'grounds', 'landscape', 'lawn maintenance']),

('Auto Repair Shop',
$prompt$You are helping a auto repair shop set up their software modules. Focus on:
- Vehicle service history
- Parts inventory with compatibility
- Labor guide integration
- Diagnostic trouble codes
- Customer vehicle profiles
- Warranty tracking
- Vendor parts ordering
- Bay scheduling
- Technician skill matching
- State inspection tracking
- Fluid disposal compliance
- Loaner vehicle management

Ask specific questions about their auto repair, mechanic, car repair operations, workflows, and specific business needs.$prompt$,
ARRAY['auto repair', 'mechanic', 'car repair', 'automotive', 'garage', 'auto shop', 'vehicle repair', 'car service']),

('Hair/Nail Salon',
$prompt$You are helping a hair/nail salon set up their software modules. Focus on:
- Appointment booking with stylist selection
- Client style history and photos
- Product inventory tracking
- Color formulation records
- Commission calculations
- Station/chair rental management
- Loyalty program tracking
- Walk-in queue management
- Service menu and pricing
- Tip distribution
- State license tracking
- Retail product sales

Ask specific questions about their salon, hair, beauty operations, workflows, and specific business needs.$prompt$,
ARRAY['salon', 'hair', 'beauty', 'barber', 'nails', 'hairdresser', 'stylist', 'barbershop', 'beauty parlor']),

('Dental Practice',
$prompt$You are helping a dental practice set up their software modules. Focus on:
- Patient health records
- Treatment planning
- Insurance verification and claims
- Appointment scheduling with operatory management
- Digital x-ray and image storage
- Prescription tracking
- Lab case management
- Recall and reminder system
- Procedure coding
- Referral tracking
- HIPAA compliance
- Supply inventory with expiration dates

Ask specific questions about their dentist, dental, orthodontist operations, workflows, and specific business needs.$prompt$,
ARRAY['dentist', 'dental', 'orthodontist', 'oral', 'teeth', 'dental office', 'dental clinic']),

('Plumbing',
$prompt$You are helping a plumbing set up their software modules. Focus on:
- Emergency call routing
- Parts inventory on trucks
- Job estimation tools
- Permit tracking
- Photo documentation of issues
- Warranty management
- Flat rate pricing book
- On-call scheduling
- Equipment installation records
- Code compliance checklists
- Customer property notes
- Subcontractor management

Ask specific questions about their plumber, plumbing, pipes operations, workflows, and specific business needs.$prompt$,
ARRAY['plumber', 'plumbing', 'pipes', 'drain', 'water heater', 'leak', 'plumbing service']),

('Electrical Contracting',
$prompt$You are helping a electrical contracting set up their software modules. Focus on:
- Permit and inspection tracking
- Code compliance documentation
- Panel schedules and load calculations
- Wire and material inventory
- Safety certification tracking
- Job site photo documentation
- Voltage/amperage recordings
- Emergency service dispatch
- Warranty tracking
- Subcontractor coordination
- Time and material billing
- Blueprint/plan storage

Ask specific questions about their electrician, electrical, electric operations, workflows, and specific business needs.$prompt$,
ARRAY['electrician', 'electrical', 'electric', 'wiring', 'electrical contractor', 'power', 'lighting']),

('Personal Training',
$prompt$You are helping a personal training set up their software modules. Focus on:
- Client fitness assessments
- Workout plan creation
- Progress tracking and measurements
- Session scheduling and packages
- Nutrition planning
- Exercise video library
- Liability waivers
- Payment plans and packages
- Home visit routing
- Equipment inventory
- Certification tracking
- Client injury/limitation notes

Ask specific questions about their personal trainer, fitness, trainer operations, workflows, and specific business needs.$prompt$,
ARRAY['personal trainer', 'fitness', 'trainer', 'gym', 'workout', 'exercise', 'fitness coach', 'PT']),

('Photography',
$prompt$You are helping a photography set up their software modules. Focus on:
- Shoot scheduling and locations
- Contract and model release management
- Image galleries and proofing
- Package and pricing options
- Equipment inventory and maintenance
- Backup and archive management
- Print order tracking
- Venue relationships
- Timeline templates
- Travel expense tracking
- Album design process
- Digital delivery systems

Ask specific questions about their photographer, photography, photo operations, workflows, and specific business needs.$prompt$,
ARRAY['photographer', 'photography', 'photo', 'portrait', 'wedding photographer', 'pictures', 'photoshoot']),

('Daycare/Childcare',
$prompt$You are helping a daycare/childcare set up their software modules. Focus on:
- Child enrollment and profiles
- Attendance tracking
- Parent communication portal
- Staff-to-child ratios
- Meal and snack planning
- Activity scheduling
- Incident reporting
- Immunization records
- Emergency contact management
- Pickup authorization
- Billing and subsidies
- State licensing compliance

Ask specific questions about their daycare, childcare, nursery operations, workflows, and specific business needs.$prompt$,
ARRAY['daycare', 'childcare', 'nursery', 'preschool', 'child care', 'kids', 'children', 'babysitting']),

('Home Inspection',
$prompt$You are helping a home inspection set up their software modules. Focus on:
- Inspection report templates
- Photo annotation tools
- Checklist customization by property type
- Radon/mold/pest tracking
- Report delivery systems
- Scheduling with realtors
- Liability documentation
- Continuing education tracking
- Equipment calibration logs
- Reference material library
- Weather condition notes
- Follow-up inspection tracking

Ask specific questions about their home inspector, property inspection, house inspection operations, workflows, and specific business needs.$prompt$,
ARRAY['home inspector', 'property inspection', 'house inspection', 'building inspection', 'inspection', 'home inspection']),

('Catering',
$prompt$You are helping a catering set up their software modules. Focus on:
- Event planning and timelines
- Menu customization and pricing
- Ingredient scaling calculations
- Staff scheduling for events
- Equipment and rental tracking
- Venue relationships
- Dietary restriction management
- Tasting appointment scheduling
- Transport logistics
- Leftover/donation tracking
- License and permit management
- Service style options (buffet, plated, etc.)

Ask specific questions about their catering, caterer, event food operations, workflows, and specific business needs.$prompt$,
ARRAY['catering', 'caterer', 'event food', 'party food', 'food service', 'banquet', 'event catering']),

('Pet Grooming',
$prompt$You are helping a pet grooming set up their software modules. Focus on:
- Pet profiles with breed and temperament
- Grooming style preferences
- Vaccination records
- Appointment scheduling with breed-based timing
- Before/after photos
- Special handling notes
- Product usage tracking
- Cage/station management
- Pick-up/drop-off scheduling
- Incident reporting
- Loyalty programs
- Pet parent communication

Ask specific questions about their pet grooming, dog grooming, groomer operations, workflows, and specific business needs.$prompt$,
ARRAY['pet grooming', 'dog grooming', 'groomer', 'pet groomer', 'dog groomer', 'pet salon', 'grooming']),

('Accounting Firm',
$prompt$You are helping a accounting firm set up their software modules. Focus on:
- Client document portal
- Tax deadline tracking
- Time billing by service type
- Document request checklists
- E-filing status tracking
- Continuing education tracking
- Client communication logs
- Secure file storage
- Engagement letter management
- Tax law update tracking
- Workflow management
- Bank feed integrations

Ask specific questions about their accountant, CPA, bookkeeping operations, workflows, and specific business needs.$prompt$,
ARRAY['accountant', 'CPA', 'bookkeeping', 'tax', 'accounting', 'bookkeeper', 'tax prep', 'accounting firm']),

('Moving Company',
$prompt$You are helping a moving company set up their software modules. Focus on:
- Move estimation tools
- Inventory cataloging
- Crew scheduling and routing
- Truck/equipment tracking
- Storage unit management
- Insurance and valuation
- Long-distance regulations
- Packing supply inventory
- Damage claims processing
- Weight tickets and documentation
- Customer item photos
- Third-party service coordination

Ask specific questions about their moving, movers, relocation operations, workflows, and specific business needs.$prompt$,
ARRAY['moving', 'movers', 'relocation', 'moving service', 'moving company', 'hauling', 'moving and storage']),

('Pest Control',
$prompt$You are helping a pest control set up their software modules. Focus on:
- Property treatment history
- Chemical usage tracking
- Recurring service schedules
- Pest identification guides
- State licensing compliance
- Treatment effectiveness tracking
- Bait station mapping
- Weather-based scheduling
- Warranty management
- Safety data sheets
- Restricted area notifications
- Equipment calibration logs

Ask specific questions about their pest control, exterminator, pest operations, workflows, and specific business needs.$prompt$,
ARRAY['pest control', 'exterminator', 'pest', 'termite', 'bug', 'rodent', 'pest management', 'extermination']),

('Window Cleaning',
$prompt$You are helping a window cleaning set up their software modules. Focus on:
- Route optimization for properties
- Height/difficulty pricing factors
- Recurring service schedules
- Weather-dependent scheduling
- Equipment maintenance logs
- Safety compliance tracking
- Before/after documentation
- Water usage tracking
- Screen repair tracking
- Ladder/lift scheduling
- Commercial property management
- Seasonal service variations

Ask specific questions about their window cleaning, window washer, window cleaner operations, workflows, and specific business needs.$prompt$,
ARRAY['window cleaning', 'window washer', 'window cleaner', 'glass cleaning', 'pressure washing', 'exterior cleaning']),

('Carpet Cleaning',
$prompt$You are helping a carpet cleaning set up their software modules. Focus on:
- Room measurements and pricing
- Stain treatment tracking
- Equipment maintenance schedules
- Chemical inventory and usage
- Drying time estimates
- Furniture moving notes
- Pet treatment surcharges
- Warranty tracking
- Before/after photos
- Recurring service programs
- Truck mount maintenance
- Water extraction volumes

Ask specific questions about their carpet cleaning, carpet cleaner, rug cleaning operations, workflows, and specific business needs.$prompt$,
ARRAY['carpet cleaning', 'carpet cleaner', 'rug cleaning', 'upholstery', 'steam cleaning', 'carpet care']),

('Painting Contractors',
$prompt$You are helping a painting contractors set up their software modules. Focus on:
- Surface area calculations
- Paint quantity estimating
- Color selection records
- Surface preparation checklists
- Weather tracking for exterior jobs
- Spray equipment maintenance
- Lead safety compliance
- Warranty tracking
- Before/after galleries
- Subcontractor management
- Material supplier accounts
- Project phase tracking

Ask specific questions about their painter, painting, house painting operations, workflows, and specific business needs.$prompt$,
ARRAY['painter', 'painting', 'house painting', 'painting contractor', 'paint', 'interior painting', 'exterior painting']),

('Handyman Services',
$prompt$You are helping a handyman services set up their software modules. Focus on:
- Job type categorization
- Time and material tracking
- Photo documentation
- Minimum charge management
- Supplier relationships
- Tool inventory
- License tracking by trade
- Insurance documentation
- Property access management
- Repeat customer tracking
- Emergency call handling
- Quote follow-up system

Ask specific questions about their handyman, handy man, home repair operations, workflows, and specific business needs.$prompt$,
ARRAY['handyman', 'handy man', 'home repair', 'fix it', 'maintenance', 'jack of all trades', 'general repair']),

('Massage Therapy',
$prompt$You are helping a massage therapy set up their software modules. Focus on:
- Client health intake forms
- Session notes and treatment plans
- Appointment scheduling with room management
- Package and membership sales
- Oil/lotion inventory
- Continuing education tracking
- License and certification management
- Intake form management
- Pressure preferences
- Focus area tracking
- Gift certificate management
- Therapist availability matching

Ask specific questions about their massage, massage therapist, masseuse operations, workflows, and specific business needs.$prompt$,
ARRAY['massage', 'massage therapist', 'masseuse', 'bodywork', 'therapeutic massage', 'spa', 'massage therapy']),

('Tattoo/Piercing Studio',
$prompt$You are helping a tattoo/piercing studio set up their software modules. Focus on:
- Design portfolio management
- Consent and waiver forms
- Appointment deposits
- Artist commission tracking
- Ink inventory by color
- Needle and supply tracking
- Health compliance documentation
- Photo release forms
- Aftercare instruction delivery
- Design consultation scheduling
- Flash art organization
- Equipment sterilization logs

Ask specific questions about their tattoo, tattoo shop, tattoo parlor operations, workflows, and specific business needs.$prompt$,
ARRAY['tattoo', 'tattoo shop', 'tattoo parlor', 'piercing', 'tattoo artist', 'body art', 'ink']),

('Veterinary Clinic',
$prompt$You are helping a veterinary clinic set up their software modules. Focus on:
- Pet medical records
- Vaccination schedules
- Prescription management
- Lab result tracking
- Surgery scheduling
- Boarding management
- Multi-pet family accounts
- Inventory with expiration dates
- Controlled substance logs
- Appointment reminders
- Emergency triage
- Referral management

Ask specific questions about their vet, veterinary, veterinarian operations, workflows, and specific business needs.$prompt$,
ARRAY['vet', 'veterinary', 'veterinarian', 'animal hospital', 'animal clinic', 'pet hospital', 'vet clinic']),

('Tutoring Services',
$prompt$You are helping a tutoring services set up their software modules. Focus on:
- Student progress tracking
- Lesson planning
- Parent communication portal
- Tutor matching by subject
- Session scheduling and packages
- Learning material library
- Assessment results tracking
- Homework tracking
- Goal setting and milestones
- Payment plans
- Location management (in-home, center, online)
- Tutor qualification tracking

Ask specific questions about their tutor, tutoring, academic operations, workflows, and specific business needs.$prompt$,
ARRAY['tutor', 'tutoring', 'academic', 'test prep', 'education', 'learning center', 'tutoring service']),

('Optometry',
$prompt$You are helping a optometry set up their software modules. Focus on:
- Patient vision records
- Prescription management
- Frame and lens inventory
- Contact lens ordering
- Insurance verification
- Lab order tracking
- Appointment scheduling with equipment
- Recall reminders
- Frame styling records
- Warranty tracking
- Vision therapy tracking
- Referral management

Ask specific questions about their optometrist, eye doctor, optometry operations, workflows, and specific business needs.$prompt$,
ARRAY['optometrist', 'eye doctor', 'optometry', 'vision', 'glasses', 'contacts', 'eye care', 'optical']),

('Law Firm (Small)',
$prompt$You are helping a law firm (small) set up their software modules. Focus on:
- Case management
- Time tracking by matter
- Document management
- Court date tracking
- Client communication logs
- Billing and trust accounts
- Conflict checking
- Deadline management
- Retainer tracking
- Expense tracking
- Document templates
- Statute of limitations tracking

Ask specific questions about their lawyer, attorney, law firm operations, workflows, and specific business needs.$prompt$,
ARRAY['lawyer', 'attorney', 'law firm', 'legal', 'law office', 'legal services', 'counsel']),

('Real Estate Agency',
$prompt$You are helping a real estate agency set up their software modules. Focus on:
- Property listings management
- Client relationship tracking
- Showing scheduling
- Commission calculations
- Transaction document management
- MLS integration needs
- Open house management
- Lead tracking and sources
- Marketing campaign tracking
- Closing coordination
- Vendor relationships
- License renewal tracking

Ask specific questions about their realtor, real estate, realty operations, workflows, and specific business needs.$prompt$,
ARRAY['realtor', 'real estate', 'realty', 'property', 'real estate agent', 'broker', 'real estate office']),

('Graphic Design',
$prompt$You are helping a graphic design set up their software modules. Focus on:
- Project brief management
- Revision tracking
- Asset library organization
- Client approval workflows
- Time tracking by project
- Print specification management
- Color profile management
- Vendor relationships (printers)
- License tracking for assets
- Portfolio management
- Quote templates
- File versioning

Ask specific questions about their graphic design, designer, graphics operations, workflows, and specific business needs.$prompt$,
ARRAY['graphic design', 'designer', 'graphics', 'design studio', 'creative', 'graphic designer', 'design agency']),

('Coffee Shop',
$prompt$You are helping a coffee shop set up their software modules. Focus on:
- Recipe consistency cards
- Inventory with roast dates
- Staff scheduling with roles
- Customer loyalty programs
- Seasonal menu management
- Equipment maintenance schedules
- Supplier relationships
- Daily cash reconciliation
- Pastry inventory tracking
- Temperature logging
- Waste tracking
- Event hosting calendar

Ask specific questions about their coffee shop, cafe, coffee operations, workflows, and specific business needs.$prompt$,
ARRAY['coffee shop', 'cafe', 'coffee', 'espresso', 'coffeehouse', 'coffee bar', 'barista']),

('Food Truck',
$prompt$You are helping a food truck set up their software modules. Focus on:
- Location scheduling and permits
- Commissary kitchen compliance
- Menu optimization for speed
- Inventory in limited space
- Route planning
- Event booking management
- Weather contingency planning
- Generator/equipment maintenance
- Social media location updates
- Cash management
- Health permit tracking
- Prep kitchen coordination

Ask specific questions about their food truck, mobile food, food cart operations, workflows, and specific business needs.$prompt$,
ARRAY['food truck', 'mobile food', 'food cart', 'street food', 'mobile kitchen', 'concession', 'food trailer']),

('Auto Detailing',
$prompt$You are helping a auto detailing set up their software modules. Focus on:
- Service package options
- Appointment duration by vehicle size
- Before/after photo galleries
- Product usage tracking
- Mobile service routing
- Membership programs
- Gift certificate sales
- Water usage tracking
- Equipment maintenance
- Damage documentation
- Weather rescheduling
- Add-on service tracking

Ask specific questions about their auto detailing, car detailing, detail shop operations, workflows, and specific business needs.$prompt$,
ARRAY['auto detailing', 'car detailing', 'detail shop', 'car wash', 'vehicle detailing', 'auto detail', 'detailer']),

('Fitness Studio/Gym',
$prompt$You are helping a fitness studio/gym set up their software modules. Focus on:
- Membership management and billing
- Class scheduling and capacity limits
- Instructor scheduling and payments
- Equipment maintenance tracking
- Member check-in and access control
- Personal training packages
- Fitness assessment tracking
- Liability waivers and health forms
- Locker assignments
- Guest pass management
- Challenge and program tracking
- Equipment reservation system

Ask specific questions about their gym, fitness center, fitness studio operations, workflows, and specific business needs.$prompt$,
ARRAY['gym', 'fitness center', 'fitness studio', 'health club', 'workout', 'crossfit', 'yoga studio', 'pilates']),

('Insurance Agency',
$prompt$You are helping a insurance agency set up their software modules. Focus on:
- Policy tracking and renewals
- Commission calculations and splits
- Lead management and follow-ups
- Quote comparison tools
- Client life event tracking
- Carrier appointment management
- E&O documentation
- License and CE tracking
- Claims assistance tracking
- Cross-sell opportunities
- Referral source tracking
- Policy document storage

Ask specific questions about their insurance agent, insurance broker, insurance agency operations, workflows, and specific business needs.$prompt$,
ARRAY['insurance agent', 'insurance broker', 'insurance agency', 'insurance office', 'insurance sales']),

('Flooring Contractor',
$prompt$You are helping a flooring contractor set up their software modules. Focus on:
- Material quantity calculations
- Subfloor assessment notes
- Installation scheduling
- Warranty tracking
- Supplier catalogs and pricing
- Room measurement tools
- Moisture reading logs
- Adhesive and underlayment tracking
- Furniture moving coordination
- Disposal fee calculations
- Sample inventory management
- Installation photo documentation

Ask specific questions about their flooring, floor installer, carpet installer operations, workflows, and specific business needs.$prompt$,
ARRAY['flooring', 'floor installer', 'carpet installer', 'hardwood', 'tile installer', 'flooring contractor']),

('Roofing Contractor',
$prompt$You are helping a roofing contractor set up their software modules. Focus on:
- Roof measurement and estimation
- Material ordering by squares
- Weather-dependent scheduling
- Insurance claim assistance
- Warranty tracking
- Safety compliance documentation
- Drone inspection notes
- Storm damage lead tracking
- Crew assignment
- Disposal and recycling tracking
- Manufacturer certification tracking
- Before/after documentation

Ask specific questions about their roofer, roofing, roof repair operations, workflows, and specific business needs.$prompt$,
ARRAY['roofer', 'roofing', 'roof repair', 'roofing contractor', 'roof replacement', 'shingles']),

('Event Planning',
$prompt$You are helping a event planning set up their software modules. Focus on:
- Event timeline management
- Vendor coordination and contracts
- Budget tracking by category
- Guest list management
- Venue relationships
- Design mood boards
- Payment schedules
- Day-of coordination checklists
- Rental inventory tracking
- Client preference profiles
- Post-event feedback
- Seasonal availability calendar

Ask specific questions about their event planner, party planner, wedding planner operations, workflows, and specific business needs.$prompt$,
ARRAY['event planner', 'party planner', 'wedding planner', 'event coordinator', 'event planning', 'events']),

('Chiropractic Clinic',
$prompt$You are helping a chiropractic clinic set up their software modules. Focus on:
- Patient treatment plans
- X-ray and scan management
- Adjustment history tracking
- Insurance verification
- Care plan packaging
- Posture analysis documentation
- Exercise prescription library
- Referral tracking
- Maintenance care scheduling
- Progress evaluations
- Subluxation documentation
- Multi-visit package tracking

Ask specific questions about their chiropractor, chiropractic, chiro operations, workflows, and specific business needs.$prompt$,
ARRAY['chiropractor', 'chiropractic', 'chiro', 'back doctor', 'spine', 'adjustment', 'chiropractic clinic']),

('Physical Therapy',
$prompt$You are helping a physical therapy set up their software modules. Focus on:
- Treatment plan creation
- Exercise prescription with videos
- Progress measurement tracking
- Insurance authorization management
- Equipment usage scheduling
- Home exercise program delivery
- Referral source tracking
- Outcome measurement tools
- Modality usage tracking
- Documentation compliance
- Discharge planning
- Therapist productivity tracking

Ask specific questions about their physical therapy, PT, physical therapist operations, workflows, and specific business needs.$prompt$,
ARRAY['physical therapy', 'PT', 'physical therapist', 'physiotherapy', 'rehab', 'rehabilitation', 'sports therapy']),

('Locksmith',
$prompt$You are helping a locksmith set up their software modules. Focus on:
- Emergency call dispatch
- Key code database
- Vehicle year/make/model database
- Inventory by lock type
- Mobile service routing
- After-hours pricing
- Commercial master key systems
- Safe combination management
- Access control system notes
- Warranty tracking
- Supplier relationships
- Security clearance documentation

Ask specific questions about their locksmith, lock smith, key maker operations, workflows, and specific business needs.$prompt$,
ARRAY['locksmith', 'lock smith', 'key maker', 'lock service', 'key cutting', 'lock repair']),

('Appliance Repair',
$prompt$You are helping a appliance repair set up their software modules. Focus on:
- Model number database
- Parts inventory and ordering
- Warranty claim processing
- Service call history by appliance
- Diagnostic flowcharts
- Manufacturer technical bulletins
- Recall tracking
- Customer appliance inventory
- Extended warranty sales
- Core exchange tracking
- Tool calibration logs
- EPA certification tracking

Ask specific questions about their appliance repair, appliance service, washer repair operations, workflows, and specific business needs.$prompt$,
ARRAY['appliance repair', 'appliance service', 'washer repair', 'refrigerator repair', 'appliance technician']),

('Dance Studio',
$prompt$You are helping a dance studio set up their software modules. Focus on:
- Class scheduling with age/level groups
- Recital planning and casting
- Costume ordering and sizing
- Tuition and fee management
- Student progress tracking
- Competition team management
- Studio rental scheduling
- Music licensing tracking
- Parent portal access
- Attendance tracking
- Teacher scheduling
- Makeup class policies

Ask specific questions about their dance studio, dance school, ballet operations, workflows, and specific business needs.$prompt$,
ARRAY['dance studio', 'dance school', 'ballet', 'dance academy', 'dance classes', 'dance instruction']),

('Music School/Lessons',
$prompt$You are helping a music school/lessons set up their software modules. Focus on:
- Student lesson scheduling
- Practice log tracking
- Recital planning
- Sheet music library
- Instrument rental tracking
- Teacher availability matching
- Make-up lesson policies
- Progress evaluations
- Theory curriculum tracking
- Ensemble group management
- Room scheduling
- Parent communication portal

Ask specific questions about their music lessons, piano teacher, guitar lessons operations, workflows, and specific business needs.$prompt$,
ARRAY['music lessons', 'piano teacher', 'guitar lessons', 'music school', 'voice lessons', 'music instruction']),

('Liquor Store',
$prompt$You are helping a liquor store set up their software modules. Focus on:
- Age verification logging
- Inventory by alcohol type
- Vendor deal tracking
- Special order management
- Case discount calculations
- Seasonal demand planning
- Loyalty program management
- Delivery zone management
- License compliance tracking
- Tasting event management
- Temperature control logs
- Breakage tracking

Ask specific questions about their liquor store, wine shop, beer store operations, workflows, and specific business needs.$prompt$,
ARRAY['liquor store', 'wine shop', 'beer store', 'alcohol', 'spirits', 'bottle shop', 'package store']),

('Flower Shop',
$prompt$You are helping a flower shop set up their software modules. Focus on:
- Perishable inventory tracking
- Order-to-delivery workflow
- Wire service orders
- Event proposal creation
- Seasonal availability
- Delivery route planning
- Vase and container tracking
- Wedding/event timelines
- Cooler temperature logs
- Standing order management
- Waste and spoilage tracking
- Photo portfolio management

Ask specific questions about their florist, flower shop, floral operations, workflows, and specific business needs.$prompt$,
ARRAY['florist', 'flower shop', 'floral', 'bouquet', 'flower delivery', 'floral design', 'flowers']),

('Boutique Clothing Store',
$prompt$You are helping a boutique clothing store set up their software modules. Focus on:
- Inventory by size/color/style
- Consignment tracking
- Seasonal buying plans
- Customer size preferences
- Special order management
- Alteration tracking
- Loyalty program management
- Trunk show planning
- Vendor return windows
- Visual merchandising schedules
- Commission sales tracking
- Instagram shopping integration

Ask specific questions about their boutique, clothing store, fashion operations, workflows, and specific business needs.$prompt$,
ARRAY['boutique', 'clothing store', 'fashion', 'apparel', 'dress shop', 'clothing boutique', 'retail clothing']),

('Mobile Car Mechanic',
$prompt$You are helping a mobile car mechanic set up their software modules. Focus on:
- On-site service capabilities list
- Travel time and mileage tracking
- Mobile parts inventory
- Customer location notes
- Weather contingency planning
- Tool inventory management
- Emergency roadside dispatch
- Pre-arrival diagnostics
- Payment processing on-site
- Disposal and fluid management
- Service area boundaries
- Competitor tow coordination

Ask specific questions about their mobile mechanic, mobile auto repair, traveling mechanic operations, workflows, and specific business needs.$prompt$,
ARRAY['mobile mechanic', 'mobile auto repair', 'traveling mechanic', 'on-site mechanic', 'mobile car repair']),

('Laundromat',
$prompt$You are helping a laundromat set up their software modules. Focus on:
- Machine usage tracking
- Maintenance schedules by unit
- Coin/card system management
- Utility usage monitoring
- Wash-and-fold order tracking
- Supply vending inventory
- Security camera notes
- Lost and found management
- Commercial account management
- Equipment ROI tracking
- Peak hour analysis
- Service call tracking

Ask specific questions about their laundromat, coin laundry, washateria operations, workflows, and specific business needs.$prompt$,
ARRAY['laundromat', 'coin laundry', 'washateria', 'self-service laundry', 'laundry mat', 'wash and fold']),

('Car Wash',
$prompt$You are helping a car wash set up their software modules. Focus on:
- Wash package options
- Membership program tracking
- Chemical usage monitoring
- Equipment maintenance cycles
- Water reclamation tracking
- Damage claim processing
- Weather impact analysis
- Fleet account management
- Loyalty punch cards
- Vacuum timer management
- Bay scheduling (for manual)
- Environmental compliance logs

Ask specific questions about their car wash, auto wash, car detailing operations, workflows, and specific business needs.$prompt$,
ARRAY['car wash', 'auto wash', 'car detailing', 'vehicle wash', 'wash bay', 'automatic car wash']),

('Driving School',
$prompt$You are helping a driving school set up their software modules. Focus on:
- Student progress tracking
- Instructor scheduling and vehicles
- DMV test scheduling
- Vehicle maintenance logs
- Insurance documentation
- Route planning for lessons
- Written test prep tracking
- Behind-the-wheel hours logging
- Parent portal access
- State compliance tracking
- Vehicle inspection logs
- Defensive driving certification

Ask specific questions about their driving school, drivers ed, driving instructor operations, workflows, and specific business needs.$prompt$,
ARRAY['driving school', 'drivers ed', 'driving instructor', 'CDL training', 'driving lessons', 'traffic school']),

('Escape Room',
$prompt$You are helping a escape room set up their software modules. Focus on:
- Booking with time slots
- Group size management
- Game master scheduling
- Puzzle reset checklists
- Hint tracking system
- Success rate analytics
- Corporate team building packages
- Waiver management
- Photo opportunity tracking
- Prop maintenance logs
- Theme rotation planning
- Competition timing records

Ask specific questions about their escape room, escape game, puzzle room operations, workflows, and specific business needs.$prompt$,
ARRAY['escape room', 'escape game', 'puzzle room', 'mystery room', 'escape room business']),

('3D Printing Service',
$prompt$You are helping a 3d printing service set up their software modules. Focus on:
- Print time estimation
- Material usage tracking
- File preparation workflow
- Quality control checklists
- Machine hours logging
- Filament inventory by type/color
- Design consultation tracking
- Post-processing time tracking
- Rush order management
- Failed print tracking
- Customer file storage
- Maintenance cycle tracking

Ask specific questions about their 3D printing, 3D print, additive manufacturing operations, workflows, and specific business needs.$prompt$,
ARRAY['3D printing', '3D print', 'additive manufacturing', 'rapid prototyping', '3D printing service']),

('Storage Facility',
$prompt$You are helping a storage facility set up their software modules. Focus on:
- Unit availability mapping
- Rental agreement management
- Automatic payment processing
- Gate access code management
- Auction/lien process tracking
- Climate control monitoring
- Pest control schedules
- Security system logs
- Move-in/out scheduling
- Insurance offering tracking
- Delinquency management
- Waiting list management

Ask specific questions about their storage, self storage, storage unit operations, workflows, and specific business needs.$prompt$,
ARRAY['storage', 'self storage', 'storage unit', 'mini storage', 'storage facility', 'public storage']),

('Vape Shop',
$prompt$You are helping a vape shop set up their software modules. Focus on:
- Age verification compliance
- Inventory by nicotine levels
- Flavor profile management
- Device warranty tracking
- Coil compatibility database
- Customer flavor preferences
- Lab test documentation
- Battery safety tracking
- Loyalty program management
- Custom mix recipes
- Regulatory compliance updates
- Product recall tracking

Ask specific questions about their vape shop, vapor, e-cigarette operations, workflows, and specific business needs.$prompt$,
ARRAY['vape shop', 'vapor', 'e-cigarette', 'smoke shop', 'vaping', 'e-liquid', 'vape store']),

('Financial Advisor',
$prompt$You are helping a financial advisor set up their software modules. Focus on:
- Client portfolio tracking
- Goal planning and progress
- Meeting notes and action items
- Compliance documentation
- Risk assessment profiles
- Fee calculation and billing
- Market update communications
- Referral tracking
- CE credit tracking
- Client life event monitoring
- Document vault
- Performance reporting schedules

Ask specific questions about their financial advisor, financial planner, wealth management operations, workflows, and specific business needs.$prompt$,
ARRAY['financial advisor', 'financial planner', 'wealth management', 'investment advisor', 'financial planning']),

('Construction/General Contractor',
$prompt$You are helping a construction/general contractor set up their software modules. Focus on:
- Project phase tracking
- Subcontractor scheduling
- Change order management
- Progress billing/draw requests
- Permit tracking
- Safety compliance documentation
- Material delivery coordination
- Punch list management
- Lien waiver tracking
- Weather delay documentation
- Project photo timelines
- Bid comparison tools

Ask specific questions about their general contractor, construction, GC operations, workflows, and specific business needs.$prompt$,
ARRAY['general contractor', 'construction', 'GC', 'builder', 'contractor', 'construction company', 'home builder']),

('Pharmacy',
$prompt$You are helping a pharmacy set up their software modules. Focus on:
- Prescription workflow management
- Insurance adjudication
- Controlled substance logging
- Compound formulation records
- Medication therapy management
- Immunization records
- Delivery route management
- Prior authorization tracking
- Drug interaction checking
- Inventory with expiration dates
- Patient counseling documentation
- HIPAA compliance

Ask specific questions about their pharmacy, drugstore, chemist operations, workflows, and specific business needs.$prompt$,
ARRAY['pharmacy', 'drugstore', 'chemist', 'apothecary', 'prescription', 'independent pharmacy', 'compounding pharmacy']),

('IT Support/MSP',
$prompt$You are helping a it support/msp set up their software modules. Focus on:
- Ticket management system
- Remote access tracking
- Asset management by client
- Patch management schedules
- Backup monitoring
- Service level agreements
- Project vs break-fix billing
- Knowledge base articles
- Vendor relationship management
- License tracking
- Network documentation
- On-call rotation schedules

Ask specific questions about their IT support, computer repair, tech support operations, workflows, and specific business needs.$prompt$,
ARRAY['IT support', 'computer repair', 'tech support', 'MSP', 'managed services', 'IT services', 'computer help']),

('Martial Arts Studio',
$prompt$You are helping a martial arts studio set up their software modules. Focus on:
- Belt progression tracking
- Class attendance requirements
- Tournament registration
- Sparring gear inventory
- Testing schedules
- Family account management
- Contract management
- Injury waivers
- Instructor certification tracking
- Mat time scheduling
- Seminar planning
- Equipment checkout system

Ask specific questions about their martial arts, karate, taekwondo operations, workflows, and specific business needs.$prompt$,
ARRAY['martial arts', 'karate', 'taekwondo', 'jiu jitsu', 'dojo', 'kung fu', 'MMA', 'self defense']),

('Dry Cleaner',
$prompt$You are helping a dry cleaner set up their software modules. Focus on:
- Order tracking with barcodes
- Garment inventory by customer
- Stain treatment notes
- Rush order management
- Route delivery scheduling
- Alteration measurements
- Lost item procedures
- Environmental compliance
- Recurring corporate accounts
- Seasonal storage service
- Damage claim handling
- Price list by garment type

Ask specific questions about their dry cleaner, cleaners, laundry service operations, workflows, and specific business needs.$prompt$,
ARRAY['dry cleaner', 'cleaners', 'laundry service', 'dry cleaning', 'alterations', 'pressing']),

('HVAC Installation (New Construction)',
$prompt$You are helping a hvac installation (new construction) set up their software modules. Focus on:
- Load calculations
- Equipment sizing tools
- Ductwork design plans
- Builder scheduling coordination
- Warranty registration
- Code inspection scheduling
- Material staging logistics
- Multi-unit project tracking
- Commissioning checklists
- As-built documentation
- Subcontractor coordination
- Progress payment tracking

Ask specific questions about their HVAC installer, heating installation, AC installation operations, workflows, and specific business needs.$prompt$,
ARRAY['HVAC installer', 'heating installation', 'AC installation', 'new construction HVAC', 'ductwork']),

('Tree Service',
$prompt$You are helping a tree service set up their software modules. Focus on:
- Tree health assessments
- Permit requirements by area
- Equipment maintenance (chainsaws, chippers)
- Crew certification tracking
- Emergency storm response
- Wood disposal/recycling
- Utility line clearance compliance
- Quote with aerial photos
- Insurance documentation
- Seasonal treatment schedules
- Safety equipment logs
- Customer property damage prevention

Ask specific questions about their tree service, arborist, tree removal operations, workflows, and specific business needs.$prompt$,
ARRAY['tree service', 'arborist', 'tree removal', 'tree trimming', 'tree care', 'stump grinding']),

('Cabinet Making/Carpentry',
$prompt$You are helping a cabinet making/carpentry set up their software modules. Focus on:
- Design and measurement tools
- Material optimization/cut lists
- Hardware inventory tracking
- Finish schedule management
- Installation scheduling
- Shop time tracking
- Custom color matching
- Project photo galleries
- Vendor account management
- Shop equipment maintenance
- Dust collection compliance
- Delivery coordination

Ask specific questions about their cabinet maker, carpenter, woodworking operations, workflows, and specific business needs.$prompt$,
ARRAY['cabinet maker', 'carpenter', 'woodworking', 'custom cabinets', 'millwork', 'woodshop', 'cabinetry']),

('Pool Installation/Construction',
$prompt$You are helping a pool installation/construction set up their software modules. Focus on:
- Design and permitting workflow
- Excavation scheduling
- Subcontractor coordination
- Progress photo documentation
- Material delivery timing
- Inspection scheduling
- Weather delay tracking
- HOA approval tracking
- Warranty management
- Pool school scheduling
- Equipment selection guides
- Project milestone billing

Ask specific questions about their pool builder, pool contractor, pool installation operations, workflows, and specific business needs.$prompt$,
ARRAY['pool builder', 'pool contractor', 'pool installation', 'pool construction', 'swimming pool builder']),

('Security System Installation',
$prompt$You are helping a security system installation set up their software modules. Focus on:
- System design templates
- Central station coordination
- Recurring monitoring billing
- Service call dispatching
- Equipment inventory by type
- License and certification tracking
- Code compliance by jurisdiction
- Customer portal access
- False alarm tracking
- Maintenance contract management
- Integration documentation
- Emergency contact management

Ask specific questions about their security installer, alarm company, security systems operations, workflows, and specific business needs.$prompt$,
ARRAY['security installer', 'alarm company', 'security systems', 'camera installation', 'access control']),

('Nutritionist/Dietitian',
$prompt$You are helping a nutritionist/dietitian set up their software modules. Focus on:
- Meal plan creation
- Client food diary tracking
- Progress measurements
- Supplement recommendations
- Insurance billing codes
- Telehealth session management
- Recipe database
- Grocery list generation
- Lab result tracking
- Continuing education credits
- Group program management
- Client portal with resources

Ask specific questions about their nutritionist, dietitian, nutrition counseling operations, workflows, and specific business needs.$prompt$,
ARRAY['nutritionist', 'dietitian', 'nutrition counseling', 'diet coach', 'nutrition consultant', 'wellness coach']),

('House Cleaning Service',
$prompt$You are helping a house cleaning service set up their software modules. Focus on:
- Recurring schedule management
- Team route optimization
- Supply usage per home
- Key/code management
- Quality checklists
- Special request tracking
- First-time deep clean pricing
- Pet and allergy notes
- Damage/breakage procedures
- Holiday scheduling
- Team performance tracking
- Customer preference profiles

Ask specific questions about their house cleaning, maid service, residential cleaning operations, workflows, and specific business needs.$prompt$,
ARRAY['house cleaning', 'maid service', 'residential cleaning', 'home cleaning', 'housekeeping service']),

('Mobile Notary',
$prompt$You are helping a mobile notary set up their software modules. Focus on:
- Appointment scheduling with travel time
- Document type tracking
- Signing fee calculations
- Mileage logging
- E-notary capabilities
- Commission expiration tracking
- Journal entry requirements
- Witness coordination
- Title company relationships
- RON platform integration
- Error and omission tracking
- Same-day availability management

Ask specific questions about their mobile notary, notary public, signing agent operations, workflows, and specific business needs.$prompt$,
ARRAY['mobile notary', 'notary public', 'signing agent', 'loan signing', 'traveling notary', 'notary service']),

('Junk Removal',
$prompt$You are helping a junk removal set up their software modules. Focus on:
- Volume-based pricing estimates
- Truck capacity scheduling
- Disposal fee tracking
- Recycling/donation routing
- Heavy item surcharges
- Property cleanout stages
- Hazardous material procedures
- Before/after photos
- Estate sale coordination
- Scrap value tracking
- Transfer station relationships
- Hoarding situation protocols

Ask specific questions about their junk removal, hauling, trash removal operations, workflows, and specific business needs.$prompt$,
ARRAY['junk removal', 'hauling', 'trash removal', 'debris removal', 'junk hauling', 'cleanout service']),

('Home Staging',
$prompt$You are helping a home staging set up their software modules. Focus on:
- Inventory tracking by room type
- Rental period management
- Moving and setup scheduling
- Before/after photo galleries
- Realtor relationship management
- Warehouse organization
- Damage and loss tracking
- Styling package options
- Consultation to contract workflow
- ROI tracking for clients
- Seasonal decor rotation
- Virtual staging services

Ask specific questions about their home staging, staging, home stager operations, workflows, and specific business needs.$prompt$,
ARRAY['home staging', 'staging', 'home stager', 'property staging', 'real estate staging', 'house staging']),

('Courier Service',
$prompt$You are helping a courier service set up their software modules. Focus on:
- Real-time dispatch system
- Route optimization
- Signature capture
- Package tracking
- Driver capacity management
- Rush delivery pricing
- Regular route management
- Proof of delivery storage
- Customer delivery preferences
- Multi-stop optimization
- Vehicle maintenance tracking
- Independent contractor management

Ask specific questions about their courier, messenger, delivery service operations, workflows, and specific business needs.$prompt$,
ARRAY['courier', 'messenger', 'delivery service', 'same day delivery', 'courier service', 'local delivery']),

('Pawn Shop',
$prompt$You are helping a pawn shop set up their software modules. Focus on:
- Loan tracking with interest
- Inventory aging and turns
- Police reporting compliance
- Customer ID verification
- Collateral storage management
- Extension and renewal tracking
- Forfeiture procedures
- Buy/sell/trade workflows
- Precious metal calculations
- Firearms compliance
- Layaway management
- Daily cash reconciliation

Ask specific questions about their pawn shop, pawn broker, pawn store operations, workflows, and specific business needs.$prompt$,
ARRAY['pawn shop', 'pawn broker', 'pawn store', 'collateral loans', 'buy sell trade', 'pawnbroker']),

('Septic Service',
$prompt$You are helping a septic service set up their software modules. Focus on:
- Pumping schedule reminders
- Tank size and location records
- Inspection documentation
- County compliance reporting
- Route optimization
- Disposal site tracking
- Emergency service dispatch
- System repair history
- Water usage recommendations
- Real estate inspection certs
- Enzyme treatment schedules
- Failed system remediation

Ask specific questions about their septic service, septic pumping, septic tank operations, workflows, and specific business needs.$prompt$,
ARRAY['septic service', 'septic pumping', 'septic tank', 'septic cleaning', 'septic system', 'sewage service']),

('Smoke Shop/Tobacco',
$prompt$You are helping a smoke shop/tobacco set up their software modules. Focus on:
- Age verification compliance
- Tobacco tax reporting
- Humidor climate control
- Special order management
- Loyalty program tracking
- Event planning (tastings)
- Membership/locker rental
- High-value inventory security
- Vendor minimum orders
- Product knowledge database
- Local tax variations
- Accessories inventory

Ask specific questions about their smoke shop, tobacco shop, cigar shop operations, workflows, and specific business needs.$prompt$,
ARRAY['smoke shop', 'tobacco shop', 'cigar shop', 'cigarettes', 'tobacco store', 'cigar lounge']),

('Tour Company',
$prompt$You are helping a tour company set up their software modules. Focus on:
- Tour scheduling with capacity
- Multi-language guide assignments
- Pickup location coordination
- Weather contingency plans
- Group booking management
- Commission tracking (hotels/partners)
- Equipment check-out
- Waiver management
- Photo package sales
- Seasonal tour variations
- Transportation coordination
- Guest feedback collection

Ask specific questions about their tour operator, tour company, sightseeing operations, workflows, and specific business needs.$prompt$,
ARRAY['tour operator', 'tour company', 'sightseeing', 'tour guide', 'excursions', 'guided tours', 'tour business']),

('Tailor/Alterations',
$prompt$You are helping a tailor/alterations set up their software modules. Focus on:
- Measurement records
- Alteration type pricing
- Rush order management
- Fitting appointment scheduling
- Fabric and notion inventory
- Wedding party coordination
- Completion notification system
- Custom clothing orders
- Repair service tracking
- Seasonal demand planning
- Quality control checklists
- Designer relationships

Ask specific questions about their tailor, alterations, seamstress operations, workflows, and specific business needs.$prompt$,
ARRAY['tailor', 'alterations', 'seamstress', 'clothing alterations', 'tailoring', 'custom tailor']),

('Boat/Marine Service',
$prompt$You are helping a boat/marine service set up their software modules. Focus on:
- Vessel documentation
- Winterization/summerization schedules
- Parts compatibility by model
- Haul-out scheduling
- Storage location tracking
- Engine hour tracking
- Coast Guard compliance
- Insurance claim assistance
- Bottom paint records
- Electronics installation notes
- Slip assignment management
- Environmental compliance

Ask specific questions about their boat repair, marine service, boat maintenance operations, workflows, and specific business needs.$prompt$,
ARRAY['boat repair', 'marine service', 'boat maintenance', 'marina service', 'marine mechanic', 'boat yard']),

('Barber Shop (Traditional)',
$prompt$You are helping a barber shop (traditional) set up their software modules. Focus on:
- Walk-in queue management
- Barber chair assignments
- Hot towel service tracking
- Straight razor maintenance logs
- Cash-heavy transaction tracking
- Traditional service menu
- Product sales (pomade, etc)
- Loyalty card punching
- Wait time estimates
- Sanitation compliance
- Tips distribution
- Classic cut style guide

Ask specific questions about their barber, barber shop, barbershop operations, workflows, and specific business needs.$prompt$,
ARRAY['barber', 'barber shop', 'barbershop', 'men''s haircuts', 'traditional barber', 'straight razor']),

('Daycare Center (Home-based)',
$prompt$You are helping a daycare center (home-based) set up their software modules. Focus on:
- Limited enrollment capacity tracking
- Part-time/drop-in scheduling
- Meal program compliance
- Nap time scheduling
- Home inspection requirements
- Helper/assistant scheduling
- Sibling discount management
- Tax deduction tracking
- Activity supply inventory
- Parent communication logs
- Emergency drill documentation
- Mixed age group activities

Ask specific questions about their home daycare, family daycare, in-home childcare operations, workflows, and specific business needs.$prompt$,
ARRAY['home daycare', 'family daycare', 'in-home childcare', 'home childcare', 'family child care']),

('Snow Removal Service',
$prompt$You are helping a snow removal service set up their software modules. Focus on:
- Weather monitoring and triggers
- Route prioritization (commercial first)
- Salt/sand inventory tracking
- Equipment maintenance schedules
- Seasonal contract management
- Per-push vs seasonal billing
- Liability photo documentation
- On-call driver scheduling
- Slip and fall prevention logs
- Property damage procedures
- Municipal contract compliance
- Pre-treatment scheduling

Ask specific questions about their snow removal, snow plowing, snow plow operations, workflows, and specific business needs.$prompt$,
ARRAY['snow removal', 'snow plowing', 'snow plow', 'ice management', 'winter services', 'salting service']),

('Vintage/Antique Store',
$prompt$You are helping a vintage/antique store set up their software modules. Focus on:
- Item provenance documentation
- Consignment seller tracking
- Estate sale acquisitions
- Authentication records
- Auction platform integration
- Booth rental management
- Appraisal documentation
- Layaway programs
- Dealer network contacts
- Restoration cost tracking
- Market value research
- Photo catalog system

Ask specific questions about their antique store, vintage shop, antiques operations, workflows, and specific business needs.$prompt$,
ARRAY['antique store', 'vintage shop', 'antiques', 'collectibles', 'antique dealer', 'vintage boutique']),

('Tattoo Removal',
$prompt$You are helping a tattoo removal set up their software modules. Focus on:
- Treatment session tracking
- Laser settings per session
- Photo progress documentation
- Skin type assessments
- Package pricing management
- Consultation to treatment workflow
- Aftercare compliance tracking
- Equipment maintenance logs
- Fitzpatrick scale documentation
- Pain management protocols
- Results expectation setting
- Multi-session scheduling

Ask specific questions about their tattoo removal, laser tattoo removal, tattoo removal clinic operations, workflows, and specific business needs.$prompt$,
ARRAY['tattoo removal', 'laser tattoo removal', 'tattoo removal clinic', 'ink removal']),

('Parking Lot Striping',
$prompt$You are helping a parking lot striping set up their software modules. Focus on:
- Lot measurement tools
- ADA compliance checking
- Paint quantity calculations
- Weather window scheduling
- Stencil inventory
- Night work coordination
- Property manager relationships
- Sealcoating service tracking
- Reflective bead inventory
- Municipality specifications
- Warranty tracking
- Seasonal scheduling

Ask specific questions about their parking lot striping, line striping, pavement marking operations, workflows, and specific business needs.$prompt$,
ARRAY['parking lot striping', 'line striping', 'pavement marking', 'parking lot painting', 'striping contractor']),

('Bookstore (Independent)',
$prompt$You are helping a bookstore (independent) set up their software modules. Focus on:
- ISBN inventory management
- Special order tracking
- Author event planning
- Book club coordination
- Consignment book tracking
- Staff recommendation system
- Publisher return windows
- Reading series scheduling
- Loyalty program management
- Used book buy-back pricing
- Category sales analysis
- Community partnership events

Ask specific questions about their bookstore, book shop, independent bookstore operations, workflows, and specific business needs.$prompt$,
ARRAY['bookstore', 'book shop', 'independent bookstore', 'book seller', 'used bookstore']),

('Ice Cream Shop',
$prompt$You are helping a ice cream shop set up their software modules. Focus on:
- Flavor rotation scheduling
- Batch production tracking
- Allergen management
- Cake order system
- Party booking management
- Seasonal flavor planning
- Topping inventory
- Temperature logging
- Catering cooler tracking
- Recipe scaling
- Sampling policies
- Loyalty punch cards

Ask specific questions about their ice cream shop, ice cream parlor, gelato operations, workflows, and specific business needs.$prompt$,
ARRAY['ice cream shop', 'ice cream parlor', 'gelato', 'frozen yogurt', 'ice cream store', 'creamery']),

('Shoe Repair',
$prompt$You are helping a shoe repair set up their software modules. Focus on:
- Repair ticket system
- Work type pricing
- Material inventory (soles, heels)
- Customer notification system
- Unclaimed item procedures
- Quality guarantee tracking
- Leather type identification
- Special order parts
- Dyeing service records
- Orthopedic work notes
- Rush service pricing
- Equipment maintenance

Ask specific questions about their shoe repair, cobbler, shoe service operations, workflows, and specific business needs.$prompt$,
ARRAY['shoe repair', 'cobbler', 'shoe service', 'leather repair', 'boot repair', 'heel repair']),

('Watch Repair',
$prompt$You are helping a watch repair set up their software modules. Focus on:
- Movement type database
- Parts ordering by caliber
- Estimate documentation
- Pressure testing records
- Service interval tracking
- Vintage parts sourcing
- Battery inventory by size
- Warranty tracking
- High-value item procedures
- Shipping insurance
- Certification maintenance
- Customer collection records

Ask specific questions about their watch repair, watchmaker, timepiece repair operations, workflows, and specific business needs.$prompt$,
ARRAY['watch repair', 'watchmaker', 'timepiece repair', 'watch service', 'horology', 'clock repair']),

('Fence Installation',
$prompt$You are helping a fence installation set up their software modules. Focus on:
- Property line documentation
- Material calculations by linear foot
- Permit requirements by area
- HOA approval tracking
- Utility marking coordination
- Gate automation options
- Post-hole depth requirements
- Neighbor notification tracking
- Warranty terms by material
- Concrete quantity calculations
- Rental equipment scheduling
- Grade/slope adjustments

Ask specific questions about their fence installer, fence contractor, fencing operations, workflows, and specific business needs.$prompt$,
ARRAY['fence installer', 'fence contractor', 'fencing', 'fence company', 'fence builder']),

('Upholstery Shop',
$prompt$You are helping a upholstery shop set up their software modules. Focus on:
- Fabric inventory and samples
- Furniture measurements/patterns
- Foam density options
- Work order photo documentation
- Antique handling procedures
- Commercial vs residential pricing
- Delivery scheduling
- Frame repair notes
- Customer fabric tracking
- Spring and webbing inventory
- Fire retardant compliance
- Design consultation notes

Ask specific questions about their upholstery, reupholstery, furniture repair operations, workflows, and specific business needs.$prompt$,
ARRAY['upholstery', 'reupholstery', 'furniture repair', 'upholsterer', 'fabric repair', 'furniture restoration']),

('Gunsmith',
$prompt$You are helping a gunsmith set up their software modules. Focus on:
- ATF compliance and bound book
- Background check processing
- Firearm intake procedures
- Parts compatibility database
- Custom work documentation
- Test firing records
- Serialized part tracking
- Customer firearm inventory
- Transfer procedures
- Warranty work tracking
- Ammunition testing logs
- Safe storage compliance

Ask specific questions about their gunsmith, gun repair, firearm service operations, workflows, and specific business needs.$prompt$,
ARRAY['gunsmith', 'gun repair', 'firearm service', 'gun shop repair', 'firearms smithing']),

('Awning/Canopy Installation',
$prompt$You are helping a awning/canopy installation set up their software modules. Focus on:
- Wind load calculations
- Fabric selection and warranty
- Motorization options
- Building attachment permits
- Custom graphics pricing
- Seasonal removal service
- Commercial branding projects
- Frame welding specifications
- Weather sensor integration
- Maintenance contracts
- Color fade warranties
- Historic district compliance

Ask specific questions about their awning installer, canopy installation, shade structures operations, workflows, and specific business needs.$prompt$,
ARRAY['awning installer', 'canopy installation', 'shade structures', 'awning company', 'retractable awnings']),

('Engraving Service',
$prompt$You are helping a engraving service set up their software modules. Focus on:
- Material compatibility matrix
- Font and design library
- Rush order pricing
- Corporate account templates
- Trophy assembly components
- Laser settings by material
- Quantity discount structures
- Artwork file management
- Plaque mounting options
- Gift message tracking
- Shipping coordination
- Sample approval process

Ask specific questions about their engraving, engraver, trophy shop operations, workflows, and specific business needs.$prompt$,
ARRAY['engraving', 'engraver', 'trophy shop', 'awards', 'personalization', 'laser engraving']),

('Brew Pub/Microbrewery',
$prompt$You are helping a brew pub/microbrewery set up their software modules. Focus on:
- Batch tracking and recipes
- TTB compliance reporting
- Keg inventory and tracking
- Taproom rotation scheduling
- Grain and hop inventory
- Fermentation scheduling
- Distribution tracking
- Tours and tasting management
- Merchandise inventory
- Food pairing menus
- Crowler/growler sales
- Event space booking

Ask specific questions about their brewery, brewpub, microbrewery operations, workflows, and specific business needs.$prompt$,
ARRAY['brewery', 'brewpub', 'microbrewery', 'craft brewery', 'beer brewing', 'taproom']),

('Consignment Shop',
$prompt$You are helping a consignment shop set up their software modules. Focus on:
- Consignor account management
- Item aging and markdown schedules
- Split payment processing
- Unsold item procedures
- Seasonal rotation policies
- Space/rack assignments
- Price tag generation
- Consignor payout schedules
- Donation procedures
- High-value item security
- Quality standards enforcement
- Monthly statement generation

Ask specific questions about their consignment shop, consignment store, resale shop operations, workflows, and specific business needs.$prompt$,
ARRAY['consignment shop', 'consignment store', 'resale shop', 'second hand', 'thrift boutique']),

('Batting Cage/Golf Range',
$prompt$You are helping a batting cage/golf range set up their software modules. Focus on:
- Token/card system management
- Machine maintenance schedules
- League scheduling
- Lesson booking system
- Equipment rental tracking
- Birthday party packages
- Membership programs
- Ball inventory management
- Safety equipment check-out
- Lighting timer systems
- Weather closure policies
- Pro shop inventory

Ask specific questions about their batting cage, driving range, golf range operations, workflows, and specific business needs.$prompt$,
ARRAY['batting cage', 'driving range', 'golf range', 'batting facility', 'hitting facility', 'practice facility']),

('Print Shop (Small)',
$prompt$You are helping a print shop (small) set up their software modules. Focus on:
- Job ticket workflow
- Paper inventory by type/size
- Color matching records
- Bindery service tracking
- Rush job scheduling
- File preflight checking
- Proof approval system
- Outsourced work tracking
- Copyright compliance
- Mailing service coordination
- Wide format calculations
- Design service time tracking

Ask specific questions about their print shop, printing service, copy center operations, workflows, and specific business needs.$prompt$,
ARRAY['print shop', 'printing service', 'copy center', 'printer', 'quick print', 'printing company']);

COMMIT;