BEGIN;

-- Migration: Add conversation threads system
-- Rollback: See 005_conversation_threads.down.sql

-- Create conversation threads table
CREATE TABLE vertoie.conversation_threads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES vertoie.organizations(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    business_example_id UUID REFERENCES vertoie.business_prompt_examples(id),
    token_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for efficient querying
CREATE INDEX idx_conversation_threads_org_id ON vertoie.conversation_threads(organization_id);
CREATE INDEX idx_conversation_threads_active ON vertoie.conversation_threads(organization_id, is_active);
CREATE INDEX idx_conversation_threads_updated ON vertoie.conversation_threads(organization_id, updated_at DESC);

-- Add thread_id to existing conversations table
ALTER TABLE vertoie.organization_business_context_conversations 
ADD COLUMN thread_id UUID REFERENCES vertoie.conversation_threads(id) ON DELETE CASCADE,
ADD COLUMN token_count INTEGER DEFAULT 0;

-- Create index for thread-based conversation queries
CREATE INDEX idx_conversations_thread_id ON vertoie.organization_business_context_conversations(thread_id, message_timestamp);

-- Update existing conversations to have a default thread
-- Create a default thread for each organization that has conversations
INSERT INTO vertoie.conversation_threads (organization_id, title, is_active)
SELECT DISTINCT 
    organization_id,
    'Initial Conversation',
    true
FROM vertoie.organization_business_context_conversations
WHERE NOT EXISTS (
    SELECT 1 FROM vertoie.conversation_threads 
    WHERE conversation_threads.organization_id = organization_business_context_conversations.organization_id
);

-- Link existing conversations to their organization's thread
UPDATE vertoie.organization_business_context_conversations 
SET thread_id = (
    SELECT id FROM vertoie.conversation_threads 
    WHERE conversation_threads.organization_id = organization_business_context_conversations.organization_id
    LIMIT 1
)
WHERE thread_id IS NULL;

COMMIT; 