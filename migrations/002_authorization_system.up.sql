-- Migration 002: Authorization System for Customer Applications
-- Schema for role-based permissions and user overrides in generated apps

BEGIN;

-- Authorization roles template (for customer apps)
CREATE TABLE IF NOT EXISTS vertoie.auth_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL,
    description TEXT,
    is_template BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Role permissions template (module-based permissions)
CREATE TABLE IF NOT EXISTS vertoie.auth_role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID NOT NULL REFERENCES vertoie.auth_roles(id) ON DELETE CASCADE,
    module VARCHAR(50) NOT NULL,
    can_read BOOLEAN DEFAULT false,
    can_write BOOLEAN DEFAULT false,
    can_delete BOOLEAN DEFAULT false,
    can_approve BOOLEAN DEFAULT false,
    can_export BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, module)
);

-- User role assignments template
CREATE TABLE IF NOT EXISTS vertoie.auth_user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    role_id UUID NOT NULL REFERENCES vertoie.auth_roles(id) ON DELETE CASCADE,
    assigned_by UUID,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role_id)
);

-- User permission overrides template (can grant OR restrict)
CREATE TABLE IF NOT EXISTS vertoie.auth_user_overrides (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    module VARCHAR(50) NOT NULL,
    permission VARCHAR(50) NOT NULL, -- 'read', 'write', 'delete', 'approve', 'export'
    granted BOOLEAN NOT NULL, -- true = grant, false = restrict
    resource_type VARCHAR(50) NOT NULL, -- 'record', 'field', 'module', 'conditional'
    resource_id UUID, -- specific record ID (optional)
    conditions JSONB, -- {'customer_id': 123, 'status': 'pending', 'amount_lt': 1000}
    expires_at TIMESTAMP,
    created_by UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Override templates for common patterns
CREATE TABLE IF NOT EXISTS vertoie.auth_override_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    override_type VARCHAR(50) NOT NULL, -- 'record', 'field', 'conditional', 'territory'
    template_config JSONB NOT NULL, -- {'module': 'invoice', 'conditions': {'status': 'pending'}}
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Permission audit log
CREATE TABLE IF NOT EXISTS vertoie.auth_permission_audit (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    action VARCHAR(50) NOT NULL, -- 'role_assigned', 'override_granted', 'permission_denied'
    target_user_id UUID, -- user whose permissions were changed
    module VARCHAR(50),
    permission VARCHAR(50),
    resource_type VARCHAR(50),
    resource_id UUID,
    old_value JSONB,
    new_value JSONB,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_auth_roles_name ON vertoie.auth_roles(name);
CREATE INDEX IF NOT EXISTS idx_auth_roles_is_template ON vertoie.auth_roles(is_template);
CREATE INDEX IF NOT EXISTS idx_auth_roles_is_active ON vertoie.auth_roles(is_active);
CREATE INDEX IF NOT EXISTS idx_auth_role_permissions_role_id ON vertoie.auth_role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_auth_role_permissions_module ON vertoie.auth_role_permissions(module);
CREATE INDEX IF NOT EXISTS idx_auth_user_roles_user_id ON vertoie.auth_user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_user_roles_role_id ON vertoie.auth_user_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_auth_user_roles_expires_at ON vertoie.auth_user_roles(expires_at);
CREATE INDEX IF NOT EXISTS idx_auth_user_overrides_user_id ON vertoie.auth_user_overrides(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_user_overrides_module ON vertoie.auth_user_overrides(module);
CREATE INDEX IF NOT EXISTS idx_auth_user_overrides_expires_at ON vertoie.auth_user_overrides(expires_at);
CREATE INDEX IF NOT EXISTS idx_auth_permission_audit_user_id ON vertoie.auth_permission_audit(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_permission_audit_created_at ON vertoie.auth_permission_audit(created_at);

-- Insert default role templates
INSERT INTO vertoie.auth_roles (name, description, is_template) VALUES
    ('Owner', 'Full access to everything in the application', true),
    ('Admin', 'Full access except billing and system deletion', true),
    ('User', 'Module-based permissions with optional overrides', true),
    ('Viewer', 'Read-only access to assigned modules', true)
ON CONFLICT DO NOTHING;

-- Insert default permissions for roles
INSERT INTO vertoie.auth_role_permissions (role_id, module, can_read, can_write, can_delete, can_approve, can_export) 
SELECT 
    r.id,
    'all',
    CASE WHEN r.name IN ('Owner', 'Admin') THEN true ELSE false END,
    CASE WHEN r.name IN ('Owner', 'Admin') THEN true ELSE false END,
    CASE WHEN r.name = 'Owner' THEN true ELSE false END,
    CASE WHEN r.name IN ('Owner', 'Admin') THEN true ELSE false END,
    CASE WHEN r.name IN ('Owner', 'Admin') THEN true ELSE false END
FROM vertoie.auth_roles r
WHERE r.name IN ('Owner', 'Admin', 'User', 'Viewer')
ON CONFLICT DO NOTHING;

-- Insert common override templates
INSERT INTO vertoie.auth_override_templates (name, description, override_type, template_config) VALUES
    ('Single Record Access', 'User can only access one specific record', 'record', 
     '{"description": "Restrict user to single record", "example": {"resource_id": "uuid"}}'),
    ('Customer Assignment', 'User can only see their assigned customers', 'conditional',
     '{"description": "Filter by assigned customer", "example": {"customer_id": "assigned_customer_id"}}'),
    ('Status Filter', 'User can only see records with specific status', 'conditional',
     '{"description": "Filter by record status", "example": {"status": "pending"}}'),
    ('Amount Limit', 'User can only approve records under certain amount', 'conditional',
     '{"description": "Filter by amount threshold", "example": {"amount_lt": 1000}}'),
    ('Field Restriction', 'User cannot see specific fields', 'field',
     '{"description": "Hide specific fields", "example": {"fields": ["price", "cost"]}}')
ON CONFLICT DO NOTHING;

COMMIT;
