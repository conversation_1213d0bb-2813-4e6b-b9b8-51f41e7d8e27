-- Migration 006: Recommended Modules Storage
-- Add table for storing AI-recommended modules for organizations

BEGIN;

-- Organization recommended modules
CREATE TABLE IF NOT EXISTS vertoie.organization_recommended_modules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES vertoie.organizations(id) ON DELETE CASCADE,
    
    -- Module details from AI response
    name VARCHAR(255) NOT NULL,
    category VARCHAR(50) NOT NULL, -- core, efficiency, growth, compliance, analytics
    description TEXT NOT NULL,
    key_benefit TEXT NOT NULL,
    effort_to_value VARCHAR(20) NOT NULL, -- high, medium, low
    recommendation_score INTEGER, -- 1-10 rating
    score_reason TEXT,
    
    -- Module status
    status VARCHAR(20) DEFAULT 'recommended', -- recommended, accepted, rejected, modified
    user_feedback TEXT, -- User notes/modifications
    
    -- Metadata
    first_recommended_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    -- Constraints
    UNIQUE(organization_id, name)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_org_recommended_modules_org_id ON vertoie.organization_recommended_modules(organization_id);
CREATE INDEX IF NOT EXISTS idx_org_recommended_modules_status ON vertoie.organization_recommended_modules(status);
CREATE INDEX IF NOT EXISTS idx_org_recommended_modules_category ON vertoie.organization_recommended_modules(category);
CREATE INDEX IF NOT EXISTS idx_org_recommended_modules_score ON vertoie.organization_recommended_modules(recommendation_score);
CREATE INDEX IF NOT EXISTS idx_org_recommended_modules_deleted_at ON vertoie.organization_recommended_modules(deleted_at);

COMMIT; 