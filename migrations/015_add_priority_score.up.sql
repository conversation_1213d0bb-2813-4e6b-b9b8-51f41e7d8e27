-- Add priority_score column to organization_recommended_modules table
-- This field stores the calculated final priority score that combines AI recommendations with business logic

ALTER TABLE vertoie.organization_recommended_modules 
ADD COLUMN priority_score INTEGER;

-- Add index for better query performance on the new ordering
CREATE INDEX idx_org_modules_priority_ordering 
ON vertoie.organization_recommended_modules (
    organization_id, 
    category, 
    priority_score DESC, 
    recommendation_score DESC, 
    created_at DESC
) WHERE deleted_at IS NULL;

-- Add comment explaining the field
COMMENT ON COLUMN vertoie.organization_recommended_modules.priority_score IS 
'Calculated final priority score combining AI recommendation with business logic for core module prioritization';
