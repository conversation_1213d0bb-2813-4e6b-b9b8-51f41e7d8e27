-- Migration 001: Initial Vertoie Schema
-- Complete schema for AI-generated business software builder platform

BEGIN;

-- Create vertoie schema for platform data
CREATE SCHEMA IF NOT EXISTS vertoie;

-- Platform users (business owners)
CREATE TABLE IF NOT EXISTS vertoie.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Organizations (businesses)
CREATE TABLE IF NOT EXISTS vertoie.organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    custom_domain VARCHAR(255) UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- User-Organization memberships
CREATE TABLE IF NOT EXISTS vertoie.organization_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES vertoie.users(id),
    organization_id UUID NOT NULL REFERENCES vertoie.organizations(id),
    role VARCHAR(50) NOT NULL DEFAULT 'member',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    UNIQUE(user_id, organization_id)
);

-- Authentication tokens (magic links, invitations, API keys)
CREATE TABLE IF NOT EXISTS vertoie.auth_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(50) NOT NULL,
    user_id UUID REFERENCES vertoie.users(id),
    email VARCHAR(255) NOT NULL,
    organization_id UUID REFERENCES vertoie.organizations(id),
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Subscription plans
CREATE TABLE IF NOT EXISTS vertoie.plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    price_cents INTEGER NOT NULL,
    billing_cycle VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    features JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Plan add-ons
CREATE TABLE IF NOT EXISTS vertoie.plan_addons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    addon_type VARCHAR(50) NOT NULL,
    quantity INTEGER NOT NULL,
    price_cents INTEGER NOT NULL,
    billing_cycle VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Organization subscriptions to plans
CREATE TABLE IF NOT EXISTS vertoie.organization_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES vertoie.organizations(id),
    plan_id UUID NOT NULL REFERENCES vertoie.plans(id),
    status VARCHAR(50) NOT NULL,
    starts_at TIMESTAMP NOT NULL,
    ends_at TIMESTAMP,
    external_subscription_id VARCHAR(255),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Organization subscriptions to add-ons
CREATE TABLE IF NOT EXISTS vertoie.organization_addon_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES vertoie.organizations(id),
    addon_id UUID NOT NULL REFERENCES vertoie.plan_addons(id),
    quantity INTEGER NOT NULL,
    status VARCHAR(50) NOT NULL,
    starts_at TIMESTAMP NOT NULL,
    ends_at TIMESTAMP,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Activity/audit log
CREATE TABLE IF NOT EXISTS vertoie.activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES vertoie.users(id),
    organization_id UUID REFERENCES vertoie.organizations(id),
    action VARCHAR(255) NOT NULL,
    resource_type VARCHAR(100),
    resource_id UUID,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Platform configuration
CREATE TABLE IF NOT EXISTS vertoie.platform_config (
    key VARCHAR(255) PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON vertoie.users(email);
CREATE INDEX IF NOT EXISTS idx_users_deleted_at ON vertoie.users(deleted_at);
CREATE INDEX IF NOT EXISTS idx_organizations_slug ON vertoie.organizations(slug);
CREATE INDEX IF NOT EXISTS idx_organizations_custom_domain ON vertoie.organizations(custom_domain);
CREATE INDEX IF NOT EXISTS idx_organizations_deleted_at ON vertoie.organizations(deleted_at);
CREATE INDEX IF NOT EXISTS idx_organization_users_user_id ON vertoie.organization_users(user_id);
CREATE INDEX IF NOT EXISTS idx_organization_users_org_id ON vertoie.organization_users(organization_id);
CREATE INDEX IF NOT EXISTS idx_auth_tokens_email ON vertoie.auth_tokens(email);
CREATE INDEX IF NOT EXISTS idx_auth_tokens_expires_at ON vertoie.auth_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_auth_tokens_used_at ON vertoie.auth_tokens(used_at);
CREATE INDEX IF NOT EXISTS idx_plans_slug ON vertoie.plans(slug);
CREATE INDEX IF NOT EXISTS idx_plans_is_active ON vertoie.plans(is_active);
CREATE INDEX IF NOT EXISTS idx_plan_addons_slug ON vertoie.plan_addons(slug);
CREATE INDEX IF NOT EXISTS idx_plan_addons_is_active ON vertoie.plan_addons(is_active);
CREATE INDEX IF NOT EXISTS idx_org_subscriptions_org_id ON vertoie.organization_subscriptions(organization_id);
CREATE INDEX IF NOT EXISTS idx_org_subscriptions_status ON vertoie.organization_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_org_addon_subscriptions_org_id ON vertoie.organization_addon_subscriptions(organization_id);
CREATE INDEX IF NOT EXISTS idx_activities_user_id ON vertoie.activities(user_id);
CREATE INDEX IF NOT EXISTS idx_activities_org_id ON vertoie.activities(organization_id);
CREATE INDEX IF NOT EXISTS idx_activities_created_at ON vertoie.activities(created_at);

COMMIT; 