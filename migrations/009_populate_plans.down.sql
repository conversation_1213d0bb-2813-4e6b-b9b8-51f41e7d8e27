-- Migration 009 Down: Rollback plan population and restore addon system

BEGIN;

-- Clear the new plans
DELETE FROM vertoie.plans;

-- Recreate addon tables
CREATE TABLE IF NOT EXISTS vertoie.plan_addons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    addon_type VARCHAR(50) NOT NULL,
    quantity INTEGER NOT NULL,
    price_cents INTEGER NOT NULL,
    billing_cycle VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS vertoie.organization_addon_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES vertoie.organizations(id),
    addon_id UUID NOT NULL REFERENCES vertoie.plan_addons(id),
    quantity INTEGER NOT NULL,
    status VARCHAR(50) NOT NULL,
    starts_at TIMESTAMP NOT NULL,
    ends_at TIMESTAMP,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Recreate addon indexes
CREATE INDEX IF NOT EXISTS idx_plan_addons_slug ON vertoie.plan_addons(slug);
CREATE INDEX IF NOT EXISTS idx_plan_addons_is_active ON vertoie.plan_addons(is_active);
CREATE INDEX IF NOT EXISTS idx_org_addon_subscriptions_org_id ON vertoie.organization_addon_subscriptions(organization_id);

COMMIT; 