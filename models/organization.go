package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Organization represents a business organization
type Organization struct {
	ID              uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name            string         `json:"name" gorm:"size:255;not null"`
	Slug            string         `json:"slug" gorm:"size:100;not null;uniqueIndex"`
	CustomDomain    string         `json:"custom_domain" gorm:"size:255;uniqueIndex"`
	Description     string         `json:"description" gorm:"type:text"`
	ExamplePromptID *uuid.UUID     `json:"example_prompt_id" gorm:"type:uuid"`
	AppStatus       string         `json:"app_status" gorm:"size:20;default:setup"` // setup, draft, active, paused
	CurrentVersion  int            `json:"current_version" gorm:"default:1"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Users []User `json:"users,omitempty" gorm:"many2many:vertoie.organization_users;foreignKey:ID;joinForeignKey:organization_id;References:ID;joinReferences:user_id"`
}

// TableName specifies the table name for Organization model in vertoie schema
func (Organization) TableName() string {
	return "vertoie.organizations"
}

// App status constants
const (
	AppStatusSetup  = "setup"  // In onboarding
	AppStatusDraft  = "draft"  // Generated, testing
	AppStatusActive = "active" // Live, billing active
	AppStatusPaused = "paused" // Temporarily disabled
)

// IsInSetup returns true if organization is still in onboarding
func (o *Organization) IsInSetup() bool {
	return o.AppStatus == AppStatusSetup
}

// HasDraftApp returns true if organization has a generated draft app
func (o *Organization) HasDraftApp() bool {
	return o.AppStatus == AppStatusDraft || o.AppStatus == AppStatusActive
}

// IsActive returns true if organization has an active, billing-enabled app
func (o *Organization) IsActive() bool {
	return o.AppStatus == AppStatusActive
}
