package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BusinessContext represents the business context for an organization
type BusinessContext struct {
	ID              uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrganizationID  uuid.UUID      `json:"organization_id" gorm:"type:uuid;not null"`
	BusinessType    string         `json:"business_type"`
	Industry        string         `json:"industry"`
	KeyProcesses    []string       `json:"key_processes" gorm:"type:jsonb"`
	CustomerTypes   []string       `json:"customer_types" gorm:"type:jsonb"`
	BusinessSize    string         `json:"business_size"`
	RevenueModel    string         `json:"revenue_model"`
	GeographicScope string         `json:"geographic_scope"`
	KeyPainPoints   []string       `json:"key_pain_points" gorm:"type:jsonb"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Organization  Organization                  `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	Conversations []BusinessContextConversation `json:"conversations,omitempty" gorm:"foreignKey:OrganizationID"`
}

// TableName specifies the table name for BusinessContext model in vertoie schema
func (BusinessContext) TableName() string {
	return "vertoie.organization_business_context"
}

// IsComplete checks if the business context has all required fields filled
func (bc *BusinessContext) IsComplete() bool {
	return bc.BusinessType != "" &&
		bc.Industry != "" &&
		len(bc.KeyProcesses) > 0 &&
		len(bc.CustomerTypes) > 0
}
