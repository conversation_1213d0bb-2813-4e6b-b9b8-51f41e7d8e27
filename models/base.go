package models

// Base model with common fields for all entities
// Copied from backend/models/base.go

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

type BaseModel struct {
	ID        string     `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()" db:"id"`
	CreatedAt time.Time  `json:"created_at" gorm:"autoCreateTime" db:"created_at"`
	UpdatedAt time.Time  `json:"updated_at" gorm:"autoUpdateTime" db:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at,omitempty" gorm:"index" db:"deleted_at"`
}

type JSONB map[string]interface{}

func (j JSONB) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

func (j *JSONB) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSONB", value)
	}
	return json.Unmarshal(bytes, j)
}
