package models

import (
	"database/sql/driver"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// StringArray is a custom type for handling PostgreSQL text arrays
type StringArray []string

// Value implements the driver.Valuer interface for writing to database
func (a StringArray) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	// Convert to PostgreSQL array format: {item1,item2,item3}
	var quoted []string
	for _, s := range a {
		// Simple escaping - replace quotes with double quotes
		escaped := strings.ReplaceAll(s, `"`, `""`)
		quoted = append(quoted, `"`+escaped+`"`)
	}
	return "{" + strings.Join(quoted, ",") + "}", nil
}

// <PERSON>an implements the sql.Scanner interface for reading from database
func (a *StringArray) Scan(value interface{}) error {
	if value == nil {
		*a = nil
		return nil
	}

	str, ok := value.(string)
	if !ok {
		*a = StringArray{}
		return nil
	}

	// Parse PostgreSQL array format: {item1,item2,item3}
	str = strings.Trim(str, "{}")
	if str == "" {
		*a = StringArray{}
		return nil
	}

	// Split by comma and clean up quotes
	parts := strings.Split(str, ",")
	result := make(StringArray, 0, len(parts))

	for _, part := range parts {
		// Remove surrounding quotes and unescape
		cleaned := strings.Trim(part, `"`)
		cleaned = strings.ReplaceAll(cleaned, `""`, `"`)
		if cleaned != "" {
			result = append(result, cleaned)
		}
	}

	*a = result
	return nil
}

// BusinessPromptExample represents a business prompt example with fuzzy matching tags
type BusinessPromptExample struct {
	ID        uuid.UUID   `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name      string      `json:"name" gorm:"not null"`
	Prompt    string      `json:"prompt" gorm:"type:text;not null"`
	Tags      StringArray `json:"tags" gorm:"type:text[]"`
	CreatedAt time.Time   `json:"created_at"`
	UpdatedAt time.Time   `json:"updated_at"`
}

// TableName returns the table name for BusinessPromptExample
func (BusinessPromptExample) TableName() string {
	return "vertoie.business_prompt_examples"
}

// FuzzyMatch calculates a fuzzy match score between input text and tags
func (bpe *BusinessPromptExample) FuzzyMatch(input string) float64 {
	if input == "" {
		return 0.0
	}

	input = strings.ToLower(input)
	maxScore := 0.0

	for _, tag := range []string(bpe.Tags) {
		tag = strings.ToLower(tag)

		// Exact match gets highest score
		if input == tag {
			return 1.0
		}

		// Check if input contains tag or tag contains input
		if strings.Contains(input, tag) || strings.Contains(tag, input) {
			score := float64(min(len(tag), len(input))) / float64(max(len(tag), len(input)))
			if score > maxScore {
				maxScore = score
			}
		}

		// Check for word-level matches
		inputWords := strings.Fields(input)
		tagWords := strings.Fields(tag)

		wordMatches := 0
		totalWords := max(len(inputWords), len(tagWords))

		for _, inputWord := range inputWords {
			for _, tagWord := range tagWords {
				if inputWord == tagWord {
					wordMatches++
					break
				}
				// Partial word match
				if strings.Contains(inputWord, tagWord) || strings.Contains(tagWord, inputWord) {
					wordMatches++
					break
				}
			}
		}

		if totalWords > 0 {
			wordScore := float64(wordMatches) / float64(totalWords)
			if wordScore > maxScore {
				maxScore = wordScore
			}
		}
	}

	return maxScore
}

// FuzzyMatchResult represents the result of a fuzzy match operation
type FuzzyMatchResult struct {
	Example *BusinessPromptExample `json:"example"`
	Score   float64                `json:"score"`
}

// BusinessPromptExampleService provides methods for working with business prompt examples
type BusinessPromptExampleService struct {
	db *gorm.DB
}

// NewBusinessPromptExampleService creates a new service instance
func NewBusinessPromptExampleService(db *gorm.DB) *BusinessPromptExampleService {
	return &BusinessPromptExampleService{db: db}
}

// ExtractKeywords extracts keywords from user input for searching
func (s *BusinessPromptExampleService) ExtractKeywords(input string) []string {
	input = strings.ToLower(input)
	// Remove common words that don't help with matching
	stopWords := map[string]bool{
		"i": true, "am": true, "is": true, "are": true, "a": true, "an": true, "the": true,
		"run": true, "have": true, "own": true, "my": true, "we": true, "our": true,
		"business": true, "company": true, "and": true, "or": true, "to": true, "for": true,
	}

	words := strings.Fields(input)
	var keywords []string

	for _, word := range words {
		word = strings.Trim(word, ".,!?;:")
		if len(word) > 2 && !stopWords[word] {
			keywords = append(keywords, word)
		}
	}

	return keywords
}

// FindMatchingExamples queries the database for examples matching the given keywords
func (s *BusinessPromptExampleService) FindMatchingExamples(input string, limit int) ([]FuzzyMatchResult, error) {
	keywords := s.ExtractKeywords(input)
	if len(keywords) == 0 {
		return nil, nil
	}

	// Build query to search for examples where tags contain any of the keywords
	var examples []BusinessPromptExample
	query := s.db.Where("1=0") // Start with impossible condition

	for _, keyword := range keywords {
		// Use PostgreSQL array contains operator with case-insensitive matching
		query = query.Or("EXISTS (SELECT 1 FROM unnest(tags) AS tag WHERE LOWER(tag) LIKE ?)", "%"+keyword+"%")
	}

	err := query.Find(&examples).Error
	if err != nil {
		return nil, err
	}

	// Calculate fuzzy match scores and sort
	var results []FuzzyMatchResult
	for i := range examples {
		score := examples[i].FuzzyMatch(input)
		if score > 0.1 { // Minimum threshold
			results = append(results, FuzzyMatchResult{
				Example: &examples[i],
				Score:   score,
			})
		}
	}

	// Sort by score descending
	for i := 0; i < len(results)-1; i++ {
		for j := i + 1; j < len(results); j++ {
			if results[j].Score > results[i].Score {
				results[i], results[j] = results[j], results[i]
			}
		}
	}

	// Limit results
	if limit > 0 && len(results) > limit {
		results = results[:limit]
	}

	return results, nil
}

// FindBestMatch finds the single best matching business prompt example for the given input
func (s *BusinessPromptExampleService) FindBestMatch(input string) (*FuzzyMatchResult, error) {
	results, err := s.FindMatchingExamples(input, 1)
	if err != nil {
		return nil, err
	}

	if len(results) == 0 {
		return nil, nil
	}

	return &results[0], nil
}

// Helper functions
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
