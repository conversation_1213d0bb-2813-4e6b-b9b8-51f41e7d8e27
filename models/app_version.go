package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AppVersion represents a versioned snapshot of an organization's generated application
type AppVersion struct {
	ID               uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrganizationID   uuid.UUID      `json:"organization_id" gorm:"type:uuid;not null"`
	VersionNumber    int            `json:"version_number" gorm:"not null;default:1"`
	SchemaDefinition JSONB          `json:"schema_definition" gorm:"type:jsonb;not null"` // Complete schema snapshot
	SelectedModules  JSONB          `json:"selected_modules" gorm:"type:jsonb;not null"`  // Snapshot of selected modules
	PricingTierID    *uuid.UUID     `json:"pricing_tier_id" gorm:"type:uuid"`
	IsActive         bool           `json:"is_active" gorm:"default:false"`
	IsDraft          bool           `json:"is_draft" gorm:"default:true"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	PricingTier  *PricingTier `json:"pricing_tier,omitempty" gorm:"foreignKey:PricingTierID"`
}

// TableName specifies the table name for AppVersion model in vertoie schema
func (AppVersion) TableName() string {
	return "vertoie.organization_app_versions"
}

// AppVersionService handles operations for app versions
type AppVersionService struct {
	db *gorm.DB
}

// NewAppVersionService creates a new service for app versions
func NewAppVersionService(db *gorm.DB) *AppVersionService {
	return &AppVersionService{db: db}
}

// GetActiveVersion retrieves the active version for an organization
func (s *AppVersionService) GetActiveVersion(organizationID uuid.UUID) (*AppVersion, error) {
	var version AppVersion
	err := s.db.Where("organization_id = ? AND is_active = ? AND deleted_at IS NULL", organizationID, true).
		Preload("PricingTier").
		First(&version).Error
	if err != nil {
		return nil, err
	}
	return &version, nil
}

// GetDraftVersion retrieves the draft version for an organization
func (s *AppVersionService) GetDraftVersion(organizationID uuid.UUID) (*AppVersion, error) {
	var version AppVersion
	err := s.db.Where("organization_id = ? AND is_draft = ? AND deleted_at IS NULL", organizationID, true).
		Preload("PricingTier").
		First(&version).Error
	if err != nil {
		return nil, err
	}
	return &version, nil
}

// GetVersionsForOrganization retrieves all versions for an organization
func (s *AppVersionService) GetVersionsForOrganization(organizationID uuid.UUID) ([]AppVersion, error) {
	var versions []AppVersion
	err := s.db.Where("organization_id = ? AND deleted_at IS NULL", organizationID).
		Preload("PricingTier").
		Order("version_number DESC").
		Find(&versions).Error
	return versions, err
}

// CreateVersion creates a new version for an organization
func (s *AppVersionService) CreateVersion(organizationID uuid.UUID, schemaDefinition, selectedModules JSONB, pricingTierID *uuid.UUID) (*AppVersion, error) {
	// Get the next version number
	var maxVersion int
	s.db.Model(&AppVersion{}).
		Where("organization_id = ?", organizationID).
		Select("COALESCE(MAX(version_number), 0)").
		Scan(&maxVersion)

	version := &AppVersion{
		OrganizationID:   organizationID,
		VersionNumber:    maxVersion + 1,
		SchemaDefinition: schemaDefinition,
		SelectedModules:  selectedModules,
		PricingTierID:    pricingTierID,
		IsDraft:          true,
		IsActive:         false,
	}

	err := s.db.Create(version).Error
	if err != nil {
		return nil, err
	}

	return version, nil
}

// ActivateVersion makes a version active and deactivates others
func (s *AppVersionService) ActivateVersion(versionID uuid.UUID) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Get the version to activate
		var version AppVersion
		if err := tx.Where("id = ?", versionID).First(&version).Error; err != nil {
			return err
		}

		// Deactivate all other versions for this organization
		if err := tx.Model(&AppVersion{}).
			Where("organization_id = ? AND id != ?", version.OrganizationID, versionID).
			Updates(map[string]interface{}{
				"is_active": false,
				"is_draft":  false,
			}).Error; err != nil {
			return err
		}

		// Activate this version
		return tx.Model(&version).Updates(map[string]interface{}{
			"is_active": true,
			"is_draft":  false,
		}).Error
	})
}

// GetSchemaDefinition safely retrieves schema definition as a map
func (av *AppVersion) GetSchemaDefinition() map[string]interface{} {
	if av.SchemaDefinition == nil {
		return make(map[string]interface{})
	}
	return map[string]interface{}(av.SchemaDefinition)
}

// GetSelectedModules safely retrieves selected modules as a slice
func (av *AppVersion) GetSelectedModules() []map[string]interface{} {
	if av.SelectedModules == nil {
		return make([]map[string]interface{}, 0)
	}

	// Convert JSONB to interface{} first
	data := map[string]interface{}(av.SelectedModules)
	if modules, ok := data["modules"].([]interface{}); ok {
		result := make([]map[string]interface{}, len(modules))
		for i, module := range modules {
			if moduleMap, ok := module.(map[string]interface{}); ok {
				result[i] = moduleMap
			}
		}
		return result
	}

	return make([]map[string]interface{}, 0)
}
