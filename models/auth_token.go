package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AuthToken represents authentication tokens for magic links and sessions
type AuthToken struct {
	ID             uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	TokenHash      string     `json:"-" gorm:"column:token_hash;size:255;uniqueIndex;not null"`
	Type           string     `json:"type" gorm:"size:50;not null"` // "magic_link", "session", etc.
	UserID         uuid.UUID  `json:"user_id" gorm:"type:uuid;not null"`
	Email          string     `json:"email" gorm:"size:255;not null"`
	OrganizationID *uuid.UUID `json:"organization_id,omitempty" gorm:"type:uuid"`
	ExpiresAt      time.Time  `json:"expires_at" gorm:"not null"`
	UsedAt         *time.Time `json:"used_at,omitempty" gorm:"column:used_at"`
	IPAddress      *string    `json:"ip_address,omitempty" gorm:"size:45"`
	UserAgent      *string    `json:"user_agent,omitempty" gorm:"size:500"`
	// We will omit Metadata for now as it's not used in the current logic
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index"`

	// This field is not in the database, it's for returning the raw token to the user
	Token string `json:"token,omitempty" gorm:"-"`
}

// TableName specifies the table name for AuthToken model in vertoie schema
func (AuthToken) TableName() string {
	return "vertoie.auth_tokens"
}
