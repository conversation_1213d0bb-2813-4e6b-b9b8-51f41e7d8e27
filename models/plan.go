package models

import (
	"fmt"
)

// Plan represents a pricing plan/tier
// Uses shared BaseModel and JSONB

type Plan struct {
	BaseModel
	Name         string  `json:"name" db:"name"`
	Slug         string  `json:"slug" db:"slug"`
	Description  *string `json:"description,omitempty" db:"description"`
	PriceCents   int     `json:"price_cents" db:"price_cents"`
	BillingCycle string  `json:"billing_cycle" db:"billing_cycle"` // monthly, yearly, custom
	IsActive     bool    `json:"is_active" db:"is_active"`
	Features     JSONB   `json:"features" db:"features" gorm:"type:jsonb"`
}

func (Plan) TableName() string {
	return "vertoie.plans"
}

// Feature access methods
func (p *Plan) GetFeatureInt(key string) int {
	if p.Features == nil {
		return 0
	}
	if val, exists := p.Features[key]; exists {
		switch v := val.(type) {
		case int:
			return v
		case float64:
			return int(v)
		}
	}
	return 0
}

func (p *Plan) GetFeatureBool(key string) bool {
	if p.Features == nil {
		return false
	}
	if val, exists := p.Features[key]; exists {
		if b, ok := val.(bool); ok {
			return b
		}
	}
	return false
}

func (p *Plan) IsUnlimited(key string) bool {
	return p.GetFeatureInt(key) == -1
}

// Module and user limits
func (p *Plan) GetMaxModules() int {
	return p.GetFeatureInt("max_modules")
}

func (p *Plan) GetMaxUsers() int {
	return p.GetFeatureInt("max_users")
}

func (p *Plan) GetAIGenerations() int {
	return p.GetFeatureInt("ai_generations")
}

func (p *Plan) GetActionsPerMonth() int {
	return p.GetFeatureInt("actions_per_month")
}

// Communication capabilities
func (p *Plan) SupportsEmail() bool {
	return p.GetFeatureBool("email_enabled")
}

func (p *Plan) SupportsSMS() bool {
	return p.GetFeatureBool("sms_enabled")
}

func (p *Plan) SupportsVoice() bool {
	return p.GetFeatureBool("voice_enabled")
}

// Advanced features
func (p *Plan) SupportsIntegrations() bool {
	return p.GetFeatureBool("integrations_enabled")
}

func (p *Plan) SupportsCustomBranding() bool {
	return p.GetFeatureBool("custom_branding")
}

func (p *Plan) SupportsCustomDomain() bool {
	return p.GetFeatureBool("custom_domain")
}

func (p *Plan) SupportsAPIAccess() bool {
	return p.GetFeatureBool("api_access")
}

func (p *Plan) HasPrioritySupport() bool {
	return p.GetFeatureBool("priority_support")
}

// Validation methods
func (p *Plan) CanSelectModules(count int) bool {
	limit := p.GetMaxModules()
	return limit == -1 || count <= limit
}

func (p *Plan) CanAddUsers(count int) bool {
	limit := p.GetMaxUsers()
	return limit == -1 || count <= limit
}

func (p *Plan) CanUseAIGenerations(count int) bool {
	limit := p.GetAIGenerations()
	return limit == -1 || count <= limit
}

// Display methods
func (p *Plan) GetPriceDisplay() string {
	if p.PriceCents == 0 {
		if p.BillingCycle == "custom" {
			return "Custom"
		}
		return "Free"
	}
	return fmt.Sprintf("$%.0f", float64(p.PriceCents)/100)
}

func (p *Plan) GetModulesDisplay() string {
	max := p.GetMaxModules()
	if max == -1 {
		return "Unlimited modules"
	}
	if max == 1 {
		return "1 module"
	}
	return fmt.Sprintf("%d modules", max)
}

func (p *Plan) GetUsersDisplay() string {
	max := p.GetMaxUsers()
	if max == -1 {
		return "Unlimited users"
	}
	if max == 1 {
		return "1 user"
	}
	return fmt.Sprintf("%d users", max)
}

func (p *Plan) GetActionsDisplay() string {
	actions := p.GetActionsPerMonth()
	if actions == -1 {
		return "Unlimited actions"
	}
	if actions >= 1000 {
		return fmt.Sprintf("%dk actions/month", actions/1000)
	}
	return fmt.Sprintf("%d actions/month", actions)
}
