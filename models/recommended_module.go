package models

import (
	"strings"
	"time"

	"github.com/agnivade/levenshtein"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// RecommendedModule represents an AI-recommended module for an organization
type RecommendedModule struct {
	ID                  uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrganizationID      uuid.UUID      `json:"organization_id" gorm:"type:uuid;not null"`
	Name                string         `json:"name" gorm:"size:255;not null"`
	Category            string         `json:"category" gorm:"size:50;not null"`
	Description         string         `json:"description" gorm:"type:text;not null"`
	KeyBenefit          string         `json:"key_benefit" gorm:"type:text;not null"`
	EffortToValue       string         `json:"effort_to_value" gorm:"size:20;not null"`
	RecommendationScore *int           `json:"recommendation_score" gorm:"type:integer"`
	PriorityScore       *int           `json:"priority_score" gorm:"type:integer"` // Calculated final priority score
	ScoreReason         string         `json:"score_reason" gorm:"type:text"`
	Status              string         `json:"status" gorm:"size:20;default:'recommended'"` // recommended, accepted, rejected, modified, custom
	UserFeedback        string         `json:"user_feedback" gorm:"type:text"`
	FirstRecommendedAt  time.Time      `json:"first_recommended_at"`
	LastUpdatedAt       time.Time      `json:"last_updated_at"`
	CreatedAt           time.Time      `json:"created_at"`
	UpdatedAt           time.Time      `json:"updated_at"`
	DeletedAt           gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
}

// TableName specifies the table name for RecommendedModule model in vertoie schema
func (RecommendedModule) TableName() string {
	return "vertoie.organization_recommended_modules"
}

// ModuleStatus constants
const (
	ModuleStatusRecommended = "recommended"
	ModuleStatusAccepted    = "accepted"
	ModuleStatusRejected    = "rejected"
	ModuleStatusModified    = "modified"
	ModuleStatusCustom      = "custom" // User-created modules
)

// ModuleCategory constants
const (
	ModuleCategoryCore       = "core"
	ModuleCategoryEfficiency = "efficiency"
	ModuleCategoryGrowth     = "growth"
	ModuleCategoryCompliance = "compliance"
	ModuleCategoryAnalytics  = "analytics"
	ModuleCategoryFinancial  = "financial"
	ModuleCategoryCustom     = "custom"
)

// IsCustom returns true if this is a user-created custom module
func (rm *RecommendedModule) IsCustom() bool {
	return rm.Status == ModuleStatusCustom
}

// IsAccepted returns true if the user has accepted this module
func (rm *RecommendedModule) IsAccepted() bool {
	return rm.Status == ModuleStatusAccepted
}

// IsRejected returns true if the user has rejected this module
func (rm *RecommendedModule) IsRejected() bool {
	return rm.Status == ModuleStatusRejected
}

// RecommendedModuleService handles operations for recommended modules
type RecommendedModuleService struct {
	db *gorm.DB
}

// NewRecommendedModuleService creates a new service for recommended modules
func NewRecommendedModuleService(db *gorm.DB) *RecommendedModuleService {
	return &RecommendedModuleService{db: db}
}

// GetModulesForOrganization retrieves all modules for an organization
func (s *RecommendedModuleService) GetModulesForOrganization(organizationID uuid.UUID) ([]RecommendedModule, error) {
	var modules []RecommendedModule
	err := s.db.Where("organization_id = ? AND deleted_at IS NULL", organizationID).
		Order(`
			CASE
				WHEN category = 'core' THEN 1
				WHEN category = 'efficiency' THEN 2
				WHEN category = 'growth' THEN 3
				ELSE 4
			END,
			COALESCE(priority_score, recommendation_score, 5) DESC NULLS LAST,
			created_at ASC
		`).
		Find(&modules).Error
	return modules, err
}

// UpdateModulePriorityScore updates the priority score for a specific module
func (s *RecommendedModuleService) UpdateModulePriorityScore(moduleID uuid.UUID, priorityScore int) error {
	return s.db.Model(&RecommendedModule{}).
		Where("id = ?", moduleID).
		Update("priority_score", priorityScore).Error
}

// calculateSimilarity calculates the similarity percentage between two strings using Jaro-Winkler-like approach
func calculateSimilarity(s1, s2 string) float64 {
	if s1 == s2 {
		return 100.0
	}

	// Normalize strings for comparison
	s1 = strings.ToLower(strings.TrimSpace(s1))
	s2 = strings.ToLower(strings.TrimSpace(s2))

	if s1 == s2 {
		return 100.0
	}

	// Calculate Levenshtein distance
	distance := levenshtein.ComputeDistance(s1, s2)
	maxLen := len(s1)
	if len(s2) > maxLen {
		maxLen = len(s2)
	}

	if maxLen == 0 {
		return 100.0
	}

	// Convert to similarity percentage
	similarity := (1.0 - float64(distance)/float64(maxLen)) * 100.0
	return similarity
}

// findSimilarModule finds an existing module with similar name using fuzzy matching
// This is now deprecated - use the logic directly in UpsertModulesFromAI for transactional consistency
func (s *RecommendedModuleService) findSimilarModule(organizationID uuid.UUID, moduleName string) (*RecommendedModule, float64, error) {
	var existingModules []RecommendedModule
	err := s.db.Where("organization_id = ? AND deleted_at IS NULL", organizationID).
		Select("id, name").Find(&existingModules).Error
	if err != nil {
		return nil, 0, err
	}

	var bestMatch *RecommendedModule
	var bestSimilarity float64 = 0

	for _, module := range existingModules {
		similarity := calculateSimilarity(moduleName, module.Name)
		if similarity > bestSimilarity {
			bestSimilarity = similarity
			bestMatch = &module
		}
	}

	// Only return matches above 85% similarity threshold (increased from 60%)
	if bestSimilarity >= 85.0 {
		// Get the full module record
		var fullModule RecommendedModule
		err = s.db.Where("id = ?", bestMatch.ID).First(&fullModule).Error
		if err != nil {
			return nil, 0, err
		}
		return &fullModule, bestSimilarity, nil
	}

	return nil, 0, nil
}

// UpsertModulesFromAI updates or inserts modules from AI recommendations
func (s *RecommendedModuleService) UpsertModulesFromAI(organizationID uuid.UUID, aiModules []map[string]interface{}) error {
	// Use a transaction to ensure consistency
	return s.db.Transaction(func(tx *gorm.DB) error {
		// First, get all existing modules for this organization
		var existingModules []RecommendedModule
		if err := tx.Where("organization_id = ? AND deleted_at IS NULL", organizationID).Find(&existingModules).Error; err != nil {
			return err
		}

		// Create maps for faster lookup
		existingByName := make(map[string]*RecommendedModule)       // Exact name match
		existingByNormalized := make(map[string]*RecommendedModule) // Normalized name match

		for i := range existingModules {
			existingByName[existingModules[i].Name] = &existingModules[i]
			normalizedName := strings.ToLower(strings.TrimSpace(existingModules[i].Name))
			existingByNormalized[normalizedName] = &existingModules[i]
		}

		// Process each AI module
		for _, aiModule := range aiModules {
			module := &RecommendedModule{
				OrganizationID:     organizationID,
				Name:               getString(aiModule, "name"),
				Category:           getString(aiModule, "category"),
				Description:        getString(aiModule, "description"),
				KeyBenefit:         getString(aiModule, "key_benefit"),
				EffortToValue:      getString(aiModule, "effort_to_value"),
				ScoreReason:        getString(aiModule, "score_reason"),
				FirstRecommendedAt: time.Now(),
				LastUpdatedAt:      time.Now(),
			}

			// Handle recommendation score (might be int or float64 from JSON)
			if score := getInt(aiModule, "recommendation_score"); score > 0 {
				module.RecommendationScore = &score
			}

			// Normalize the module name for comparison
			normalizedNewName := strings.ToLower(strings.TrimSpace(module.Name))

			// Check for exact match first
			if existing, found := existingByName[module.Name]; found {
				// Exact match found, update existing module (but preserve user choices)
				updates := map[string]interface{}{
					"description":          module.Description,
					"key_benefit":          module.KeyBenefit,
					"effort_to_value":      module.EffortToValue,
					"recommendation_score": module.RecommendationScore,
					"score_reason":         module.ScoreReason,
					"last_updated_at":      time.Now(),
				}

				// Only update category if it's not a custom module
				if existing.Status != ModuleStatusCustom {
					updates["category"] = module.Category
				}

				if err := tx.Model(existing).Updates(updates).Error; err != nil {
					return err
				}
				continue
			}

			// Check for normalized name match
			if existing, found := existingByNormalized[normalizedNewName]; found {
				// Normalized match found, update existing module
				updates := map[string]interface{}{
					"description":          module.Description,
					"key_benefit":          module.KeyBenefit,
					"effort_to_value":      module.EffortToValue,
					"recommendation_score": module.RecommendationScore,
					"score_reason":         module.ScoreReason,
					"last_updated_at":      time.Now(),
				}

				// Only update category if it's not a custom module
				if existing.Status != ModuleStatusCustom {
					updates["category"] = module.Category
				}

				if err := tx.Model(existing).Updates(updates).Error; err != nil {
					return err
				}
				continue
			}

			// No exact or normalized match, check for fuzzy matches with higher threshold
			var bestMatch *RecommendedModule
			var bestSimilarity float64 = 0

			for _, existing := range existingModules {
				similarity := calculateSimilarity(module.Name, existing.Name)
				if similarity > bestSimilarity {
					bestSimilarity = similarity
					bestMatch = &existing
				}
			}

			// Use 85% threshold for fuzzy matching (increased from 60%)
			if bestSimilarity >= 85.0 && bestMatch != nil {
				// High similarity match found, update existing module
				updates := map[string]interface{}{
					"description":          module.Description,
					"key_benefit":          module.KeyBenefit,
					"effort_to_value":      module.EffortToValue,
					"recommendation_score": module.RecommendationScore,
					"score_reason":         module.ScoreReason,
					"last_updated_at":      time.Now(),
				}

				// Only update category if it's not a custom module
				if bestMatch.Status != ModuleStatusCustom {
					updates["category"] = module.Category
				}

				// If similarity is very high (95%+), also update the name
				if bestSimilarity >= 95.0 {
					updates["name"] = module.Name
				}

				if err := tx.Model(bestMatch).Updates(updates).Error; err != nil {
					return err
				}
			} else {
				// No match found, create new module
				module.Status = ModuleStatusRecommended
				if err := tx.Create(module).Error; err != nil {
					return err
				}

				// Add to our maps so subsequent modules in this batch can find it
				existingByName[module.Name] = module
				existingByNormalized[normalizedNewName] = module
			}
		}
		return nil
	})

}

// CreateCustomModule creates a user-requested custom module
func (s *RecommendedModuleService) CreateCustomModule(organizationID uuid.UUID, name, description, category string) (*RecommendedModule, error) {
	module := &RecommendedModule{
		OrganizationID:     organizationID,
		Name:               name,
		Category:           category,
		Description:        description,
		KeyBenefit:         "Custom module requested by user",
		EffortToValue:      "medium",
		Status:             ModuleStatusCustom,
		FirstRecommendedAt: time.Now(),
		LastUpdatedAt:      time.Now(),
	}

	if err := s.db.Create(module).Error; err != nil {
		return nil, err
	}

	return module, nil
}

// UpdateModuleStatus updates the status of a module (accept, reject, modify)
func (s *RecommendedModuleService) UpdateModuleStatus(moduleID uuid.UUID, status, feedback string) error {
	updates := map[string]interface{}{
		"status":          status,
		"user_feedback":   feedback,
		"last_updated_at": time.Now(),
	}

	return s.db.Model(&RecommendedModule{}).Where("id = ?", moduleID).Updates(updates).Error
}

// Helper functions to safely extract values from AI response maps
func getString(m map[string]interface{}, key string) string {
	if val, ok := m[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

func getInt(m map[string]interface{}, key string) int {
	if val, ok := m[key]; ok {
		switch v := val.(type) {
		case int:
			return v
		case float64:
			return int(v)
		}
	}
	return 0
}
