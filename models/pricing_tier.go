package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PricingTier represents a pricing tier with module limits and features
type PricingTier struct {
	ID                uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name              string         `json:"name" gorm:"size:50;not null"`
	MaxModules        int            `json:"max_modules" gorm:"not null"`
	MonthlyPriceCents int            `json:"monthly_price_cents" gorm:"not null"`
	Features          []string       `json:"features" gorm:"type:jsonb"`
	IsActive          bool           `json:"is_active" gorm:"default:true"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName specifies the table name for PricingTier model in vertoie schema
func (PricingTier) TableName() string {
	return "vertoie.pricing_tiers"
}

// GetMonthlyPrice returns the monthly price in dollars
func (pt *PricingTier) GetMonthlyPrice() float64 {
	return float64(pt.MonthlyPriceCents) / 100.0
}

// CanAccommodateModules checks if this tier can handle the given number of modules
func (pt *PricingTier) CanAccommodateModules(moduleCount int) bool {
	return moduleCount <= pt.MaxModules
}

// HasFeature checks if this tier includes a specific feature
func (pt *PricingTier) HasFeature(feature string) bool {
	for _, f := range pt.Features {
		if f == feature {
			return true
		}
	}
	return false
}

// PricingTierService handles operations for pricing tiers
type PricingTierService struct {
	db *gorm.DB
}

// NewPricingTierService creates a new service for pricing tiers
func NewPricingTierService(db *gorm.DB) *PricingTierService {
	return &PricingTierService{db: db}
}

// GetActiveTiers retrieves all active pricing tiers
func (s *PricingTierService) GetActiveTiers() ([]PricingTier, error) {
	var tiers []PricingTier
	err := s.db.Where("is_active = ? AND deleted_at IS NULL", true).
		Order("max_modules ASC").
		Find(&tiers).Error
	return tiers, err
}

// GetTierForModuleCount finds the appropriate tier for a given number of modules
func (s *PricingTierService) GetTierForModuleCount(moduleCount int) (*PricingTier, error) {
	var tier PricingTier
	err := s.db.Where("is_active = ? AND max_modules >= ? AND deleted_at IS NULL", true, moduleCount).
		Order("max_modules ASC").
		First(&tier).Error
	if err != nil {
		return nil, err
	}
	return &tier, nil
}

// GetTierByID retrieves a pricing tier by ID
func (s *PricingTierService) GetTierByID(id uuid.UUID) (*PricingTier, error) {
	var tier PricingTier
	err := s.db.Where("id = ? AND deleted_at IS NULL", id).First(&tier).Error
	if err != nil {
		return nil, err
	}
	return &tier, nil
}
