package models

import (
	"fmt"
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ThreadService handles thread management operations
type ThreadService struct {
	db *gorm.DB
}

// NewThreadService creates a new thread service instance
func NewThreadService(db *gorm.DB) *ThreadService {
	return &ThreadService{db: db}
}

// GetActiveThread returns the active thread for an organization, creates one if none exists
func (ts *ThreadService) GetActiveThread(organizationID uuid.UUID) (*ConversationThread, error) {
	var thread ConversationThread

	// Try to find existing active thread
	err := ts.db.Where("organization_id = ? AND is_active = ?", organizationID, true).
		Preload("BusinessExample").
		First(&thread).Error

	if err == gorm.ErrRecordNotFound {
		// Create new thread if none exists
		return ts.CreateNewThread(organizationID, "New Conversation", nil)
	}

	if err != nil {
		return nil, err
	}

	// Prepare computed fields
	thread.PrepareForJSON()
	return &thread, nil
}

// CreateNewThread creates a new conversation thread
func (ts *ThreadService) CreateNewThread(organizationID uuid.UUID, title string, businessExampleID *uuid.UUID) (*ConversationThread, error) {
	// Deactivate existing active threads
	err := ts.db.Model(&ConversationThread{}).
		Where("organization_id = ? AND is_active = ?", organizationID, true).
		Update("is_active", false).Error
	if err != nil {
		return nil, fmt.Errorf("failed to deactivate existing threads: %w", err)
	}

	// Create new active thread
	thread := ConversationThread{
		OrganizationID:    organizationID,
		Title:             title,
		BusinessExampleID: businessExampleID,
		TokenCount:        0,
		IsActive:          true,
	}

	err = ts.db.Create(&thread).Error
	if err != nil {
		return nil, fmt.Errorf("failed to create new thread: %w", err)
	}

	// Load business example if present
	if businessExampleID != nil {
		err = ts.db.Preload("BusinessExample").First(&thread, thread.ID).Error
		if err != nil {
			return nil, fmt.Errorf("failed to load thread with business example: %w", err)
		}
	}

	thread.PrepareForJSON()
	return &thread, nil
}

// GetThreadHistory returns all threads for an organization, ordered by most recent
func (ts *ThreadService) GetThreadHistory(organizationID uuid.UUID) ([]ConversationThread, error) {
	var threads []ConversationThread

	err := ts.db.Where("organization_id = ?", organizationID).
		Preload("BusinessExample").
		Order("updated_at DESC").
		Find(&threads).Error

	if err != nil {
		return nil, err
	}

	// Prepare computed fields for all threads
	for i := range threads {
		threads[i].PrepareForJSON()
	}

	return threads, nil
}

// SwitchToThread switches the active thread for an organization
func (ts *ThreadService) SwitchToThread(organizationID, threadID uuid.UUID) (*ConversationThread, error) {
	// Verify thread belongs to organization
	var thread ConversationThread
	err := ts.db.Where("id = ? AND organization_id = ?", threadID, organizationID).
		Preload("BusinessExample").
		First(&thread).Error
	if err != nil {
		return nil, fmt.Errorf("thread not found or access denied: %w", err)
	}

	// Deactivate all threads for this organization
	err = ts.db.Model(&ConversationThread{}).
		Where("organization_id = ?", organizationID).
		Update("is_active", false).Error
	if err != nil {
		return nil, fmt.Errorf("failed to deactivate threads: %w", err)
	}

	// Activate target thread
	err = ts.db.Model(&thread).Update("is_active", true).Error
	if err != nil {
		return nil, fmt.Errorf("failed to activate thread: %w", err)
	}

	thread.IsActive = true
	thread.PrepareForJSON()
	return &thread, nil
}

// UpdateThreadTokens updates the token count for a thread
func (ts *ThreadService) UpdateThreadTokens(threadID uuid.UUID, additionalTokens int) error {
	return ts.db.Model(&ConversationThread{}).
		Where("id = ?", threadID).
		Update("token_count", gorm.Expr("token_count + ?", additionalTokens)).Error
}

// UpdateThreadBusinessExample updates the business example for a thread
func (ts *ThreadService) UpdateThreadBusinessExample(threadID, exampleID uuid.UUID) error {
	return ts.db.Model(&ConversationThread{}).
		Where("id = ?", threadID).
		Update("business_example_id", exampleID).Error
}

// CreateThreadFromFirstMessage creates a new thread with title generated from first message
func (ts *ThreadService) CreateThreadFromFirstMessage(organizationID uuid.UUID, firstMessage string, businessExampleID *uuid.UUID) (*ConversationThread, error) {
	title := GenerateThreadTitle(firstMessage)
	return ts.CreateNewThread(organizationID, title, businessExampleID)
}

// GetThreadConversations returns all conversations for a specific thread
func (ts *ThreadService) GetThreadConversations(threadID uuid.UUID) ([]BusinessContextConversation, error) {
	var conversations []BusinessContextConversation

	err := ts.db.Where("thread_id = ?", threadID).
		Order("message_timestamp ASC").
		Find(&conversations).Error

	return conversations, err
}

// CheckIfThreadNeedsReplacement checks if thread is full and needs a new one
func (ts *ThreadService) CheckIfThreadNeedsReplacement(thread *ConversationThread) (bool, error) {
	if thread.IsFull() {
		// Create new thread with continuation title
		continuationTitle := fmt.Sprintf("%s (continued)",
			strings.TrimSuffix(thread.Title, "..."))

		_, err := ts.CreateNewThread(thread.OrganizationID, continuationTitle, thread.BusinessExampleID)
		if err != nil {
			return false, fmt.Errorf("failed to create continuation thread: %w", err)
		}
		return true, nil
	}
	return false, nil
}

// EstimateTokens provides a rough token estimate for text (1 token ≈ 4 characters)
func EstimateTokens(text string) int {
	// Rough estimation: 1 token ≈ 4 characters for English text
	// This is a simplified estimation - in production you'd use tiktoken or similar
	return len(text) / 4
}
