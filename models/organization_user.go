package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// OrganizationUser represents the many-to-many relationship between users and organizations
type OrganizationUser struct {
	ID             uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID         uuid.UUID      `json:"user_id" gorm:"type:uuid;not null"`
	OrganizationID uuid.UUID      `json:"organization_id" gorm:"type:uuid;not null"`
	Role           string         `json:"role" gorm:"size:50;not null;default:'member'"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	User         User         `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
}

// TableName specifies the table name for OrganizationUser model in vertoie schema
func (OrganizationUser) TableName() string {
	return "vertoie.organization_users"
}
