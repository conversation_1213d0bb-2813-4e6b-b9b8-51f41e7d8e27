package models

import (
	"time"

	"github.com/google/uuid"
)

const THREAD_TOKEN_LIMIT = 100000 // 100K tokens per thread

// ConversationThread represents a conversation thread for an organization
type ConversationThread struct {
	ID                uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrganizationID    uuid.UUID  `json:"organization_id" gorm:"type:uuid;not null"`
	Title             string     `json:"title" gorm:"not null"`
	BusinessExampleID *uuid.UUID `json:"business_example_id" gorm:"type:uuid"`
	TokenCount        int        `json:"-" gorm:"default:0"` // Hidden from JSON
	IsActive          bool       `json:"is_active" gorm:"default:true"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`

	// Relationships
	Organization    Organization                  `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	BusinessExample *BusinessPromptExample        `json:"business_example,omitempty" gorm:"foreignKey:BusinessExampleID"`
	Conversations   []BusinessContextConversation `json:"conversations,omitempty" gorm:"foreignKey:ThreadID"`

	// Computed fields (not stored in database)
	UsagePercentage int    `json:"usage_percentage" gorm:"-"`
	Status          string `json:"status" gorm:"-"`
}

// TableName specifies the table name for ConversationThread model in vertoie schema
func (ConversationThread) TableName() string {
	return "vertoie.conversation_threads"
}

// CalculateUsagePercentage calculates the token usage as a percentage
func (ct *ConversationThread) CalculateUsagePercentage() int {
	if ct.TokenCount <= 0 {
		return 0
	}

	percentage := (ct.TokenCount * 100) / THREAD_TOKEN_LIMIT
	if percentage > 100 {
		percentage = 100
	}
	return percentage
}

// GetStatus returns the thread status based on token usage
func (ct *ConversationThread) GetStatus() string {
	percentage := ct.CalculateUsagePercentage()

	switch {
	case percentage >= 95:
		return "full" // Force new thread
	case percentage >= 85:
		return "warning" // Show warning
	case percentage >= 60:
		return "moderate" // Yellow progress
	default:
		return "normal" // Green progress
	}
}

// IsNearLimit checks if the thread is near the token limit
func (ct *ConversationThread) IsNearLimit() bool {
	return ct.CalculateUsagePercentage() >= 85
}

// IsFull checks if the thread has reached the token limit
func (ct *ConversationThread) IsFull() bool {
	return ct.CalculateUsagePercentage() >= 95
}

// UpdateTokenCount updates the token count and calculated fields
func (ct *ConversationThread) UpdateTokenCount(additionalTokens int) {
	ct.TokenCount += additionalTokens
	ct.UsagePercentage = ct.CalculateUsagePercentage()
	ct.Status = ct.GetStatus()
}

// PrepareForJSON sets computed fields before JSON serialization
func (ct *ConversationThread) PrepareForJSON() {
	ct.UsagePercentage = ct.CalculateUsagePercentage()
	ct.Status = ct.GetStatus()
}

// GenerateTitle creates an automatic title from the first user message
func GenerateThreadTitle(firstMessage string) string {
	// Keep it simple for now - take first 50 characters
	maxLength := 50
	if len(firstMessage) <= maxLength {
		return firstMessage
	}

	// Find a good break point (space, punctuation)
	title := firstMessage[:maxLength]
	for i := maxLength - 1; i >= 30; i-- {
		if firstMessage[i] == ' ' || firstMessage[i] == '.' || firstMessage[i] == ',' {
			title = firstMessage[:i]
			break
		}
	}

	return title + "..."
}
