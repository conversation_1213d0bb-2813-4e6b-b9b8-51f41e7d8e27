package models

import (
	"time"

	"github.com/google/uuid"
)

// BusinessContextConversation represents a conversation message in business context gathering
type BusinessContextConversation struct {
	ID                    uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrganizationID        uuid.UUID  `json:"organization_id" gorm:"type:uuid;not null"`
	UserID                uuid.UUID  `json:"user_id" gorm:"type:uuid;not null"`
	ThreadID              *uuid.UUID `json:"thread_id" gorm:"type:uuid"`
	ConversationSessionID uuid.UUID  `json:"conversation_session_id" gorm:"type:uuid;not null"` // Deprecated, use ThreadID
	MessageRole           string     `json:"message_role" gorm:"size:20;not null"`              // "user" or "assistant"
	MessageContent        string     `json:"message_content" gorm:"type:text;not null"`
	TokenCount            int        `json:"token_count" gorm:"default:0"`
	MessageTimestamp      time.Time  `json:"message_timestamp"`
	CreatedAt             time.Time  `json:"created_at"`

	// Relationships
	Organization Organization        `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	User         User                `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Thread       *ConversationThread `json:"thread,omitempty" gorm:"foreignKey:ThreadID"`
}

// TableName specifies the table name for BusinessContextConversation model in vertoie schema
func (BusinessContextConversation) TableName() string {
	return "vertoie.organization_business_context_conversations"
}
