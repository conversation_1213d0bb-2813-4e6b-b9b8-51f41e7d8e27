package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User represents a business owner in the platform
type User struct {
	ID        uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Email     string         `json:"email" gorm:"size:255;uniqueIndex;not null"`
	FirstName string         `json:"first_name" gorm:"size:100"`
	LastName  string         `json:"last_name" gorm:"size:100"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Organizations []Organization `json:"organizations,omitempty" gorm:"many2many:vertoie.organization_users;foreignKey:ID;joinForeignKey:user_id;References:ID;joinReferences:organization_id"`
}

// TableName specifies the table name for User model in vertoie schema
func (User) TableName() string {
	return "vertoie.users"
}

// UserRole represents possible roles in organization membership
type UserRole string

const (
	RoleOwner  UserRole = "owner"
	RoleAdmin  UserRole = "admin"
	RoleMember UserRole = "member"
)
