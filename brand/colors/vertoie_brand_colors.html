<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vertoie Brand Color Palette</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 40px;
            background: #fafafa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #1F2937;
            margin-bottom: 40px;
            font-weight: 600;
        }
        .color-section {
            margin-bottom: 50px;
        }
        .section-title {
            color: #374151;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .color-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        .color-card:hover {
            transform: translateY(-2px);
        }
        .color-swatch {
            height: 100px;
            cursor: pointer;
            position: relative;
        }
        .color-info {
            padding: 16px;
        }
        .color-name {
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 8px;
        }
        .color-values {
            font-size: 12px;
            color: #6B7280;
            line-height: 1.4;
        }
        .copy-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        .gradient-demo {
            height: 60px;
            margin: 20px 0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Vertoie Brand Color Palette</h1>
        
        <div class="color-section">
            <div class="section-title">Primary Colors</div>
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-swatch" style="background: #FFF4F0;" onclick="copyColor('#FFF4F0')">
                        <div class="copy-indicator" id="copy-FFF4F0">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Orange 50</div>
                        <div class="color-values">
                            HEX: #FFF4F0<br>
                            Subtle backgrounds
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #FFE5D9;" onclick="copyColor('#FFE5D9')">
                        <div class="copy-indicator" id="copy-FFE5D9">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Orange 100</div>
                        <div class="color-values">
                            HEX: #FFE5D9<br>
                            Light backgrounds
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #FFCAB0;" onclick="copyColor('#FFCAB0')">
                        <div class="copy-indicator" id="copy-FFCAB0">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Orange 200</div>
                        <div class="color-values">
                            HEX: #FFCAB0<br>
                            Hover backgrounds
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #FFA87A;" onclick="copyColor('#FFA87A')">
                        <div class="copy-indicator" id="copy-FFA87A">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Orange 300</div>
                        <div class="color-values">
                            HEX: #FFA87A<br>
                            Disabled states
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #FF8A56;" onclick="copyColor('#FF8A56')">
                        <div class="copy-indicator" id="copy-FF8A56">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Orange 400</div>
                        <div class="color-values">
                            HEX: #FF8A56<br>
                            Secondary buttons
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #FF6B35;" onclick="copyColor('#FF6B35')">
                        <div class="copy-indicator" id="copy-FF6B35">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Orange 500</div>
                        <div class="color-values">
                            HEX: #FF6B35<br>
                            Primary brand color
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #E65100;" onclick="copyColor('#E65100')">
                        <div class="copy-indicator" id="copy-E65100">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Orange 600</div>
                        <div class="color-values">
                            HEX: #E65100<br>
                            Primary hover states
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #CC4700;" onclick="copyColor('#CC4700')">
                        <div class="copy-indicator" id="copy-CC4700">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Orange 700</div>
                        <div class="color-values">
                            HEX: #CC4700<br>
                            Active states
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #B33E00;" onclick="copyColor('#B33E00')">
                        <div class="copy-indicator" id="copy-B33E00">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Orange 800</div>
                        <div class="color-values">
                            HEX: #B33E00<br>
                            High contrast text
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #992E00;" onclick="copyColor('#992E00')">
                        <div class="copy-indicator" id="copy-992E00">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Orange 900</div>
                        <div class="color-values">
                            HEX: #992E00<br>
                            Darkest orange
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="color-section" style="margin-top: 30px;">
                <div class="section-title" style="font-size: 16px; margin-bottom: 15px;">Amber Scale (Secondary)</div>
                <div class="color-grid">
                    <div class="color-card">
                        <div class="color-swatch" style="background: #FDF6E3;" onclick="copyColor('#FDF6E3')">
                            <div class="copy-indicator" id="copy-FDF6E3">Copied!</div>
                        </div>
                        <div class="color-info">
                            <div class="color-name">Amber 100</div>
                            <div class="color-values">HEX: #FDF6E3</div>
                        </div>
                    </div>
                    
                    <div class="color-card">
                        <div class="color-swatch" style="background: #FBBF24;" onclick="copyColor('#FBBF24')">
                            <div class="copy-indicator" id="copy-FBBF24">Copied!</div>
                        </div>
                        <div class="color-info">
                            <div class="color-name">Amber 400</div>
                            <div class="color-values">HEX: #FBBF24</div>
                        </div>
                    </div>
                    
                    <div class="color-card">
                        <div class="color-swatch" style="background: #F7931E;" onclick="copyColor('#F7931E')">
                            <div class="copy-indicator" id="copy-F7931E">Copied!</div>
                        </div>
                        <div class="color-info">
                            <div class="color-name">Amber 500</div>
                            <div class="color-values">HEX: #F7931E</div>
                        </div>
                    </div>
                    
                    <div class="color-card">
                        <div class="color-swatch" style="background: #D97706;" onclick="copyColor('#D97706')">
                            <div class="copy-indicator" id="copy-D97706">Copied!</div>
                        </div>
                        <div class="color-info">
                            <div class="color-name">Amber 600</div>
                            <div class="color-values">HEX: #D97706</div>
                        </div>
                    </div>
                    
                    <div class="color-card">
                        <div class="color-swatch" style="background: #92400E;" onclick="copyColor('#92400E')">
                            <div class="copy-indicator" id="copy-92400E">Copied!</div>
                        </div>
                        <div class="color-info">
                            <div class="color-name">Amber 800</div>
                            <div class="color-values">HEX: #92400E</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="gradient-demo" style="background: linear-gradient(135deg, #FF6B35, #F7931E); margin-top: 20px;">
                Primary Gradient: Orange 500 → Amber 500
            </div>
        </div>
        
        <div class="color-section">
            <div class="section-title">Supporting Colors</div>
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-swatch" style="background: #FFE5D9;" onclick="copyColor('#FFE5D9')">
                        <div class="copy-indicator" id="copy-FFE5D9">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Light Peach</div>
                        <div class="color-values">
                            HEX: #FFE5D9<br>
                            RGB: 255, 229, 217<br>
                            Backgrounds, highlights
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #FFF7F0;" onclick="copyColor('#FFF7F0')">
                        <div class="copy-indicator" id="copy-FFF7F0">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Warm White</div>
                        <div class="color-values">
                            HEX: #FFF7F0<br>
                            RGB: 255, 247, 240<br>
                            Page backgrounds
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #E65100;" onclick="copyColor('#E65100')">
                        <div class="copy-indicator" id="copy-E65100">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Deep Orange</div>
                        <div class="color-values">
                            HEX: #E65100<br>
                            RGB: 230, 81, 0<br>
                            Hover states, accents
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="color-section">
            <div class="section-title">Neutral Colors</div>
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-swatch" style="background: #FFFFFF;" onclick="copyColor('#FFFFFF')">
                        <div class="copy-indicator" id="copy-FFFFFF">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">White</div>
                        <div class="color-values">
                            HEX: #FFFFFF<br>
                            Pure white backgrounds
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #FAFAFA;" onclick="copyColor('#FAFAFA')">
                        <div class="copy-indicator" id="copy-FAFAFA">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Gray 50</div>
                        <div class="color-values">
                            HEX: #FAFAFA<br>
                            Page backgrounds
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #F4F4F5;" onclick="copyColor('#F4F4F5')">
                        <div class="copy-indicator" id="copy-F4F4F5">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Gray 100</div>
                        <div class="color-values">
                            HEX: #F4F4F5<br>
                            Subtle backgrounds
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #E4E4E7;" onclick="copyColor('#E4E4E7')">
                        <div class="copy-indicator" id="copy-E4E4E7">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Gray 200</div>
                        <div class="color-values">
                            HEX: #E4E4E7<br>
                            Light borders
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #D4D4D8;" onclick="copyColor('#D4D4D8')">
                        <div class="copy-indicator" id="copy-D4D4D8">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Gray 300</div>
                        <div class="color-values">
                            HEX: #D4D4D8<br>
                            Borders, dividers
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #A1A1AA;" onclick="copyColor('#A1A1AA')">
                        <div class="copy-indicator" id="copy-A1A1AA">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Gray 400</div>
                        <div class="color-values">
                            HEX: #A1A1AA<br>
                            Placeholders
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #71717A;" onclick="copyColor('#71717A')">
                        <div class="copy-indicator" id="copy-71717A">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Gray 500</div>
                        <div class="color-values">
                            HEX: #71717A<br>
                            Muted text
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #52525B;" onclick="copyColor('#52525B')">
                        <div class="copy-indicator" id="copy-52525B">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Gray 600</div>
                        <div class="color-values">
                            HEX: #52525B<br>
                            Secondary text
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #3F3F46;" onclick="copyColor('#3F3F46')">
                        <div class="copy-indicator" id="copy-3F3F46">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Gray 700</div>
                        <div class="color-values">
                            HEX: #3F3F46<br>
                            Body text
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #27272A;" onclick="copyColor('#27272A')">
                        <div class="copy-indicator" id="copy-27272A">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Gray 800</div>
                        <div class="color-values">
                            HEX: #27272A<br>
                            Headings
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #1F2937;" onclick="copyColor('#1F2937')">
                        <div class="copy-indicator" id="copy-1F2937">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Gray 900</div>
                        <div class="color-values">
                            HEX: #1F2937<br>
                            Primary text, logo
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #111827;" onclick="copyColor('#111827')">
                        <div class="copy-indicator" id="copy-111827">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Gray 950</div>
                        <div class="color-values">
                            HEX: #111827<br>
                            Darkest text
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="color-section">
            <div class="section-title">Semantic Colors</div>
            <div class="color-grid">
                <!-- Success Colors -->
                <div class="color-card">
                    <div class="color-swatch" style="background: #DCFCE7;" onclick="copyColor('#DCFCE7')">
                        <div class="copy-indicator" id="copy-DCFCE7">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Success Light</div>
                        <div class="color-values">
                            HEX: #DCFCE7<br>
                            Success backgrounds
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #22C55E;" onclick="copyColor('#22C55E')">
                        <div class="copy-indicator" id="copy-22C55E">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Success</div>
                        <div class="color-values">
                            HEX: #22C55E<br>
                            Paid invoices, completed
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #16A34A;" onclick="copyColor('#16A34A')">
                        <div class="copy-indicator" id="copy-16A34A">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Success Dark</div>
                        <div class="color-values">
                            HEX: #16A34A<br>
                            Success hover states
                        </div>
                    </div>
                </div>
                
                <!-- Warning Colors -->
                <div class="color-card">
                    <div class="color-swatch" style="background: #FEF3C7;" onclick="copyColor('#FEF3C7')">
                        <div class="copy-indicator" id="copy-FEF3C7">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Warning Light</div>
                        <div class="color-values">
                            HEX: #FEF3C7<br>
                            Warning backgrounds
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #F59E0B;" onclick="copyColor('#F59E0B')">
                        <div class="copy-indicator" id="copy-F59E0B">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Warning</div>
                        <div class="color-values">
                            HEX: #F59E0B<br>
                            Pending, due soon
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #D97706;" onclick="copyColor('#D97706')">
                        <div class="copy-indicator" id="copy-D97706">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Warning Dark</div>
                        <div class="color-values">
                            HEX: #D97706<br>
                            Warning hover states
                        </div>
                    </div>
                </div>
                
                <!-- Error Colors -->
                <div class="color-card">
                    <div class="color-swatch" style="background: #FEE2E2;" onclick="copyColor('#FEE2E2')">
                        <div class="copy-indicator" id="copy-FEE2E2">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Error Light</div>
                        <div class="color-values">
                            HEX: #FEE2E2<br>
                            Error backgrounds
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #EF4444;" onclick="copyColor('#EF4444')">
                        <div class="copy-indicator" id="copy-EF4444">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Error</div>
                        <div class="color-values">
                            HEX: #EF4444<br>
                            Overdue, errors
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #DC2626;" onclick="copyColor('#DC2626')">
                        <div class="copy-indicator" id="copy-DC2626">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Error Dark</div>
                        <div class="color-values">
                            HEX: #DC2626<br>
                            Error hover states
                        </div>
                    </div>
                </div>
                
                <!-- Info Colors -->
                <div class="color-card">
                    <div class="color-swatch" style="background: #DBEAFE;" onclick="copyColor('#DBEAFE')">
                        <div class="copy-indicator" id="copy-DBEAFE">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Info Light</div>
                        <div class="color-values">
                            HEX: #DBEAFE<br>
                            Info backgrounds
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #3B82F6;" onclick="copyColor('#3B82F6')">
                        <div class="copy-indicator" id="copy-3B82F6">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Info</div>
                        <div class="color-values">
                            HEX: #3B82F6<br>
                            Links, info states
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #2563EB;" onclick="copyColor('#2563EB')">
                        <div class="copy-indicator" id="copy-2563EB">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Info Dark</div>
                        <div class="color-values">
                            HEX: #2563EB<br>
                            Info hover states
                        </div>
                    </div>
                </div>
            </div>
        </div>        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #EF4444;" onclick="copyColor('#EF4444')">
                        <div class="copy-indicator" id="copy-EF4444">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Error Red</div>
                        <div class="color-values">
                            HEX: #EF4444<br>
                            RGB: 239, 68, 68<br>
                            Errors, overdue items
                        </div>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-swatch" style="background: #3B82F6;" onclick="copyColor('#3B82F6')">
                        <div class="copy-indicator" id="copy-3B82F6">Copied!</div>
                    </div>
                    <div class="color-info">
                        <div class="color-name">Info Blue</div>
                        <div class="color-values">
                            HEX: #3B82F6<br>
                            RGB: 59, 130, 246<br>
                            Links, info states
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function copyColor(color) {
            navigator.clipboard.writeText(color).then(() => {
                const indicator = document.getElementById('copy-' + color.substring(1));
                indicator.style.opacity = '1';
                setTimeout(() => {
                    indicator.style.opacity = '0';
                }, 1000);
            });
        }
    </script>
</body>
</html>