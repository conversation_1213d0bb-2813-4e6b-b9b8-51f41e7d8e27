{
  description = "Vertoie development environment";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-25.05";
    unstable.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs = { self, nixpkgs, unstable, flake-utils }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pkgs = nixpkgs.legacyPackages.${system};
        unstablePkgs = unstable.legacyPackages.${system};
        
        # Import modular components
        devTools = import ./nix/dev-tools.nix { inherit pkgs unstablePkgs; };
        devShell = import ./nix/shell.nix { inherit pkgs devTools; };
        packages = import ./nix/packages.nix { inherit pkgs unstablePkgs; };

      in
      {
        devShells.default = devShell;
        packages = packages // {
          # Default package is help
          default = packages.help;
        };
      });
}