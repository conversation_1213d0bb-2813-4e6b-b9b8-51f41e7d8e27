// Theme management
class ThemeManager {
    constructor() {
        this.init();
    }

    init() {
        this.loadTheme();
        this.setupToggle();
        this.watchSystemTheme();
    }

    loadTheme() {
        const savedTheme = localStorage.getItem('theme');
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        const theme = savedTheme || systemTheme;
        
        document.documentElement.setAttribute('data-theme', theme);
    }

    setupToggle() {
        const toggle = document.getElementById('theme-toggle');
        if (toggle) {
            toggle.addEventListener('click', () => this.toggleTheme());
        }
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
    }

    watchSystemTheme() {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            // Only apply system theme if user hasn't manually set a preference
            if (!localStorage.getItem('theme')) {
                const theme = e.matches ? 'dark' : 'light';
                document.documentElement.setAttribute('data-theme', theme);
            }
        });
    }
}

// Pricing management
class PricingManager {
    constructor() {
        this.loadPricing();
    }

    async loadPricing() {
        try {
            const [plansResponse, addonsResponse] = await Promise.all([
                fetch('/api/plans', {
                    headers: {
                        'X-Internal-Request': 'true'
                    }
                }),
                fetch('/api/addons', {
                    headers: {
                        'X-Internal-Request': 'true'
                    }
                })
            ]);

            const plans = await plansResponse.json();
            const addons = await addonsResponse.json();

            this.renderPricingCards(plans);
            this.setupAddonsInfo(addons);
        } catch (error) {
            console.error('Failed to load pricing:', error);
            this.renderFallbackPricing();
        }
    }

    renderPricingCards(plans) {
        const container = document.getElementById('pricing-cards');
        if (!container) return;

        // Sort plans by price
        const sortedPlans = plans.sort((a, b) => {
            if (a.price_cents === 0 && a.billing_cycle === 'custom') return 1;
            if (b.price_cents === 0 && b.billing_cycle === 'custom') return -1;
            return a.price_cents - b.price_cents;
        });

        container.innerHTML = sortedPlans.map((plan, index) => {
            const isFeatured = index === Math.floor(sortedPlans.length / 2);
            const price = this.formatPrice(plan.price_cents, plan.billing_cycle);
            const features = this.formatFeatures(plan.features);

            return `
                <div class="pricing-card ${isFeatured ? 'featured' : ''}">
                    <div class="pricing-header">
                        <h3 class="pricing-title">${plan.name}</h3>
                        <div class="pricing-price">
                            ${price.amount}
                            ${price.period ? `<span class="pricing-period">/${price.period}</span>` : ''}
                        </div>
                    </div>
                    <p class="pricing-description">${plan.description}</p>
                    <ul class="pricing-features">
                        ${features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                    <div class="pricing-cta">
                        <a href="#" class="btn ${isFeatured ? 'btn-primary' : 'btn-secondary'} btn-lg">
                            ${plan.price_cents === 0 && plan.billing_cycle === 'custom' ? 'Contact Sales' : 'Get Started'}
                        </a>
                    </div>
                </div>
            `;
        }).join('');
    }

    formatPrice(priceCents, billingCycle) {
        if (priceCents === 0) {
            return billingCycle === 'custom' ? { amount: 'Custom' } : { amount: 'Free' };
        }

        const dollars = priceCents / 100;
        return {
            amount: `$${dollars.toFixed(0)}`,
            period: billingCycle === 'monthly' ? 'month' : billingCycle
        };
    }

    formatFeatures(features) {
        const featureList = [];

        if (features.modules !== undefined) {
            featureList.push(features.modules === -1 ? 'Unlimited modules' : `${features.modules} modules`);
        }

        if (features.users !== undefined) {
            featureList.push(features.users === -1 ? 'Unlimited users' : `${features.users} users`);
        }

        if (features.ai_generations !== undefined) {
            featureList.push(
                features.ai_generations === -1 
                    ? 'Unlimited AI generations' 
                    : `${features.ai_generations} AI generations/month`
            );
        }

        if (features.storage_gb !== undefined) {
            featureList.push(
                features.storage_gb === -1 
                    ? 'Unlimited storage' 
                    : `${features.storage_gb}GB storage`
            );
        }

        return featureList;
    }

    setupAddonsInfo(addons) {
        // Could be used to show addon information in modals or tooltips
        this.addons = addons;
    }

    renderFallbackPricing() {
        const container = document.getElementById('pricing-cards');
        if (!container) return;

        container.innerHTML = `
            <div class="pricing-card">
                <div class="pricing-header">
                    <h3 class="pricing-title">Basic</h3>
                    <div class="pricing-price">Free</div>
                </div>
                <p class="pricing-description">Perfect for trying out Vertoie</p>
                <ul class="pricing-features">
                    <li>2 modules</li>
                    <li>1 user</li>
                    <li>10 AI generations/month</li>
                    <li>1GB storage</li>
                </ul>
                <div class="pricing-cta">
                    <a href="#" class="btn btn-secondary btn-lg">Get Started</a>
                </div>
            </div>
            <div class="pricing-card featured">
                <div class="pricing-header">
                    <h3 class="pricing-title">Professional</h3>
                    <div class="pricing-price">$49<span class="pricing-period">/month</span></div>
                </div>
                <p class="pricing-description">Growing businesses</p>
                <ul class="pricing-features">
                    <li>10 modules</li>
                    <li>10 users</li>
                    <li>250 AI generations/month</li>
                    <li>25GB storage</li>
                </ul>
                <div class="pricing-cta">
                    <a href="#" class="btn btn-primary btn-lg">Get Started</a>
                </div>
            </div>
            <div class="pricing-card">
                <div class="pricing-header">
                    <h3 class="pricing-title">Enterprise</h3>
                    <div class="pricing-price">Custom</div>
                </div>
                <p class="pricing-description">Custom solutions with dedicated support</p>
                <ul class="pricing-features">
                    <li>Unlimited modules</li>
                    <li>Unlimited users</li>
                    <li>Unlimited AI generations</li>
                    <li>Unlimited storage</li>
                </ul>
                <div class="pricing-cta">
                    <a href="#" class="btn btn-secondary btn-lg">Contact Sales</a>
                </div>
            </div>
        `;
    }
}

// Smooth scrolling for navigation links
class Navigation {
    constructor() {
        this.setupSmoothScrolling();
    }

    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 80; // Account for fixed nav
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }
}

// Minimal form utilities (HTMX handles most form logic)
class AuthFormManager {
    constructor() {
        // Only setup minimal fallback protection
        this.setupFallbackProtection();
    }

    setupFallbackProtection() {
        // Basic double-submission prevention as fallback
        // (HTMX hx-disabled-elt should handle this, but backup doesn't hurt)
        document.querySelectorAll('form[id$="-form"]').forEach(form => {
            form.addEventListener('submit', (e) => {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn && submitBtn.disabled) {
                    e.preventDefault();
                    return false;
                }
            });
        });
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ThemeManager();
    new PricingManager();
    new Navigation();
    new AuthFormManager();

    // Add subtle animations on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.feature-card, .pricing-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});
