/* Modern Dashboard Styles - Built on Vertoie Design System */

/* Dashboard Body */
.dashboard-body {
    background-color: var(--bg-secondary);
    height: 100vh;
    font-family: var(--font-family);
    font-size: 1.4rem;
    line-height: 1.6;
    color: var(--text-primary);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Dashboard Header */
.dashboard-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    padding: 0;
    z-index: 100;
    backdrop-filter: blur(20px);
    height: var(--header-height);
    flex: 0 0 var(--header-height);
    display: flex;
    align-items: center;
}

.dashboard-header .dashboard-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0;
    padding: 0 2.4rem;
    width: 100%;
    height: 100%;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.mobile-menu-btn {
    display: none;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.8rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.mobile-menu-btn:hover {
    background: var(--bg-accent);
    color: var(--text-primary);
}

.logo-link {
    display: flex;
    align-items: center;
}

.logo {
    height: 2.8rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1.6rem;
    height: 100%;
}

/* Search */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
    margin-right: 2rem;
    height: 100%;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    height: 100%;
}

.search-icon {
    position: absolute;
    left: 1.2rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
}

.search-input {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 0.8rem 1.2rem 0.8rem 3.6rem;
    font-size: 1.4rem;
    color: var(--text-primary);
    width: 24rem;
    transition: all 0.2s ease;
    height: 2.8rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    margin-bottom: 0;
}

.search-input:focus {
    outline: none;
    border-color: var(--vertoie-orange);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.search-input::placeholder {
    color: var(--text-muted);
}

/* Notifications */
.notification-dropdown {
    position: relative;
}

.notification-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.8rem;
    border-radius: 6px;
    position: relative;
    transition: all 0.2s ease;
    margin-bottom: 0;
}

.notification-btn:hover {
    background: var(--bg-accent);
    color: var(--text-primary);
}

.notification-badge {
    position: absolute;
    top: 0.4rem;
    right: 0.4rem;
    background: var(--vertoie-orange);
    color: white;
    font-size: 1rem;
    font-weight: 600;
    padding: 0.2rem 0.6rem;
    border-radius: 10px;
    min-width: 1.8rem;
    text-align: center;
}

.notification-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    width: 32rem;
    max-height: 40rem;
    overflow-y: auto;
    z-index: 1000;
    margin-top: 0.8rem;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.6rem;
    border-bottom: 1px solid var(--border-light);
}

.notification-header h4 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
}

.mark-all-read {
    background: transparent;
    border: none;
    color: var(--vertoie-orange);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
}

.notification-list {
    padding: 0.8rem 0;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 1.2rem;
    padding: 1.2rem 1.6rem;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.notification-item:hover {
    background: var(--bg-accent);
}

.notification-item.unread {
    background: rgba(255, 107, 53, 0.05);
}

.notification-item.unread:hover {
    background: rgba(255, 107, 53, 0.1);
}

.notification-icon {
    color: var(--vertoie-orange);
    flex-shrink: 0;
    margin-top: 0.2rem;
}

.notification-content p {
    margin: 0 0 0.4rem 0;
    font-size: 1.3rem;
    color: var(--text-primary);
    font-weight: 500;
}

.notification-time {
    font-size: 1.2rem;
    color: var(--text-muted);
}

/* Theme Toggle */
.theme-toggle {
    background: var(--bg-secondary) !important;
    border: 1px solid var(--border-light) !important;
    border-radius: 8px;
    padding: 0.8rem !important;
    cursor: pointer;
    color: var(--text-secondary) !important;
    font-size: 1.6rem !important;
    transition: all 0.2s ease;
    font-weight: normal !important;
    transform: none !important;
    text-decoration: none !important;
    width: 4rem;
    height: 4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;
}

.theme-toggle:hover {
    background: var(--bg-accent) !important;
    border-color: var(--border-medium) !important;
    color: var(--text-primary) !important;
    transform: none !important;
}

.theme-icon {
    display: none;
}

[data-theme="light"] .light-icon {
    display: inline;
}

[data-theme="dark"] .dark-icon {
    display: inline;
}

/* User Menu */
.user-menu {
    position: relative;
}

.user-menu-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 1.2rem;
    padding: 0.8rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    color: var(--text-primary);
    margin-bottom: 0;
}

.user-menu-btn:hover {
    background: var(--bg-accent);
}

.user-avatar {
    width: 3.2rem;
    height: 3.2rem;
    background: var(--vertoie-orange);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.4rem;
}

.user-info {
    text-align: left;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    font-size: 1.4rem;
    line-height: 1.2;
}

.user-email {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.2;
}

.chevron-down {
    color: var(--text-muted);
    transition: transform 0.2s ease;
}

.user-menu[data-open="true"] .chevron-down {
    transform: rotate(180deg);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    width: 20rem;
    z-index: 1000;
    margin-top: 0.8rem;
    overflow: hidden;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    padding: 1.2rem 1.6rem;
    color: var(--text-primary);
    text-decoration: none;
    font-size: 1.4rem;
    transition: background-color 0.2s ease;
    border: none;
    background: transparent;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.dropdown-item:hover {
    background: var(--bg-accent);
    color: var(--text-primary);
}

.dropdown-item svg {
    color: var(--text-muted);
    flex-shrink: 0;
}

.dropdown-divider {
    height: 1px;
    background: var(--border-light);
    margin: 0.8rem 0;
}

.logout-btn {
    color: #dc3545 !important;
}

.logout-btn svg {
    color: #dc3545 !important;
}

/* Dashboard Layout */
.dashboard-layout {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.dashboard-layout.sidebar-collapsed,
.sidebar-collapsed-init .dashboard-layout {
    /* Handled by sidebar flex-basis */
}

/* Sidebar */
.dashboard-sidebar {
    background: var(--bg-primary);
    border-right: 1px solid var(--border-light);
    padding: 0;
    flex: 0 0 var(--sidebar-width);
    overflow-y: auto;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.dashboard-sidebar.collapsed,
.dashboard-layout.sidebar-collapsed .dashboard-sidebar,
.sidebar-collapsed-init .dashboard-sidebar {
    flex-basis: var(--sidebar-width-collapsed);
}

/* Sidebar Footer */
.sidebar-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 1.2rem 1.6rem;
    border-top: 1px solid var(--border-light);
    margin-top: auto;
    box-sizing: border-box;
}

.sidebar-collapse-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.6rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;
    width: 3.2rem;
    height: 3.2rem;
}

.sidebar-collapse-btn:hover {
    background: var(--bg-accent);
    color: var(--text-primary);
}

.collapse-icon {
    transition: transform 0.3s ease;
}

.dashboard-sidebar.collapsed .collapse-icon {
    transform: rotate(180deg);
}

.dashboard-sidebar.collapsed .sidebar-footer {
    justify-content: center;
    padding: 1.2rem 0.6rem;
}

.sidebar-nav {
    padding: 1.6rem 1.6rem;
    transition: padding 0.3s ease;
    flex: 1;
    overflow-y: auto;
}

.dashboard-sidebar.collapsed .sidebar-nav {
    padding: 1.6rem 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.sidebar-section {
    margin-bottom: 3.2rem;
}

.dashboard-sidebar.collapsed .sidebar-section {
    margin-bottom: 1.6rem;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0 0 1.2rem 0;
    padding: 0 1.2rem;
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
}

.dashboard-sidebar.collapsed .sidebar-title,
.sidebar-collapsed-init .dashboard-sidebar .sidebar-title {
    opacity: 0;
    height: 0;
    margin: 0;
    padding: 0;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.dashboard-sidebar.collapsed .sidebar-nav ul {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.sidebar-nav li {
    margin-bottom: 0.4rem;
}

.dashboard-sidebar.collapsed .sidebar-nav li {
    margin-bottom: 0.4rem;
    width: 4rem;
    display: flex;
    justify-content: center;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    padding: 1.2rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-size: 1.4rem;
    font-weight: 500;
}

.nav-item.active {
    background: var(--vertoie-orange);
    color: white;
}

.nav-item:hover, .nav-item.active:hover, .dashboard-sidebar a.nav-item:hover {
    background: var(--vertoie-orange-hover);
    color: white;
    transform: translateY(-1px);
}

.nav-icon {
    width: 2rem;
    height: 2rem;
    flex-shrink: 0;
}

.dashboard-sidebar.collapsed .nav-icon {
    width: 2rem;
    height: 2rem;
}

.nav-text {
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
}

/* Collapsed Sidebar Styles */
.dashboard-sidebar.collapsed .nav-item,
.sidebar-collapsed-init .dashboard-sidebar .nav-item {
    justify-content: center;
    align-items: center;
    gap: 0;
    padding: 1rem;
    position: relative;
    width: 4rem;
    height: 4rem;
    margin: 0;
    display: flex;
    border-radius: 8px;
}

.dashboard-sidebar.collapsed .nav-text,
.sidebar-collapsed-init .dashboard-sidebar .nav-text {
    opacity: 0;
    width: 0;
    margin: 0;
}

/* Custom Tooltip Styles */
.tooltip {
    position: fixed;
    background: var(--text-primary);
    color: var(--bg-primary);
    padding: 0.8rem 1.2rem;
    border-radius: 8px;
    font-size: 1.4rem;
    font-weight: 500;
    white-space: nowrap;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    pointer-events: none;
    opacity: 0;
    transform: translateY(-50%) scale(0.9);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.tooltip.show {
    opacity: 1;
    transform: translateY(-50%) scale(1);
}

.tooltip::before {
    content: '';
    position: absolute;
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 0.6rem solid transparent;
    border-right-color: var(--text-primary);
}

/* Main Content */
.dashboard-main {
    flex: 1;
    padding: 2.4rem;
    overflow-y: auto;
}

.main-content {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 3.2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Page Header */
.page-header {
    margin-bottom: 3.2rem;
}

.page-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
}

.page-title-section {
    flex: 1;
}

.page-title {
    font-size: 3.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.8rem 0;
    line-height: 1.2;
}

.page-subtitle {
    font-size: 1.6rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
}

.page-actions {
    flex-shrink: 0;
}

/* Dashboard-specific button extensions (design system provides base .btn styles) */

/* Stats Overview */
.stats-overview {
    margin-bottom: 3.2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(24rem, 1fr));
    gap: 2rem;
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 2.4rem;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--border-medium);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.6rem;
}

.stat-icon {
    width: 4.8rem;
    height: 4.8rem;
    background: rgba(255, 107, 53, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--vertoie-orange);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    font-size: 1.2rem;
    font-weight: 600;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
}

.stat-trend.positive {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.stat-trend.negative {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.stat-content {
    text-align: left;
}

.stat-number {
    font-size: 3.6rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.8rem 0;
    line-height: 1;
}

.stat-label {
    font-size: 1.4rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin: 0;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(40rem, 1fr));
    gap: 2.4rem;
}

/* Dashboard Cards */
.dashboard-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.dashboard-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border-color: var(--border-medium);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2.4rem;
    border-bottom: 1px solid var(--border-light);
}

.card-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.card-action {
    background: transparent;
    border: none;
    color: var(--vertoie-orange);
    font-size: 1.4rem;
    font-weight: 500;
    cursor: pointer;
    padding: 0;
    transition: color 0.2s ease;
}

.card-action:hover {
    color: var(--vertoie-orange-hover);
}

.card-content {
    padding: 2.4rem;
}

/* Activity List */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1.6rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1.2rem;
    padding: 0;
}

.activity-icon {
    width: 3.2rem;
    height: 3.2rem;
    background: rgba(255, 107, 53, 0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--vertoie-orange);
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-text {
    font-size: 1.4rem;
    color: var(--text-primary);
    margin: 0 0 0.4rem 0;
    line-height: 1.4;
}

.activity-time {
    font-size: 1.2rem;
    color: var(--text-muted);
}

/* Quick Actions */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(16rem, 1fr));
    gap: 1.6rem;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.2rem;
    padding: 2rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    color: var(--text-primary);
}

.quick-action-btn:hover {
    background: var(--bg-accent);
    border-color: var(--vertoie-orange);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-icon {
    width: 4rem;
    height: 4rem;
    background: rgba(255, 107, 53, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--vertoie-orange);
}

.quick-action-btn span {
    font-size: 1.4rem;
    font-weight: 500;
    text-align: center;
}

/* Project List */
.project-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.project-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    padding: 0;
}

.project-info {
    flex: 1;
    min-width: 0;
}

.project-name {
    font-size: 1.6rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.4rem 0;
}

.project-description {
    font-size: 1.3rem;
    color: var(--text-secondary);
    margin: 0 0 1.2rem 0;
    line-height: 1.4;
}

.project-meta {
    display: flex;
    align-items: center;
    gap: 1.2rem;
}

.project-status {
    font-size: 1.2rem;
    font-weight: 500;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
}

.project-status.in-progress {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.project-status.planning {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.project-status.completed {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.project-deadline {
    font-size: 1.2rem;
    color: var(--text-muted);
}

.project-progress {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.8rem;
    flex-shrink: 0;
}

.progress-bar {
    width: 8rem;
    height: 0.6rem;
    background: var(--bg-secondary);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--vertoie-orange);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Team Overview */
.team-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-bottom: 2.4rem;
}

.team-stat {
    text-align: center;
}

.team-stat-number {
    font-size: 2.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.4rem 0;
}

.team-stat-label {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.team-avatars {
    display: flex;
    justify-content: center;
}

.avatar-group {
    display: flex;
    align-items: center;
}

.avatar {
    width: 3.2rem;
    height: 3.2rem;
    background: var(--vertoie-orange);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    margin-left: -0.8rem;
    border: 2px solid var(--bg-primary);
    transition: transform 0.2s ease;
}

.avatar:first-child {
    margin-left: 0;
}

.avatar:hover {
    transform: scale(1.1);
    z-index: 1;
}

.avatar-more {
    width: 3.2rem;
    height: 3.2rem;
    background: var(--bg-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 1.2rem;
    font-weight: 600;
    margin-left: -0.8rem;
    border: 2px solid var(--bg-primary);
}

/* Mobile Overlay */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 150;
    display: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-layout {
        grid-template-columns: 24rem 1fr;
    }
    
    .search-input {
        width: 20rem;
    }
    
    .main-content {
        padding: 2.4rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(20rem, 1fr));
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-layout {
        grid-template-columns: 1fr;
    }
    
    .dashboard-layout.sidebar-collapsed {
        grid-template-columns: 1fr;
    }
    
    .dashboard-sidebar {
        position: fixed;
        left: -28rem;
        top: 0;
        height: 100vh;
        z-index: 200;
        transition: left 0.3s ease;
        background: var(--bg-primary);
        border-right: 1px solid var(--border-light);
        width: 28rem;
    }
    
    .dashboard-sidebar.collapsed {
        width: 28rem; /* Keep full width on mobile even when collapsed */
    }
    
    .dashboard-sidebar.open {
        left: 0;
    }
    
    /* Reset collapsed styles on mobile */
    .dashboard-sidebar.collapsed .nav-item {
        justify-content: flex-start;
        padding: 1.2rem;
        width: auto;
        margin: 0 0 0.4rem 0;
    }
    
    .dashboard-sidebar.collapsed .nav-text {
        opacity: 1;
        width: auto;
        margin: 0;
    }
    
    .dashboard-sidebar.collapsed .sidebar-title {
        opacity: 1;
        height: auto;
        margin: 0 0 1.2rem 0;
        padding: 0 1.2rem;
    }
    
    .dashboard-sidebar.collapsed .nav-item::after,
    .dashboard-sidebar.collapsed .nav-item::before {
        display: none;
    }
    
    .dashboard-sidebar.collapsed .sidebar-footer {
        justify-content: flex-end;
    }
    
    .mobile-menu-btn {
        display: block;
    }
    
    .dashboard-header .container {
        padding: 0 1.6rem;
    }
    
    .header-right {
        gap: 1.2rem;
    }
    
    .search-input {
        width: 16rem;
    }
    
    .user-info {
        display: none;
    }
    
    .main-content {
        padding: 1.6rem;
    }
    
    .mobile-overlay {
        display: block;
    }
    
    .notification-menu {
        width: 28rem;
        right: -6rem;
    }
    
    .user-dropdown {
        width: 18rem;
        right: -4rem;
    }
    
    .page-header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1.6rem;
    }
    
    .page-title {
        font-size: 2.8rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(16rem, 1fr));
        gap: 1.6rem;
    }
    
    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .team-stats {
        grid-template-columns: 1fr;
        gap: 1.6rem;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        height: var(--header-height-mobile);
        flex-basis: var(--header-height-mobile);
    }
    
    .dashboard-sidebar {
        position: fixed;
        top: var(--header-height-mobile);
        left: -100%;
        width: 100%;
        height: calc(100vh - var(--header-height-mobile));
        z-index: 99;
    }
    
    .header-left .logo {
        height: 2.8rem;
    }
    
    .search-input {
        width: 12rem;
    }
    
    .main-content {
        padding: 1.2rem;
    }
    
    .notification-menu {
        width: 24rem;
        right: -8rem;
    }
    
    .user-dropdown {
        width: 16rem;
        right: -6rem;
    }
    
    .page-title {
        font-size: 2.4rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .project-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1.6rem;
    }
    
    .project-progress {
        align-items: flex-start;
        width: 100%;
    }
    
    .progress-bar {
        width: 100%;
    }
} 