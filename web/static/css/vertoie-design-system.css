/* ===================================
   VERTOIE DESIGN SYSTEM
   Modern, consistent component library
   =================================== */

/* ===================================
   1. DESIGN TOKENS & VARIABLES
   =================================== */

:root {
  /* Brand Colors */
  --vertoie-orange: #FF6B35;
  --vertoie-orange-light: #FF8A65;
  --vertoie-orange-dark: #E65100;
  
  /* Semantic Colors */
  --color-success: #10B981;
  --success-500: #10B981;
  --success-600: #059669;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #3B82F6;
  
  /* Surface Colors */
  --surface-primary: var(--bg-primary);
  
  /* Border Colors */
  --border-subtle: var(--border-light);
  --border-focus: var(--vertoie-orange);
  
  /* Neutral Palette - Light Mode */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8FAFC;
  --bg-tertiary: #F1F5F9;
  --bg-accent: #E2E8F0;
  
  --text-primary: #0F172A;
  --text-secondary: #475569;
  --text-tertiary: #64748B;
  --text-muted: #94A3B8;
  
  --border-light: #E2E8F0;
  --border-medium: #CBD5E1;
  --border-dark: #94A3B8;
  
  /* Shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  /* Spacing Scale */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
  
  /* Layout Dimensions */
  --header-height: 5rem;
  --header-height-mobile: 6rem;
  --sidebar-width: 24rem;
  --sidebar-width-collapsed: 6rem;
}

/* Dark Mode Variables */
[data-theme="dark"] {
  --bg-primary: #0F172A;
  --bg-secondary: #1E293B;
  --bg-tertiary: #334155;
  --bg-accent: #475569;
  
  --text-primary: #F8FAFC;
  --text-secondary: #E2E8F0;
  --text-tertiary: #CBD5E1;
  --text-muted: #94A3B8;
  
  --border-light: #334155;
  --border-medium: #475569;
  --border-dark: #64748B;
}

/* ===================================
   2. BASE STYLES & RESETS
   =================================== */

* {
  box-sizing: border-box;
}

html {
  font-size: 62.5%; /* 1rem = 10px */
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: 1.4rem;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  margin: 0;
  padding: 0;
  transition: background-color var(--transition-normal), color var(--transition-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===================================
   2. BASE LAYOUT SYSTEM
   =================================== */

/* Ensure proper height inheritance */
html {
  height: 100%;
}

body {
  min-height: 100%;
  margin: 0;
}

/* Base layout containers */
.full-height {
  height: 100vh;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-1 {
  flex: 1;
}

.flex-grow {
  flex-grow: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

/* ===================================
   3. TYPOGRAPHY SYSTEM
   =================================== */

.text-xs { font-size: 1.2rem; line-height: 1.4; }
.text-sm { font-size: 1.3rem; line-height: 1.5; }
.text-base { font-size: 1.4rem; line-height: 1.6; }
.text-lg { font-size: 1.6rem; line-height: 1.6; }
.text-xl { font-size: 1.8rem; line-height: 1.6; }
.text-2xl { font-size: 2.4rem; line-height: 1.4; }
.text-3xl { font-size: 3rem; line-height: 1.3; }
.text-4xl { font-size: 3.6rem; line-height: 1.2; }
.text-5xl { font-size: 4.8rem; line-height: 1.1; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-muted { color: var(--text-muted); }
.text-brand { color: var(--vertoie-orange); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

/* ===================================
   4. BUTTON SYSTEM
   =================================== */

/* HTMX Loading States */
.htmx-indicator {
  display: none;
}

.htmx-request .htmx-indicator {
  display: inline;
}

.htmx-request .default-text {
  display: none;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: 1.4rem;
  font-weight: 500;
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  user-select: none;
  position: relative;
  outline: none;
  margin: 0;
}

.btn:focus-visible {
  outline: 2px solid var(--vertoie-orange);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button Variants */
.btn-primary {
  background: var(--vertoie-orange);
  border-color: var(--vertoie-orange);
  color: white;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover:not(:disabled) {
  background: var(--vertoie-orange-dark);
  border-color: var(--vertoie-orange-dark);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--bg-primary);
  border-color: var(--border-medium);
  color: var(--text-primary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-secondary);
  border-color: var(--border-dark);
}

.btn-ghost {
  background: transparent;
  border-color: transparent;
  color: var(--text-secondary);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.btn-success {
  background: var(--color-success);
  border-color: var(--color-success);
  color: white;
}

.btn-warning {
  background: var(--color-warning);
  border-color: var(--color-warning);
  color: white;
}

.btn-error {
  background: var(--color-error);
  border-color: var(--color-error);
  color: white;
}

/* Button Sizes */
.btn-xs {
  padding: var(--space-1) var(--space-2);
  font-size: 1.2rem;
}

.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: 1.3rem;
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: 1.6rem;
}

.btn-xl {
  padding: var(--space-5) var(--space-8);
  font-size: 1.8rem;
}

/* Icon Buttons */
.btn-icon {
  padding: var(--space-3);
  width: auto;
  height: auto;
  aspect-ratio: 1;
}

.btn-icon.btn-sm {
  padding: var(--space-2);
}

.btn-icon.btn-lg {
  padding: var(--space-4);
}

/* ===================================
   5. FORM SYSTEM
   =================================== */

.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: 1.3rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: 1.4rem;
  line-height: 1.5;
  color: var(--text-primary);
  background: var(--bg-primary);
  transition: all var(--transition-fast);
  outline: none;
  margin: 0;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  border-color: var(--vertoie-orange);
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--text-muted);
}

.form-textarea {
  resize: vertical;
  min-height: 8rem;
}

/* Input Sizes */
.form-input-sm { padding: var(--space-2) var(--space-3); font-size: 1.3rem; }
.form-input-lg { padding: var(--space-4) var(--space-5); font-size: 1.6rem; }

/* ===================================
   6. CARD SYSTEM
   =================================== */

.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-fast);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-light);
}

.card-content {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

.card-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.card-subtitle {
  font-size: 1.4rem;
  color: var(--text-secondary);
  margin: 0;
}

/* ===================================
   7. LAYOUT UTILITIES
   =================================== */

/* Container */
.container {
  max-width: 120rem;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.container-sm { max-width: 64rem; }
.container-md { max-width: 76.8rem; }
.container-lg { max-width: 102.4rem; }

/* Grid System */
.grid {
  display: grid;
  gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

/* Gap variations */
.gap-3 { gap: var(--space-3); }

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

/* Spacing Utilities */
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

/* Margin & Padding */
.m-0 { margin: 0; }
.m-auto { margin: auto; }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }
.mx-auto { margin-left: auto; margin-right: auto; }

.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
.py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }
.py-12 { padding-top: var(--space-12); padding-bottom: var(--space-12); }

/* Additional Utilities */
.text-center { text-align: center; }
.font-semibold { font-weight: 600; }
.flex-1 { flex: 1; }
.h-5 { height: 1.25rem; }
.w-5 { width: 1.25rem; }
.h-8 { height: 2rem; }
.w-8 { width: 2rem; }

/* ===================================
   8. COMPONENT PATTERNS
   =================================== */

/* Chat/Message Components */
.message {
  display: flex;
  max-width: 100%;
  margin-bottom: var(--space-4);
}

.ai-message {
  justify-content: flex-start;
}

.user-message {
  justify-content: flex-end;
}

.message-content {
  max-width: 85%;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-xl);
  font-size: 1.4rem;
  line-height: 1.6;
  word-wrap: break-word;
}

.ai-message .message-content {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  color: var(--text-primary);
}

.user-message .message-content {
  background: var(--vertoie-orange);
  color: white;
}

/* Onboarding Specific */
.onboarding-body {
  background: var(--bg-secondary);
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.onboarding-main {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.step-hint {
  margin-top: var(--space-8);
  text-align: center;
}

.step-hint p {
  color: var(--text-secondary);
  font-size: 1.3rem;
  font-style: italic;
}

/* Progress Bar */
.progress {
  width: 100%;
  height: 0.8rem;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--vertoie-orange);
  border-radius: var(--radius-full);
  transition: width var(--transition-normal);
}

/* Badge */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  font-size: 1.2rem;
  font-weight: 500;
  border-radius: var(--radius-full);
  white-space: nowrap;
}

.badge-primary { background: var(--vertoie-orange); color: white; }
.badge-secondary { background: var(--bg-tertiary); color: var(--text-secondary); }
.badge-success { background: var(--color-success); color: white; }
.badge-warning { background: var(--color-warning); color: white; }
.badge-error { background: var(--color-error); color: white; }

/* Avatar */
.avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  border-radius: var(--radius-full);
  background: var(--vertoie-orange);
  color: white;
  font-weight: 600;
  font-size: 1.4rem;
  text-transform: uppercase;
  overflow: hidden;
}

.avatar-sm { width: 3rem; height: 3rem; font-size: 1.2rem; }
.avatar-lg { width: 5rem; height: 5rem; font-size: 1.8rem; }
.avatar-xl { width: 6rem; height: 6rem; font-size: 2rem; }

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Loading Spinner */
.spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--vertoie-orange);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

.loading-spinner {
  width: 4rem;
  height: 4rem;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--vertoie-orange);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Module Components */
.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(30rem, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.module-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  transition: all var(--transition-fast);
}

.module-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
}

.module-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.module-description {
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
  line-height: 1.6;
}

.module-actions {
  display: flex;
  gap: var(--space-2);
  justify-content: flex-end;
}

.module-action-btn {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: 1.3rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.module-action-btn:hover {
  background: var(--bg-secondary);
  border-color: var(--border-dark);
  color: var(--text-primary);
}

.module-action-btn.accept {
  background: var(--color-success);
  border-color: var(--color-success);
  color: white;
}

.module-action-btn.accept:hover {
  background: #059669;
  border-color: #059669;
}

/* Organization Components */
.organization-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  transition: all var(--transition-fast);
}

.organization-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.organization-header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.organization-icon {
  width: 4rem;
  height: 4rem;
  background: var(--vertoie-orange);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: white;
}

.organization-info h3 {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
}

.organization-info p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 1.4rem;
}

.organization-meta {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
  font-size: 1.3rem;
  color: var(--text-muted);
}

.organization-actions {
  display: flex;
  justify-content: flex-end;
}

/* Chat Components */
.chat-input {
  display: flex;
  gap: var(--space-3);
  align-items: flex-end;
  margin-top: var(--space-4);
}

.chat-input textarea {
  flex: 1;
  min-height: 4rem;
  max-height: 12rem;
  resize: vertical;
  padding: var(--space-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: 1.4rem;
  line-height: 1.5;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.chat-input textarea:focus {
  outline: none;
  border-color: var(--vertoie-orange);
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.chat-messages {
  overflow-y: auto;
  padding: var(--space-4);
  background: var(--bg-secondary);
}

/* Modal Components */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.modal {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 50rem;
  width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-light);
}

.modal-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 2.4rem;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: var(--text-primary);
}

.modal-content {
  padding: var(--space-6);
}

.modal-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  padding: var(--space-6);
  border-top: 1px solid var(--border-light);
}

/* ===================================
   9. RESPONSIVE DESIGN
   =================================== */

@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-4);
  }
  
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: 1fr;
  }
  
  .btn-lg {
    padding: var(--space-3) var(--space-5);
    font-size: 1.5rem;
  }
  
  .card-header,
  .card-content,
  .card-footer {
    padding: var(--space-4);
  }
}

@media (max-width: 480px) {
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  .btn + .btn {
    margin-top: var(--space-2);
  }
}

/* ===================================
   10. UTILITY CLASSES
   =================================== */

.hidden { display: none !important; }
.visible { visibility: visible; }
.invisible { visibility: hidden; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.border { border: 1px solid var(--border-light); }
.border-t { border-top: 1px solid var(--border-light); }
.border-b { border-bottom: 1px solid var(--border-light); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

/* ====================================
   PRICING COMPONENTS
   ==================================== */

/* Pricing Cards Container */
.pricing-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 768px) {
    .pricing-cards {
        grid-template-columns: repeat(5, 1fr);
        gap: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .pricing-cards {
        gap: 2rem;
    }
}

/* Individual Pricing Card */
.pricing-card {
    position: relative;
    background: var(--surface-primary);
    border: 1px solid var(--border-subtle);
    border-radius: 16px;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    height: 100%;
    transition: all 0.3s ease;
    overflow: hidden;
}

.pricing-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--border-focus);
}

/* Featured Card (Professional) */
.pricing-card--featured {
    border: 2px solid var(--vertoie-orange);
    background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(255, 107, 53, 0.02) 100%);
    transform: scale(1.05);
    z-index: 10;
}

.pricing-card--featured:hover {
    transform: scale(1.05) translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* Popular Badge */
.pricing-card__badge {
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, var(--vertoie-orange) 0%, var(--vertoie-orange-dark) 100%);
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 0 0 12px 12px;
    font-size: 1.2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-md);
}

/* Card Header */
.pricing-card__header {
    text-align: center;
    margin-bottom: 2rem;
    margin-top: 1rem;
}

.pricing-card__title {
    font-size: 2.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
}

.pricing-card__description {
    font-size: 1.4rem;
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
}

/* Pricing Display */
.pricing-card__price {
    text-align: center;
    margin-bottom: 2.5rem;
}

.pricing-card__price-main {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
}

.pricing-card__currency {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-secondary);
}

.pricing-card__amount {
    font-size: 4.8rem;
    font-weight: 800;
    color: var(--text-primary);
    line-height: 1;
}

.pricing-card__period {
    font-size: 1.6rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.pricing-card__price-note {
    font-size: 1.4rem;
    color: var(--success-600);
    font-weight: 600;
}

/* Features List */
.pricing-card__features {
    flex: 1;
    margin-bottom: 2.5rem;
}

.pricing-card__feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.pricing-card__feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.4rem;
    color: var(--text-primary);
    line-height: 1.5;
}

.pricing-card__check {
    width: 2rem;
    height: 2rem;
    color: var(--success-500);
    flex-shrink: 0;
}

/* CTA Button */
.pricing-card__cta {
    margin-top: auto;
}

.pricing-card__button {
    width: 100%;
    justify-content: center;
    font-weight: 600;
    font-size: 1.6rem;
    padding: 1.2rem 1.5rem;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.pricing-card--featured .pricing-card__button.btn-primary {
    background: linear-gradient(135deg, var(--vertoie-orange) 0%, var(--vertoie-orange-dark) 100%);
    box-shadow: var(--shadow-md);
}

.pricing-card--featured .pricing-card__button.btn-primary:hover {
    background: linear-gradient(135deg, var(--vertoie-orange-dark) 0%, #CC4100 100%);
    box-shadow: var(--shadow-lg);
    transform: translateY(-1px);
}

/* ====================================
   PRICING PAGE SPECIFIC STYLES
   ==================================== */

/* Larger text for pricing page cards */
.pricing-page-plans .pricing-card__title {
    font-size: 2.8rem;
}

.pricing-page-plans .pricing-card__description {
    font-size: 1.6rem;
}

.pricing-page-plans .pricing-card__amount {
    font-size: 5.2rem;
}

.pricing-page-plans .pricing-card__period {
    font-size: 1.8rem;
}

.pricing-page-plans .pricing-card__feature {
    font-size: 1.6rem;
}

.pricing-page-plans .pricing-card__button {
    font-size: 1.8rem;
    padding: 1.4rem 2rem;
}

/* Mobile Responsiveness */
@media (max-width: 767px) {
    .pricing-cards {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1rem;
    }
    
    .pricing-card--featured {
        transform: none;
        order: -1; /* Show Professional first on mobile */
    }
    
    .pricing-card--featured:hover {
        transform: translateY(-4px);
    }
    
    .pricing-card {
        padding: 1.5rem;
    }
    
    .pricing-card__amount {
        font-size: 2.5rem;
    }
}

/* Pricing Cards Container - Main (3 cards) */
.pricing-cards--main {
    grid-template-columns: repeat(3, 1fr);
    max-width: 1200px;
    gap: 2.5rem;
}

@media (min-width: 768px) {
    .pricing-cards--main {
        grid-template-columns: repeat(3, 1fr);
        gap: 2.5rem;
    }
}

@media (min-width: 1024px) {
    .pricing-cards--main {
        gap: 3rem;
    }
}

/* Mobile responsiveness for main cards */
@media (max-width: 767px) {
    .pricing-cards--main {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1rem;
    }
    
    .pricing-cards--main .pricing-card--featured {
        order: -1; /* Show Professional first on mobile */
    }
}

/* ====================================
   PRICING PAGE COMPONENTS
   ==================================== */

.pricing-page-header {
    padding: 6rem 0 4rem;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.pricing-page-header h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.pricing-page-plans {
    padding: 4rem 0;
}

.pricing-page-footer {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.pricing-contact {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.pricing-contact h2 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: var(--text-primary);
}

.pricing-contact p {
    font-size: 1.8rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    line-height: 1.6;
}

.pricing-contact-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.pricing-contact-actions .btn {
    padding: 1.2rem 2.5rem;
    font-size: 1.6rem;
    font-weight: 600;
}

@media (max-width: 768px) {
    .pricing-page-header h1 {
        font-size: 2.5rem;
    }
    
    .pricing-contact h2 {
        font-size: 2rem;
    }
    
    .pricing-contact p {
        font-size: 1.1rem;
    }
    
    .pricing-contact-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .pricing-contact-actions .btn {
        width: 100%;
        max-width: 300px;
    }
} 