/* Vertoie Marketing Site - Clean & Minimalist */
/* Updated to work with the new design system */

:root {
  /* Vertoie Brand Colors */
  --vertoie-orange: #FF6B35;
  --vertoie-orange-hover: #E65100;
  
  /* Clean Greys & Blacks */
  --bg-primary: #FFFFFF;
  --bg-secondary: #FAFAFA;
  --bg-accent: #F5F5F5;
  --text-primary: #000000;
  --text-secondary: #666666;
  --text-muted: #999999;
  --border-light: #E0E0E0;
  --border-medium: #CCCCCC;
}

[data-theme="dark"] {
  --bg-primary: #1E2330; /* Darker blue-gray from image */
  --bg-secondary: #252B3B; /* Lighter blue-gray for secondary elements */
  --bg-accent: #2D3446; /* Accent blue-gray */
  --text-primary: #E0E0E0; /* Lighter text for better contrast */
  --text-secondary: #B0B0B0; /* Slightly darker secondary text */
  --text-muted: #757575; /* Muted text */
  --border-light: #3A4055; /* Darker border */
  --border-medium: #4A5169; /* Medium border */
}

/* Reset Milligram defaults and overrides */
body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  transition: all 0.2s ease;
}

/* Remove Milligram purple defaults */
a:not(.btn) {
  color: var(--vertoie-orange);
  text-decoration: none;
}

a:not(.btn):hover {
  color: var(--vertoie-orange-hover);
}

/* Buttons - Clean Override */
.button,
.button-primary,
button,
input[type='submit'] {
  background-color: var(--vertoie-orange);
  border: 1px solid var(--vertoie-orange);
  color: #FFFFFF;
  padding: 1.2rem 2.4rem;
  font-weight: 500;
  border-radius: 6px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 1.4rem;
  line-height: 1;
  vertical-align: middle;
}

.button:hover,
.button-primary:hover,
button:hover,
input[type='submit']:hover {
  background-color: var(--vertoie-orange-hover);
  border-color: var(--vertoie-orange-hover);
  color: #FFFFFF; /* Ensuring button text is white on hover */
  transform: translateY(-1px);
}

.button-secondary {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
}

.button-secondary:hover {
  background: var(--bg-accent);
  color: var(--text-primary); /* Ensure text color contrasts with new bg-accent */
}

/* Forms - Themed */
input[type='email'],
input[type='text'],
input[type='password'],
input[type='number'],
textarea,
select {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-light);
    color: var(--text-primary);
    border-radius: 6px;
    padding: 1rem;
    box-shadow: none;
    transition: all 0.2s ease;
}

input[type='email']:focus,
input[type='text']:focus,
input[type='password']:focus,
input[type='number']:focus,
textarea:focus,
select:focus {
    border-color: var(--vertoie-orange);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.2);
}

label {
    font-weight: 500;
    margin-bottom: 0.8rem;
    color: var(--text-secondary);
}

/* Form Groups and Styling */
.form-group {
    margin-bottom: 2rem;
}

.form-label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.8rem;
    color: var(--text-secondary);
    font-size: 1.4rem;
}

.form-input {
    width: 100%;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-light);
    color: var(--text-primary);
    border-radius: 6px;
    padding: 1rem;
    box-shadow: none;
    transition: all 0.2s ease;
    font-size: 1.4rem;
}

.form-input:focus {
    border-color: var(--vertoie-orange);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.2);
}

/* Button Styles */
.btn {
    background-color: var(--vertoie-orange);
    border: 1px solid var(--vertoie-orange);
    color: #FFFFFF;
    padding: 1.2rem 2.4rem;
    font-weight: 500;
    border-radius: 6px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.2s ease;
    cursor: pointer;
    font-size: 1.4rem;
    line-height: 1;
    vertical-align: middle;
    border: none;
}

.btn:hover {
    background-color: var(--vertoie-orange-hover);
    color: #FFFFFF;
    transform: translateY(-1px);
}

.btn-primary {
    background-color: var(--vertoie-orange);
    border-color: var(--vertoie-orange);
}

.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-medium);
}

.btn-secondary:hover {
    background: var(--bg-accent);
    color: var(--text-primary);
}

.w-full {
    width: 100%;
    margin-top: 0.8rem;
}

/* Navigation - Clean & Minimal */
.navbar {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  padding: 2rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(20px);
}

.navbar .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 120rem;
  margin: 0 auto;
  padding: 0 3rem;
}

.nav-brand .logo {
  height: 3rem;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 3rem;
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 400;
  font-size: 1.5rem;
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: var(--vertoie-orange);
}

.theme-toggle {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-light) !important;
  border-radius: 6px;
  padding: 0.8rem !important;
  cursor: pointer;
  color: var(--text-secondary) !important;
  font-size: 1.6rem !important;
  transition: all 0.2s ease;
  font-weight: normal !important;
  transform: none !important;
  text-decoration: none !important;
}

.theme-toggle:hover {
  background: var(--bg-accent) !important;
  border-color: var(--border-medium) !important;
  color: var(--text-primary) !important;
  transform: none !important;
}

.theme-toggle:focus {
  outline: none;
  border-color: var(--vertoie-orange) !important;
  box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.2);
  background: var(--bg-primary) !important;
}

.theme-icon {
  display: none;
}

[data-theme="light"] .light-icon {
  display: inline;
}

[data-theme="dark"] .dark-icon {
  display: inline;
}

/* Hero Section */
.hero {
  padding: 8rem 0;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.hero .container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6rem;
  align-items: center;
  max-width: 120rem;
  margin: 0 auto;
  padding: 0 3rem;
}

.hero-content h1 {
  font-size: 4.8rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 2rem;
  color: var(--text-primary);
}

.hero-content .highlight {
  color: var(--vertoie-orange);
}

.hero-content p {
  font-size: 1.8rem;
  color: var(--text-secondary);
  margin-bottom: 3rem;
  line-height: 1.6;
}

.hero-cta {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.mockup-browser {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  width: 100%;
  max-width: 48rem;
}

.mockup-header {
  background: var(--bg-secondary);
  padding: 1.2rem 2rem;
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.mockup-dots {
  display: flex;
  gap: 0.6rem;
}

.mockup-dots span {
  width: 1.2rem;
  height: 1.2rem;
  border-radius: 50%;
}

.mockup-dots span:first-child { background: #FF5F56; }
.mockup-dots span:nth-child(2) { background: #FFBD2E; }
.mockup-dots span:nth-child(3) { background: #27CA3F; }

.mockup-content {
  padding: 2rem;
  background: var(--bg-primary);
  min-height: 30rem;
}

.mockup-nav {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-light);
}

.mockup-nav-item {
  padding: 1rem 0;
  color: var(--text-secondary);
  font-weight: 500;
  border-bottom: 2px solid transparent;
  cursor: pointer;
}

.mockup-nav-item.active {
  color: var(--vertoie-orange);
  border-bottom-color: var(--vertoie-orange);
}

.mockup-cards {
  display: grid;
  gap: 1.5rem;
}

.mockup-card {
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid var(--border-light);
}

.mockup-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.mockup-card-content {
  color: var(--text-secondary);
  font-size: 1.4rem;
}

.mockup-card-content:last-child {
  margin-bottom: 0;
}

/* Features Section */
.features {
  padding: 8rem 0;
  background: var(--bg-primary);
}

.features .container {
  max-width: 120rem;
  margin: 0 auto;
  padding: 0 3rem;
}

.section-header {
  text-align: center;
  margin-bottom: 6rem;
}

.section-footer {
  text-align: center;
  margin-top: 6rem;
}

.section-header h2, .section-footer h2 {
  font-size: 3.6rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: var(--text-primary);
}

.section-header p, .section-footer p {
  font-size: 1.8rem;
  color: var(--text-secondary);
  max-width: 60rem;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
  gap: 4rem;
  margin-bottom: 6rem;
}

.feature-card {
  text-align: center;
  padding: 3rem 2rem;
  border-radius: 12px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  font-size: 4rem;
  margin-bottom: 2rem;
  display: block;
}

.feature-card h3 {
  font-size: 2.4rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.feature-card p {
  font-size: 1.6rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Pricing Section */
.pricing {
  padding: 8rem 0;
  background: var(--bg-secondary);
}

.pricing .container {
  max-width: 120rem;
  margin: 0 auto;
  padding: 0 3rem;
}

/* Pricing cards styles moved to vertoie-design-system.css */

.pricing-badge {
  position: absolute;
  top: -1rem;
  left: 50%;
  transform: translateX(-50%);
  background: var(--vertoie-orange);
  color: white;
  padding: 0.5rem 2rem;
  border-radius: 2rem;
  font-size: 1.2rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.pricing-header {
  margin-bottom: 3rem;
}

.plan-name {
  font-size: 2.4rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.plan-description {
  font-size: 1.4rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.pricing-price {
  margin-bottom: 3rem;
}

.price {
  font-size: 4.8rem;
  font-weight: 700;
  color: var(--text-primary);
}

.period {
  font-size: 1.6rem;
  color: var(--text-secondary);
  font-weight: 400;
}

.pricing-features {
  text-align: left;
  margin-bottom: 3rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 1.4rem;
  color: var(--text-primary);
}

.feature-check {
  width: 2rem;
  height: 2rem;
  background: var(--color-success);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.pricing-cta {
  margin-top: auto;
}

.pricing-cta .btn {
  width: 100%;
  padding: 1.2rem 2rem;
  font-size: 1.6rem;
}

.enterprise-note {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  padding: 3rem;
  text-align: center;
  max-width: 60rem;
  margin: 0 auto;
}

.enterprise-note p {
  font-size: 1.6rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.enterprise-note a {
  color: var(--vertoie-orange);
  font-weight: 600;
  text-decoration: none;
}

.enterprise-note a:hover {
  text-decoration: underline;
}

.plan-name {
  font-size: 2.4rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.plan-price {
  font-size: 3.6rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.plan-period {
  color: var(--text-secondary);
  font-size: 1.4rem;
}

.cta-row td {
  padding-top: 2rem;
}

.cta-row .btn {
  width: 100%;
  padding: 1.2rem 2rem;
  font-size: 1.6rem;
}

/* CTA Section */
.cta {
  padding: 8rem 0;
  background: linear-gradient(135deg, var(--vertoie-orange) 0%, var(--vertoie-orange-hover) 100%);
  color: white;
}

.cta .container {
  max-width: 80rem;
  margin: 0 auto;
  padding: 0 3rem;
  text-align: center;
}

.cta h2 {
  font-size: 3.6rem;
  font-weight: 700;
  margin-bottom: 2rem;
}

.cta p {
  font-size: 1.8rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

.cta .btn {
  background: white;
  color: var(--vertoie-orange);
  border: 2px solid white;
  font-size: 1.8rem;
  padding: 1.5rem 3rem;
  font-weight: 700;
  text-shadow: none;
}

.cta .btn:hover {
  background: transparent;
  color: white;
  border-color: white;
}

.pricing-loading {
  text-align: center;
  padding: 4rem 0;
}

.loading-spinner {
  width: 4rem;
  height: 4rem;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--vertoie-orange);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 2rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.htmx-request .pricing-table {
  opacity: 0.6;
}

/* Footer */
.footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-light);
  padding: 6rem 0 3rem;
}

.footer .container {
  max-width: 120rem;
  margin: 0 auto;
  padding: 0 3rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 6rem;
  margin-bottom: 4rem;
}

.footer-brand {
  display: flex;
  flex-direction: column;
}

.footer-brand .logo {
  height: 3rem;
  margin-bottom: 2rem;
}

.footer-brand p {
  color: var(--text-secondary);
  font-size: 1.4rem;
  line-height: 1.6;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4rem;
}

.footer-group h4 {
  font-size: 1.6rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: var(--text-primary);
}

.footer-group a {
  display: block;
  color: var(--text-secondary);
  text-decoration: none;
  margin-bottom: 1rem;
  font-size: 1.4rem;
  transition: color 0.2s ease;
}

.footer-group a:hover {
  color: var(--vertoie-orange);
}

.footer-bottom {
  border-top: 1px solid var(--border-light);
  padding-top: 3rem;
  text-align: center;
}

.footer-bottom p {
  color: var(--text-secondary);
  font-size: 1.4rem;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 4rem;
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

/* Old pricing responsive styles removed - now handled in design system */

@media (max-width: 992px) {
  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
    gap: 3rem;
  }
}

@media (max-width: 768px) {
  .hero .container {
    grid-template-columns: 1fr;
    gap: 4rem;
    text-align: center;
  }

  .navbar .container {
    padding: 0 2rem;
  }

  .nav-links {
    gap: 2rem;
  }

  .hero-content h1 {
    font-size: 3.6rem;
  }

  .hero-content p {
    font-size: 1.6rem;
  }

  .section-header h2 {
    font-size: 2.8rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  /* Pricing responsive styles handled in design system */

  .price {
    font-size: 3.6rem;
  }
}

@media (max-width: 480px) {
  .hero-content h1 {
    font-size: 2.8rem;
  }

  .hero-content p {
    font-size: 1.4rem;
  }

  .pricing-card {
    padding: 2rem;
  }

  .price {
    font-size: 3rem;
  }

  .feature-card {
    padding: 2rem;
  }
}

/* Compact Magic Link Info Box */
.magic-link-info {
  text-align: center;
  padding: 1.6rem 2rem;
  border-radius: 12px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  margin-bottom: 2.4rem;
}

.magic-link-icon {
  font-size: 2.4rem;
  margin-bottom: 0.8rem;
  display: block;
}

.magic-link-content h3 {
  font-size: 1.6rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.magic-link-content p {
  font-size: 1.4rem;
  color: var(--text-secondary);
  margin-bottom: 0;
  line-height: 1.5;
}
