package email

import (
	"context"
	"fmt"
	"time"

	"github.com/mailgun/mailgun-go/v4"
)

// EmailService provides a client for sending emails.
type EmailService struct {
	mg     mailgun.Mailgun
	sender string
}

// NewEmailService creates a new email service with the given Mailgun configuration.
func NewEmailService(domain, apiKey, sender string) *EmailService {
	mg := mailgun.NewMailgun(domain, apiKey)
	return &EmailService{
		mg:     mg,
		sender: sender,
	}
}

// SendMagicLink sends a registration or login magic link email.
func (es *EmailService) SendMagicLink(recipient, magicLink string) error {
	subject := "Your Vertoie Magic Link"
	// In a real app, you would use HTML templates for emails
	body := fmt.Sprintf(`
		<html>
		<body>
			<h2>Welcome to Vertoie!</h2>
			<p>Click the link below to sign in. This link will expire in 15 minutes and can only be used once.</p>
			<p><a href="%s" style="font-size: 16px; font-family: Helvetica, Arial, sans-serif; color: #ffffff; text-decoration: none; border-radius: 5px; background-color: #FF6B35; border-top: 12px solid #FF6B35; border-bottom: 12px solid #FF6B35; border-right: 18px solid #FF6B35; border-left: 18px solid #FF6B35; display: inline-block;">Sign In to Vertoie</a></p>
			<p>If you did not request this email, you can safely ignore it.</p>
			<hr>
			<p><small>Link not working? Copy and paste this URL into your browser: %s</small></p>
		</body>
		</html>
	`, magicLink, magicLink)

	message := es.mg.NewMessage(es.sender, subject, "", recipient)
	message.SetHtml(body)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	_, _, err := es.mg.Send(ctx, message)
	if err != nil {
		return fmt.Errorf("failed to send email via mailgun: %w", err)
	}

	return nil
}
