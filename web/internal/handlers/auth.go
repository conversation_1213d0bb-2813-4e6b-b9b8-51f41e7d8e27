package handlers

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"log/slog"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"github.com/vertoie/models"
	"github.com/vertoie/web/internal/email"
	"gorm.io/gorm"
)

// AuthHandler handles authentication-related requests
type AuthHandler struct {
	DB           *gorm.DB
	Logger       *slog.Logger
	EmailService *email.EmailService
}

// NewAuthHandler creates a new authentication handler
func NewAuthHandler(db *gorm.DB, logger *slog.Logger, emailService *email.EmailService) *AuthHandler {
	return &AuthHandler{
		DB:           db,
		Logger:       logger,
		EmailService: emailService,
	}
}

// MagicLinkRequest represents the magic link request
type MagicLinkRequest struct {
	Email string `json:"email" form:"email"`
}

// RegisterRequest represents the registration form data
type RegisterRequest struct {
	Email     string `json:"email" form:"email"`
	FirstName string `json:"first_name" form:"first_name"`
	LastName  string `json:"last_name" form:"last_name"`
}

// RequestMagicLink handles magic link requests
func (h *AuthHandler) RequestMagicLink(c *fiber.Ctx) error {
	var req MagicLinkRequest
	if err := c.BodyParser(&req); err != nil {
		h.Logger.Warn("Failed to parse magic link request", "error", err)
		return c.Status(http.StatusBadRequest).Render("partials/error-message", fiber.Map{
			"Message": "Invalid request format.",
		})
	}

	// Validate input
	if req.Email == "" {
		return c.Status(400).JSON(fiber.Map{
			"error": "Email is required",
		})
	}

	// Check if user exists
	var user models.User
	if err := h.DB.Where("email = ?", req.Email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.Logger.Warn("Magic link requested for non-existent user", "email", req.Email)
			// Still return a success-like message to prevent email enumeration
			return c.Render("partials/magic-link-sent", fiber.Map{
				"Email": req.Email,
			})
		}
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to process request",
		})
	}

	// Create magic link token
	token, err := h.createMagicLinkToken(h.DB, user.ID, user.Email)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create magic link",
		})
	}

	// Send magic link email
	magicLink := fmt.Sprintf("http://localhost:%s/auth/verify?token=%s", "8001", token)
	if h.EmailService != nil {
		if err := h.EmailService.SendMagicLink(user.Email, magicLink); err != nil {
			h.Logger.Error("Failed to send magic link email", "error", err, "email", user.Email)
			// Don't fail the request, but log the error
			// In production, you might want to queue this for retry
		}
	} else {
		h.Logger.Warn("Email service not available, magic link not sent", "email", user.Email, "token", token)
	}

	h.Logger.Info("Magic link email sent", "email", req.Email)
	return c.Render("partials/magic-link-sent", fiber.Map{
		"Email": req.Email,
		"Token": token,
	})
}

// Register handles user registration
func (h *AuthHandler) Register(c *fiber.Ctx) error {
	var req RegisterRequest
	if err := c.BodyParser(&req); err != nil {
		h.Logger.Warn("Failed to parse registration request", "error", err)
		return c.Status(http.StatusBadRequest).Render("partials/error-message", fiber.Map{
			"Message": "Invalid request format.",
		})
	}

	// Validate input
	if req.Email == "" || req.FirstName == "" || req.LastName == "" {
		return c.Status(400).JSON(fiber.Map{
			"error": "Email, first name, and last name are required",
		})
	}

	// Check if user already exists
	var existingUser models.User
	if err := h.DB.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		h.Logger.Warn("Registration attempt for existing email", "email", req.Email)
		return c.Status(http.StatusOK).Render("partials/error-message", fiber.Map{
			"Message": "A user with this email address already exists.",
		})
	}

	var user models.User
	var token string

	// Use a transaction to ensure user, organization, and token are created successfully
	err := h.DB.Transaction(func(tx *gorm.DB) error {
		// Create user in transaction
		newUser := models.User{
			FirstName: req.FirstName,
			LastName:  req.LastName,
			Email:     req.Email,
		}
		if err := tx.Create(&newUser).Error; err != nil {
			return err // Rollback
		}
		user = newUser

		// Create default organization for new user
		orgName := user.FirstName + "'s Business"
		slug := h.generateSlug(orgName)
		
		// Ensure slug is unique by appending random suffix if needed
		var existingOrg models.Organization
		if err := tx.Where("slug = ?", slug).First(&existingOrg).Error; err == nil {
			// Slug exists, append user ID to make it unique
			slug = slug + "-" + user.ID.String()[:8]
		}

		organization := models.Organization{
			Name:        orgName,
			Slug:        slug,
			Description: "My business organization",
			AppStatus:   models.AppStatusSetup, // Ensures onboarding is required
		}

		if err := tx.Create(&organization).Error; err != nil {
			return err // Rollback
		}

		// Create organization-user relationship
		orgUser := models.OrganizationUser{
			UserID:         user.ID,
			OrganizationID: organization.ID,
			Role:           string(models.RoleOwner),
		}

		if err := tx.Create(&orgUser).Error; err != nil {
			return err // Rollback
		}

		// Create magic link token in transaction
		var err error
		token, err = h.createMagicLinkToken(tx, user.ID, user.Email)
		if err != nil {
			return err // Rollback
		}

		return nil // Commit
	})

	if err != nil {
		h.Logger.Error("Failed to complete registration transaction", "error", err, "email", req.Email)
		return c.Status(http.StatusOK).Render("partials/error-message", fiber.Map{
			"Message": "We couldn't create your account at this time. Please try again later.",
		})
	}

	// Send magic link email
	magicLink := fmt.Sprintf("http://localhost:%s/auth/verify?token=%s", "8001", token)
	if h.EmailService != nil {
		if err := h.EmailService.SendMagicLink(user.Email, magicLink); err != nil {
			h.Logger.Error("Failed to send magic link email", "error", err, "email", user.Email)
			// Don't fail the registration, but log the error
			// In production, you might want to queue this for retry
		}
	} else {
		h.Logger.Warn("Email service not available, magic link not sent", "email", user.Email, "token", token)
	}

	h.Logger.Info("New user registered and magic link email sent", "email", req.Email)

	// Return success response
	return c.Render("partials/registration-successful", fiber.Map{
		"Email": req.Email,
		"Token": token,
	})
}

// VerifyMagicLink handles magic link verification
func (h *AuthHandler) VerifyMagicLink(c *fiber.Ctx) error {
	if c.Get("HX-Request") == "true" {
		token := c.Query("token")
		if token == "" {
			return c.Status(http.StatusBadRequest).SendString("Invalid verification link: no token provided.")
		}

		var authToken models.AuthToken
		var sessionToken string
		var user models.User
		var needsOnboarding bool

		err := h.DB.Transaction(func(tx *gorm.DB) error {
			if err := tx.Where("token_hash = ? AND expires_at > ? AND used_at IS NULL", token, time.Now()).First(&authToken).Error; err != nil {
				return err
			}
			now := time.Now()
			if err := tx.Model(&authToken).Update("used_at", &now).Error; err != nil {
				return err
			}
			
			// Get user to check if they have organizations in setup status
			if err := tx.Where("id = ?", authToken.UserID).First(&user).Error; err != nil {
				return err
			}
			
			// Check if user has any organization in setup status
			var org models.Organization
			orgErr := tx.
				Joins("JOIN vertoie.organization_users ON vertoie.organization_users.organization_id = vertoie.organizations.id").
				Where("vertoie.organization_users.user_id = ? AND vertoie.organizations.app_status = ?", user.ID, models.AppStatusSetup).
				First(&org).Error
			
			if orgErr == nil {
				// User has an organization in setup status, needs onboarding
				needsOnboarding = true
				// Set this org as current
				h.setCurrentOrganization(c, org.ID)
			}
			
			var err error
			sessionToken, err = h.createSession(tx, authToken.UserID)
			if err != nil {
				return err
			}
			return nil
		})

		if err != nil {
			return c.Status(http.StatusUnauthorized).SendString("This link is invalid, has expired, or has already been used.")
		}

		c.Cookie(&fiber.Cookie{
			Name:     "session_token",
			Value:    sessionToken,
			Expires:  time.Now().Add(24 * time.Hour),
			HTTPOnly: true,
			Secure:   false, // Set to true in production with HTTPS
			SameSite: "Strict",
			Path:     "/",
		})

		// Redirect to onboarding if new user, otherwise dashboard
		if needsOnboarding {
			c.Set("HX-Redirect", "/onboarding")
		} else {
			c.Set("HX-Redirect", "/dashboard")
		}
		return c.SendStatus(204)
	}

	return c.Render("partials/magic-link-loading", fiber.Map{
		"Token":   c.Query("token"),
		"Message": "Logging you in securely. Please wait…",
	}, "base")
}

// Logout handles user logout
func (h *AuthHandler) Logout(c *fiber.Ctx) error {
	// Get session token from cookie
	sessionToken := c.Cookies("session_token")
	if sessionToken != "" {
		// Delete session token - fix field name from "token" to "token_hash"
		h.DB.Where("token_hash = ? AND type = 'session'", sessionToken).Delete(&models.AuthToken{})
	}

	// Clear session cookie
	c.ClearCookie("session_token")

	// Return success response
	if c.Get("HX-Request") == "true" {
		// HTMX request - redirect to home
		c.Set("HX-Redirect", "/")
		return c.SendStatus(200)
	}

	return c.JSON(fiber.Map{
		"message": "Logout successful",
	})
}

// GetCurrentUser returns the current authenticated user
func (h *AuthHandler) GetCurrentUser(c *fiber.Ctx) error {
	user, err := h.getCurrentUser(c)
	if err != nil {
		return c.Status(401).JSON(fiber.Map{
			"error": "Unauthorized",
		})
	}

	return c.JSON(fiber.Map{
		"user": fiber.Map{
			"id":         user.ID,
			"email":      user.Email,
			"first_name": user.FirstName,
			"last_name":  user.LastName,
		},
	})
}

// createMagicLinkToken creates a new magic link token for a user
func (h *AuthHandler) createMagicLinkToken(db *gorm.DB, userID uuid.UUID, email string) (string, error) {
	// Generate random token
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return "", err
	}
	token := hex.EncodeToString(tokenBytes)

	// Create auth token
	authToken := models.AuthToken{
		UserID:    userID,
		Email:     email,
		TokenHash: token, // NOTE: Storing the raw token in the hash field for now
		Type:      "magic_link",
		ExpiresAt: time.Now().Add(15 * time.Minute), // Magic links expire in 15 minutes
	}

	if err := db.Create(&authToken).Error; err != nil {
		return "", err
	}

	return token, nil
}

// createSession creates a new session token for a user
func (h *AuthHandler) createSession(db *gorm.DB, userID uuid.UUID) (string, error) {
	// Generate random token
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return "", err
	}
	token := hex.EncodeToString(tokenBytes)

	// Get user email for the session token
	var user models.User
	if err := db.Where("id = ?", userID).First(&user).Error; err != nil {
		return "", err
	}

	// Create auth token
	authToken := models.AuthToken{
		UserID:    userID,
		Email:     user.Email,
		TokenHash: token,
		Type:      "session",
		ExpiresAt: time.Now().Add(24 * time.Hour), // Sessions expire in 24 hours
	}

	if err := db.Create(&authToken).Error; err != nil {
		return "", err
	}

	return token, nil
}

// getCurrentUser retrieves the current user from the session token
func (h *AuthHandler) getCurrentUser(c *fiber.Ctx) (*models.User, error) {
	sessionToken := c.Cookies("session_token")
	if sessionToken == "" {
		return nil, fmt.Errorf("no session token found")
	}

	// Find the session token in the database
	var authToken models.AuthToken
	err := h.DB.Where("token_hash = ? AND type = 'session' AND expires_at > ?", sessionToken, time.Now()).First(&authToken).Error
	if err != nil {
		return nil, fmt.Errorf("invalid or expired session token")
	}

	// Find the associated user
	var user models.User
	err = h.DB.Where("id = ?", authToken.UserID).First(&user).Error
	if err != nil {
		return nil, err
	}

	return &user, nil
}

// RequireAuth middleware to require authentication
func (h *AuthHandler) RequireAuth(c *fiber.Ctx) error {
	user, err := h.getCurrentUser(c)
	if err != nil {
		// For HTMX requests, return an error response instead of redirecting
		if c.Get("HX-Request") == "true" {
			return c.Status(401).SendString("Authentication required")
		}
		return c.Redirect("/login", 302)
	}
	c.Locals("user", user)
	return c.Next()
}

// RedirectIfAuthenticated middleware to redirect authenticated users away from auth pages
func (h *AuthHandler) RedirectIfAuthenticated(c *fiber.Ctx) error {
	user, err := h.getCurrentUser(c)
	if err == nil && user != nil {
		return c.Redirect("/dashboard", 302)
	}

	return c.Next()
}

// OptionalAuth middleware that sets user context if authenticated, but doesn't require it
func (h *AuthHandler) OptionalAuth(c *fiber.Ctx) error {
	user, err := h.getCurrentUser(c)
	if err == nil && user != nil {
		c.Locals("user", user)
	}
	return c.Next()
}

// setCurrentOrganization sets the current organization in session cookie
func (h *AuthHandler) setCurrentOrganization(c *fiber.Ctx, organizationID uuid.UUID) {
	c.Cookie(&fiber.Cookie{
		Name:     "current_org",
		Value:    organizationID.String(),
		Expires:  time.Now().Add(24 * time.Hour),
		HTTPOnly: true,
		Secure:   false, // Set to true in production with HTTPS
		SameSite: "Strict",
		Path:     "/",
	})
}

// getCurrentOrganization gets the current organization from session cookie
func (h *AuthHandler) getCurrentOrganization(c *fiber.Ctx) (uuid.UUID, error) {
	orgCookie := c.Cookies("current_org")
	if orgCookie == "" {
		return uuid.Nil, fmt.Errorf("no organization in session")
	}

	orgID, err := uuid.Parse(orgCookie)
	if err != nil {
		return uuid.Nil, fmt.Errorf("invalid organization ID in session")
	}

	return orgID, nil
}

// clearCurrentOrganization removes the current organization from session
func (h *AuthHandler) clearCurrentOrganization(c *fiber.Ctx) {
	c.Cookie(&fiber.Cookie{
		Name:     "current_org",
		Value:    "",
		Expires:  time.Now().Add(-1 * time.Hour), // Expire in the past
		HTTPOnly: true,
		Secure:   false,
		SameSite: "Strict",
		Path:     "/",
	})
}

// generateSlug creates a URL-friendly slug from a name
func (h *AuthHandler) generateSlug(name string) string {
	// Simple slug generation - convert to lowercase, replace spaces with hyphens
	slug := strings.ToLower(name)
	slug = strings.ReplaceAll(slug, " ", "-")
	slug = strings.ReplaceAll(slug, "_", "-")
	// Remove special characters
	slug = regexp.MustCompile(`[^a-z0-9-]`).ReplaceAllString(slug, "")
	// Remove multiple consecutive hyphens
	slug = regexp.MustCompile(`-+`).ReplaceAllString(slug, "-")
	// Remove leading/trailing hyphens
	slug = strings.Trim(slug, "-")
	return slug
}
