package handlers

import (
	"fmt"
	"log/slog"
	"regexp"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"github.com/vertoie/models"
	"github.com/vertoie/web/internal/llm"
	"gorm.io/gorm"
)

type Handlers struct {
	DB                     *gorm.DB
	Logger                 *slog.Logger
	BusinessContextService *llm.BusinessContextService
}

func New(db *gorm.DB, logger *slog.Logger, businessContextService *llm.BusinessContextService) *Handlers {
	return &Handlers{
		DB:                     db,
		Logger:                 logger,
		BusinessContextService: businessContextService,
	}
}

// isInternalRequest checks if the request is coming from the server itself
func (h *Handlers) isInternalRequest(c *fiber.Ctx) bool {
	// Check if request has the internal header (set by HTMX)
	return c.Get("X-Internal-Request") == "web-client"
}

// needsOnboarding checks if an organization still needs to complete onboarding
func (h *Handlers) needsOnboarding(organizationID uuid.UUID) (bool, error) {
	var organization models.Organization
	err := h.DB.Where("id = ?", organizationID).First(&organization).Error

	if err != nil {
		return false, err
	}

	// Organization needs onboarding if app_status is 'setup'
	return organization.AppStatus == models.AppStatusSetup, nil
}

// hasInitialBusinessContext checks if organization has any business context or modules
func (h *Handlers) hasInitialBusinessContext(organizationID uuid.UUID) bool {
	// Check if business context exists
	var businessContext models.BusinessContext
	err := h.DB.Where("organization_id = ?", organizationID).First(&businessContext).Error
	if err == nil {
		return true
	}

	// Check if modules exist
	var moduleCount int64
	h.DB.Model(&models.RecommendedModule{}).Where("organization_id = ?", organizationID).Count(&moduleCount)
	return moduleCount > 0
}

// getDefaultOrganization gets the user's first organization (for single-org users)
func (h *Handlers) getDefaultOrganization(userID uuid.UUID) (*models.Organization, error) {
	var organization models.Organization
	err := h.DB.
		Where("id IN (SELECT organization_id FROM vertoie.organization_users WHERE user_id = ?)", userID).
		First(&organization).Error

	if err != nil {
		return nil, err
	}

	return &organization, nil
}

// ensureCurrentOrganization ensures there's a current organization in session, sets default if needed
func (h *Handlers) ensureCurrentOrganization(c *fiber.Ctx, userID uuid.UUID) (*models.Organization, error) {
	// Try to get current organization from session cookie
	authHandler := NewAuthHandler(h.DB, h.Logger, nil) // We don't need email service for this
	currentOrgID, err := authHandler.getCurrentOrganization(c)

	if err == nil {
		// Verify user has access to this organization
		var organization models.Organization
		err = h.DB.
			Joins("JOIN vertoie.organization_users ON vertoie.organization_users.organization_id = vertoie.organizations.id").
			Where("vertoie.organizations.id = ? AND vertoie.organization_users.user_id = ?", currentOrgID, userID).
			First(&organization).Error

		if err == nil {
			return &organization, nil
		}

		// If organization not found or no access, clear the session and fall through to default
		authHandler.clearCurrentOrganization(c)
	}

	// No valid organization in session, get user's default (first) organization
	defaultOrg, err := h.getDefaultOrganization(userID)
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("no organizations")
	}

	if err != nil {
		return nil, err
	}

	// Set this as current organization in session
	authHandler.setCurrentOrganization(c, defaultOrg.ID)

	return defaultOrg, nil
}

func (h *Handlers) Home(c *fiber.Ctx) error {
	// Get user from context if authenticated (set by OptionalAuth middleware)
	user, _ := c.Locals("user").(*models.User)

	return c.Render("index", fiber.Map{
		"Title": "Welcome",
		"User":  user, // Will be nil if not authenticated
	}, "layouts/base")
}

// LoginPage serves the login page
func (h *Handlers) LoginPage(c *fiber.Ctx) error {
	return c.Render("login", fiber.Map{
		"Title": "Sign In",
	}, "layouts/base")
}

// RegisterPage serves the registration page
func (h *Handlers) RegisterPage(c *fiber.Ctx) error {
	return c.Render("register", fiber.Map{
		"Title": "Sign Up",
	}, "layouts/base")
}

// DashboardPage serves the dashboard page (requires authentication)
func (h *Handlers) DashboardPage(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Get current organization from session or set default
	currentOrg, err := h.ensureCurrentOrganization(c, user.ID)
	if err != nil {
		if err.Error() == "no organizations" {
			return c.Redirect("/organizations", 302)
		}
		h.Logger.Error("failed to ensure current organization", "error", err, "user_id", user.ID)
		return c.Status(500).SendString("Failed to load organization context")
	}

	// Check if organization needs onboarding
	needsOnboarding, err := h.needsOnboarding(currentOrg.ID)
	if err != nil {
		h.Logger.Error("failed to check onboarding status", "error", err, "org_id", currentOrg.ID)
		// Don't fail the request, just log the error and continue
	}

	if needsOnboarding {
		// Redirect to onboarding flow
		return c.Redirect("/onboarding", 302)
	}

	return c.Render("dashboard", fiber.Map{
		"Title": "Dashboard",
		"User":  user,
	}, "layouts/dashboard")
}

// OrganizationsPage serves the organizations page (requires authentication)
func (h *Handlers) OrganizationsPage(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Get user's organizations to check their setup status
	var organizations []models.Organization
	err := h.DB.
		Where("id IN (SELECT organization_id FROM vertoie.organization_users WHERE user_id = ?)", user.ID).
		Find(&organizations).Error

	if err != nil {
		h.Logger.Error("failed to get user organizations", "error", err, "user_id", user.ID)
		// Don't fail the page load, just log the error
	}

	// Check if any organization needs onboarding and redirect if so
	for _, org := range organizations {
		needsOnboarding, err := h.needsOnboarding(org.ID)
		if err != nil {
			h.Logger.Error("failed to check onboarding status", "error", err, "org_id", org.ID)
			continue
		}

		if needsOnboarding {
			// Set this organization as current and redirect to onboarding
			authHandler := NewAuthHandler(h.DB, h.Logger, nil)
			authHandler.setCurrentOrganization(c, org.ID)
			return c.Redirect("/onboarding", 302)
		}
	}

	return c.Render("organizations", fiber.Map{
		"Title": "Organizations",
		"User":  user,
	}, "layouts/dashboard")
}

// ManageOrganizationPage serves the organization management page
func (h *Handlers) ManageOrganizationPage(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Get organization ID from URL parameter
	orgID := c.Params("id")
	if orgID == "" {
		return c.Status(400).SendString("Organization ID is required")
	}

	// Parse UUID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		return c.Status(400).SendString("Invalid organization ID")
	}

	// Fetch the organization and verify user has access
	var organization models.Organization
	err = h.DB.
		Joins("JOIN vertoie.organization_users ON vertoie.organization_users.organization_id = vertoie.organizations.id").
		Where("vertoie.organizations.id = ? AND vertoie.organization_users.user_id = ?", orgUUID, user.ID).
		Preload("Users").
		First(&organization).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(404).SendString("Organization not found or access denied")
		}
		h.Logger.Error("failed to fetch organization", "error", err)
		return c.Status(500).SendString("Failed to load organization")
	}

	// Fetch business context for this organization
	var businessContext models.BusinessContext
	err = h.DB.Where("organization_id = ?", orgUUID).First(&businessContext).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		h.Logger.Error("failed to fetch business context", "error", err)
		// Don't fail the page load for this error
	}

	// Only pass business context if it exists
	var contextData *models.BusinessContext
	businessContextComplete := false
	if err == nil {
		contextData = &businessContext
		businessContextComplete = businessContext.IsComplete()
	}

	return c.Render("manage-organization", fiber.Map{
		"Title":                   "Manage " + organization.Name,
		"User":                    user,
		"Organization":            organization,
		"BusinessContext":         contextData,
		"BusinessContextComplete": businessContextComplete,
	}, "layouts/dashboard")
}

// OnboardingPage serves the simplified single-page onboarding experience
func (h *Handlers) OnboardingPage(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Get current organization from session
	currentOrg, err := h.ensureCurrentOrganization(c, user.ID)
	if err != nil {
		if err.Error() == "no organizations" {
			return c.Redirect("/organizations", 302)
		}
		h.Logger.Error("failed to get current organization", "error", err, "user_id", user.ID)
		return c.Status(500).SendString("Failed to load organization context")
	}

	// Check if organization is already complete (not in setup mode)
	if currentOrg.AppStatus != models.AppStatusSetup {
		return c.Redirect("/dashboard", 302)
	}

	// Check if organization has initial business context for showing different sections
	hasContext := h.hasInitialBusinessContext(currentOrg.ID)

	return c.Render("onboarding", fiber.Map{
		"Title":        "Setup " + currentOrg.Name,
		"User":         user,
		"Organization": currentOrg,
		"HasContext":   hasContext,
	}, "layouts/onboarding")
}

// SwitchOrganization switches the current organization in session
func (h *Handlers) SwitchOrganization(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Get organization ID from URL parameter
	orgID := c.Params("id")
	if orgID == "" {
		return c.Status(400).SendString("Organization ID is required")
	}

	// Parse UUID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		return c.Status(400).SendString("Invalid organization ID")
	}

	// Verify user has access to this organization
	var organization models.Organization
	err = h.DB.
		Joins("JOIN vertoie.organization_users ON vertoie.organization_users.organization_id = vertoie.organizations.id").
		Where("vertoie.organizations.id = ? AND vertoie.organization_users.user_id = ?", orgUUID, user.ID).
		First(&organization).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(404).SendString("Organization not found or access denied")
		}
		h.Logger.Error("failed to fetch organization", "error", err)
		return c.Status(500).SendString("Failed to load organization")
	}

	// Set organization in session
	authHandler := NewAuthHandler(h.DB, h.Logger, nil)
	authHandler.setCurrentOrganization(c, organization.ID)

	// Redirect to dashboard (which will handle onboarding if needed)
	return c.Redirect("/dashboard", 302)
}

// CreateOrganization handles organization creation
func (h *Handlers) CreateOrganization(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Parse request body
	var input struct {
		Name        string `json:"name" form:"name"`
		Description string `json:"description" form:"description"`
	}

	if err := c.BodyParser(&input); err != nil {
		return c.Status(400).SendString("<p>Invalid request data</p>")
	}

	// Validate input
	if input.Name == "" {
		return c.Status(400).SendString("<p>Organization name is required</p>")
	}

	// Generate slug from name
	slug := h.generateSlug(input.Name)

	// Check if slug already exists
	var existingOrg models.Organization
	if err := h.DB.Where("slug = ?", slug).First(&existingOrg).Error; err == nil {
		return c.Status(400).SendString("<p>An organization with this name already exists</p>")
	}

	// Create organization
	organization := models.Organization{
		Name:        input.Name,
		Slug:        slug,
		Description: input.Description,
	}

	if err := h.DB.Create(&organization).Error; err != nil {
		h.Logger.Error("failed to create organization", "error", err)
		return c.Status(500).SendString("<p>Failed to create organization</p>")
	}

	// Add user as owner of the organization
	orgUser := models.OrganizationUser{
		UserID:         user.ID,
		OrganizationID: organization.ID,
		Role:           string(models.RoleOwner),
	}

	if err := h.DB.Create(&orgUser).Error; err != nil {
		h.Logger.Error("failed to create organization user relationship", "error", err)
		// Clean up the organization if we can't create the relationship
		h.DB.Delete(&organization)
		return c.Status(500).SendString("<p>Failed to create organization</p>")
	}

	// Set the new organization as current in session
	authHandler := NewAuthHandler(h.DB, h.Logger, nil)
	authHandler.setCurrentOrganization(c, organization.ID)

	// Return HTML response for HTMX - close modal and redirect to onboarding
	return c.SendString(`
		<script>
			// Close the modal
			const modal = document.getElementById('create-organization-modal');
			if (modal) {
				modal.remove();
			}
			// Redirect to onboarding for the new organization
			window.location.href = '/onboarding';
		</script>
	`)
}

// GetOrganizations returns a list of user's organizations
func (h *Handlers) GetOrganizations(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	var organizations []models.Organization
	err := h.DB.
		Where("id IN (SELECT organization_id FROM vertoie.organization_users WHERE user_id = ?)", user.ID).
		Preload("Users").
		Find(&organizations).Error

	if err != nil {
		h.Logger.Error("failed to get user organizations", "error", err)
		return c.Status(500).SendString("<p>Failed to load organizations</p>")
	}

	// Return HTML for HTMX
	return c.Render("partials/organizations-list", fiber.Map{
		"Organizations": organizations,
	})
}

// GetCreateOrganizationModal returns the create organization modal
func (h *Handlers) GetCreateOrganizationModal(c *fiber.Ctx) error {
	return c.Render("partials/create-organization-modal", fiber.Map{})
}

// generateSlug creates a URL-friendly slug from a name
func (h *Handlers) generateSlug(name string) string {
	// Simple slug generation - convert to lowercase, replace spaces with hyphens
	// In a real app, you'd want more sophisticated slug generation
	slug := strings.ToLower(name)
	slug = strings.ReplaceAll(slug, " ", "-")
	slug = strings.ReplaceAll(slug, "_", "-")
	// Remove special characters
	slug = regexp.MustCompile(`[^a-z0-9-]`).ReplaceAllString(slug, "")
	// Remove multiple consecutive hyphens
	slug = regexp.MustCompile(`-+`).ReplaceAllString(slug, "-")
	// Remove leading/trailing hyphens
	slug = strings.Trim(slug, "-")
	return slug
}

// GetPlans - API to get pricing plans
func (h *Handlers) GetPlans(c *fiber.Ctx) error {
	var plans []models.Plan
	// Read-only query with specific fields to minimize data exposure
	result := h.DB.Select("id, name, slug, description, price_cents, billing_cycle, features").
		Where("is_active = ? AND deleted_at IS NULL", true).
		Order("price_cents ASC").
		Find(&plans)

	if result.Error != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Service temporarily unavailable",
		})
	}

	return c.JSON(plans)
}

// GetPricing - Returns HTML pricing cards for HTMX (all plans except enterprise)
func (h *Handlers) GetPricing(c *fiber.Ctx) error {
	var plans []models.Plan

	result := h.DB.Where("is_active = ? AND deleted_at IS NULL AND slug != ?", true, "enterprise").
		Order("price_cents ASC").
		Find(&plans)

	if result.Error != nil {
		h.Logger.Error("failed to get pricing plans", "error", result.Error)
		return c.SendString("<p>Could not load pricing at this time.</p>")
	}

	return c.Render("partials/pricing-cards", fiber.Map{
		"Plans": plans,
	})
}

// GetMainPricing - Returns HTML pricing cards for home page (only main 3 plans)
func (h *Handlers) GetMainPricing(c *fiber.Ctx) error {
	var plans []models.Plan

	// Only show the main 3 plans: starter, professional, business
	result := h.DB.Where("is_active = ? AND deleted_at IS NULL AND slug IN ?", true, []string{"starter", "professional", "business"}).
		Order("price_cents ASC").
		Find(&plans)

	if result.Error != nil {
		h.Logger.Error("failed to get main pricing plans", "error", result.Error)
		return c.SendString("<p>Could not load pricing at this time.</p>")
	}

	return c.Render("partials/pricing-cards-main", fiber.Map{
		"Plans": plans,
	})
}

// BusinessContextChat handles the business context chat API
func (h *Handlers) BusinessContextChat(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Parse request body
	var input struct {
		OrganizationID string `json:"organization_id"`
		Message        string `json:"message"`
	}

	if err := c.BodyParser(&input); err != nil {
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"error":   "Invalid request data",
		})
	}

	// Validate input
	if input.OrganizationID == "" || input.Message == "" {
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"error":   "Organization ID and message are required",
		})
	}

	// Parse organization UUID
	orgUUID, err := uuid.Parse(input.OrganizationID)
	if err != nil {
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"error":   "Invalid organization ID",
		})
	}

	// Verify user has access to this organization
	var orgUser models.OrganizationUser
	err = h.DB.Where("organization_id = ? AND user_id = ?", orgUUID, user.ID).First(&orgUser).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(403).JSON(fiber.Map{
				"success": false,
				"error":   "Access denied",
			})
		}
		h.Logger.Error("failed to verify organization access", "error", err)
		return c.Status(500).JSON(fiber.Map{
			"success": false,
			"error":   "Failed to verify access",
		})
	}

	// Get or create business context
	var businessContext models.BusinessContext
	err = h.DB.Where("organization_id = ?", orgUUID).First(&businessContext).Error
	if err == gorm.ErrRecordNotFound {
		// Create new business context
		businessContext = models.BusinessContext{
			OrganizationID: orgUUID,
		}
		err = h.DB.Create(&businessContext).Error
		if err != nil {
			h.Logger.Error("failed to create business context", "error", err)
			return c.Status(500).JSON(fiber.Map{
				"success": false,
				"error":   "Failed to create business context",
			})
		}
	} else if err != nil {
		h.Logger.Error("failed to fetch business context", "error", err)
		return c.Status(500).JSON(fiber.Map{
			"success": false,
			"error":   "Failed to load business context",
		})
	}

	// Process message with business context service (thread-based)
	var businessContextData *models.BusinessContext
	if businessContext.ID != uuid.Nil {
		businessContextData = &businessContext
	}

	ctx := c.Context()
	response, thread, err := h.BusinessContextService.ProcessBusinessContextMessage(
		ctx,
		input.Message,
		businessContextData,
		orgUUID,
	)
	if err != nil {
		h.Logger.Error("failed to process business context message", "error", err)
		return c.Status(500).JSON(fiber.Map{
			"success": false,
			"error":   "Failed to process message",
		})
	}

	// Save the user message to the thread
	userConversation := models.BusinessContextConversation{
		OrganizationID:        orgUUID,
		UserID:                user.ID,
		ConversationSessionID: businessContext.ID,
		ThreadID:              &thread.ID,
		MessageRole:           "user",
		MessageContent:        input.Message,
		MessageTimestamp:      time.Now(),
		TokenCount:            models.EstimateTokens(input.Message),
	}
	err = h.DB.Create(&userConversation).Error
	if err != nil {
		h.Logger.Error("failed to save user message", "error", err)
		return c.Status(500).JSON(fiber.Map{
			"success": false,
			"error":   "Failed to save message",
		})
	}

	// Save AI response to the thread
	aiConversation := models.BusinessContextConversation{
		OrganizationID:        orgUUID,
		UserID:                user.ID,
		ConversationSessionID: businessContext.ID,
		ThreadID:              &thread.ID,
		MessageRole:           "assistant",
		MessageContent:        response.Message,
		MessageTimestamp:      time.Now(),
		TokenCount:            models.EstimateTokens(response.Message),
	}
	err = h.DB.Create(&aiConversation).Error
	if err != nil {
		h.Logger.Error("failed to save AI message", "error", err)
		// Don't fail the request for this error
	}

	return c.JSON(fiber.Map{
		"success":           true,
		"response":          response.Message,
		"complete":          response.IsComplete,
		"business_analysis": response.BusinessAnalysis,
		"thread": fiber.Map{
			"id":               thread.ID,
			"title":            thread.Title,
			"usage_percentage": thread.UsagePercentage,
			"status":           thread.Status,
		},
	})
}

// GetThreads returns the thread switcher component for an organization
func (h *Handlers) GetThreads(c *fiber.Ctx) error {
	user := c.Locals("user").(*models.User)
	orgID := c.Params("org_id")

	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		return c.Status(400).SendString("Invalid organization ID")
	}

	// Verify user has access to this organization
	var orgUser models.OrganizationUser
	err = h.DB.Where("organization_id = ? AND user_id = ?", orgUUID, user.ID).First(&orgUser).Error
	if err != nil {
		return c.Status(403).SendString("Access denied")
	}

	// Get thread service from business context service
	threadService := models.NewThreadService(h.DB)
	threads, err := threadService.GetThreadHistory(orgUUID)
	if err != nil {
		h.Logger.Error("failed to get thread history", "error", err)
		return c.Status(500).SendString("Failed to load threads")
	}

	return c.Render("partials/thread-switcher", fiber.Map{
		"Threads":        threads,
		"OrganizationID": orgUUID,
	})
}

// CreateNewThread creates a new conversation thread
func (h *Handlers) CreateNewThread(c *fiber.Ctx) error {
	user := c.Locals("user").(*models.User)

	var input struct {
		OrganizationID string `json:"organization_id"`
	}

	if err := c.BodyParser(&input); err != nil {
		return c.Status(400).SendString("Invalid request data")
	}

	orgUUID, err := uuid.Parse(input.OrganizationID)
	if err != nil {
		return c.Status(400).SendString("Invalid organization ID")
	}

	// Verify user has access to this organization
	var orgUser models.OrganizationUser
	err = h.DB.Where("organization_id = ? AND user_id = ?", orgUUID, user.ID).First(&orgUser).Error
	if err != nil {
		return c.Status(403).SendString("Access denied")
	}

	// Create new thread
	threadService := models.NewThreadService(h.DB)
	thread, err := threadService.CreateNewThread(orgUUID, "New Conversation", nil)
	if err != nil {
		h.Logger.Error("failed to create new thread", "error", err)
		return c.Status(500).SendString("Failed to create new thread")
	}

	// Return the progress component for the new thread
	return c.Render("partials/thread-progress", thread)
}

// SwitchThread switches to a different conversation thread
func (h *Handlers) SwitchThread(c *fiber.Ctx) error {
	user := c.Locals("user").(*models.User)
	threadID := c.Params("thread_id")

	threadUUID, err := uuid.Parse(threadID)
	if err != nil {
		return c.Status(400).SendString("Invalid thread ID")
	}

	// Get thread and verify access
	var thread models.ConversationThread
	err = h.DB.Where("id = ?", threadUUID).Preload("Organization").First(&thread).Error
	if err != nil {
		return c.Status(404).SendString("Thread not found")
	}

	// Verify user has access to this organization
	var orgUser models.OrganizationUser
	err = h.DB.Where("organization_id = ? AND user_id = ?", thread.OrganizationID, user.ID).First(&orgUser).Error
	if err != nil {
		return c.Status(403).SendString("Access denied")
	}

	// Switch to this thread
	threadService := models.NewThreadService(h.DB)
	_, err = threadService.SwitchToThread(thread.OrganizationID, threadUUID)
	if err != nil {
		h.Logger.Error("failed to switch thread", "error", err)
		return c.Status(500).SendString("Failed to switch thread")
	}

	// Get conversation history for this thread
	conversations, err := threadService.GetThreadConversations(threadUUID)
	if err != nil {
		h.Logger.Error("failed to get thread conversations", "error", err)
		return c.Status(500).SendString("Failed to load conversation history")
	}

	// Render chat messages for this thread
	return c.Render("partials/chat-messages", fiber.Map{
		"Conversations": conversations,
		"Thread":        thread,
	})
}

// GetThreadProgress returns the thread progress component
func (h *Handlers) GetThreadProgress(c *fiber.Ctx) error {
	user := c.Locals("user").(*models.User)
	threadID := c.Params("thread_id")

	threadUUID, err := uuid.Parse(threadID)
	if err != nil {
		return c.Status(400).SendString("Invalid thread ID")
	}

	// Get thread and verify access
	var thread models.ConversationThread
	err = h.DB.Where("id = ?", threadUUID).Preload("Organization").First(&thread).Error
	if err != nil {
		return c.Status(404).SendString("Thread not found")
	}

	// Verify user has access to this organization
	var orgUser models.OrganizationUser
	err = h.DB.Where("organization_id = ? AND user_id = ?", thread.OrganizationID, user.ID).First(&orgUser).Error
	if err != nil {
		return c.Status(403).SendString("Access denied")
	}

	thread.PrepareForJSON()
	return c.Render("partials/thread-progress", thread)
}

// BusinessContextChatHTMX handles the business context chat for HTMX requests
func (h *Handlers) BusinessContextChatHTMX(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Parse form data
	organizationID := c.FormValue("organization_id")
	message := c.FormValue("message")

	// Validate input
	if organizationID == "" || message == "" {
		return c.Status(400).SendString("Organization ID and message are required")
	}

	// Parse organization UUID
	orgUUID, err := uuid.Parse(organizationID)
	if err != nil {
		return c.Status(400).SendString("Invalid organization ID")
	}

	// Verify user has access to this organization
	var orgUser models.OrganizationUser
	err = h.DB.Where("organization_id = ? AND user_id = ?", orgUUID, user.ID).First(&orgUser).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(403).SendString("Access denied")
		}
		h.Logger.Error("failed to verify organization access", "error", err)
		return c.Status(500).SendString("Failed to verify access")
	}

	// Get or create business context
	var businessContext models.BusinessContext
	err = h.DB.Where("organization_id = ?", orgUUID).First(&businessContext).Error
	if err == gorm.ErrRecordNotFound {
		// Create new business context
		businessContext = models.BusinessContext{
			OrganizationID: orgUUID,
		}
		err = h.DB.Create(&businessContext).Error
		if err != nil {
			h.Logger.Error("failed to create business context", "error", err)
			return c.Status(500).SendString("Failed to create business context")
		}
	} else if err != nil {
		h.Logger.Error("failed to fetch business context", "error", err)
		return c.Status(500).SendString("Failed to load business context")
	}

	// Process message with business context service (thread-based)
	var businessContextData *models.BusinessContext
	if businessContext.ID != uuid.Nil {
		businessContextData = &businessContext
	}

	ctx := c.Context()
	response, thread, err := h.BusinessContextService.ProcessBusinessContextMessage(
		ctx,
		message,
		businessContextData,
		orgUUID,
	)
	if err != nil {
		h.Logger.Error("failed to process business context message", "error", err)
		return c.Status(500).SendString("Failed to process message")
	}

	// Save the user message to the thread
	userConversation := models.BusinessContextConversation{
		OrganizationID:        orgUUID,
		UserID:                user.ID,
		ConversationSessionID: businessContext.ID,
		ThreadID:              &thread.ID,
		MessageRole:           "user",
		MessageContent:        message,
		MessageTimestamp:      time.Now(),
		TokenCount:            models.EstimateTokens(message),
	}
	err = h.DB.Create(&userConversation).Error
	if err != nil {
		h.Logger.Error("failed to save user message", "error", err)
		return c.Status(500).SendString("Failed to save message")
	}

	// Save AI response to the thread
	aiConversation := models.BusinessContextConversation{
		OrganizationID:        orgUUID,
		UserID:                user.ID,
		ConversationSessionID: businessContext.ID,
		ThreadID:              &thread.ID,
		MessageRole:           "assistant",
		MessageContent:        response.Message,
		MessageTimestamp:      time.Now(),
		TokenCount:            models.EstimateTokens(response.Message),
	}
	err = h.DB.Create(&aiConversation).Error
	if err != nil {
		h.Logger.Error("failed to save AI message", "error", err)
		// Don't fail the request for this error
	}

	// Return chat messages HTML for HTMX
	return c.Render("partials/chat-messages", fiber.Map{
		"UserMessage": message,
		"AIMessage":   response.Message,
	})
}

// BusinessContextSingleSentence handles single-sentence business analysis for onboarding
func (h *Handlers) BusinessContextSingleSentence(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Parse form data
	organizationID := c.FormValue("organization_id")
	message := c.FormValue("message")

	// Validate input
	if organizationID == "" || message == "" {
		return c.Status(400).SendString("Organization ID and message are required")
	}

	// Parse organization UUID
	orgUUID, err := uuid.Parse(organizationID)
	if err != nil {
		return c.Status(400).SendString("Invalid organization ID")
	}

	// Verify user has access to this organization
	var orgUser models.OrganizationUser
	err = h.DB.Where("organization_id = ? AND user_id = ?", orgUUID, user.ID).First(&orgUser).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(403).SendString("Access denied")
		}
		h.Logger.Error("failed to verify organization access", "error", err)
		return c.Status(500).SendString("Failed to verify access")
	}

	// Get or create business context
	var businessContext models.BusinessContext
	err = h.DB.Where("organization_id = ?", orgUUID).First(&businessContext).Error
	if err == gorm.ErrRecordNotFound {
		// Create new business context
		businessContext = models.BusinessContext{
			OrganizationID: orgUUID,
		}
		err = h.DB.Create(&businessContext).Error
		if err != nil {
			h.Logger.Error("failed to create business context", "error", err)
			return c.Status(500).SendString("Failed to create business context")
		}
	} else if err != nil {
		h.Logger.Error("failed to fetch business context", "error", err)
		return c.Status(500).SendString("Failed to load business context")
	}

	// Process single-sentence business analysis
	ctx := c.Context()
	response, err := h.BusinessContextService.ProcessSingleSentenceAnalysis(
		ctx,
		message,
		orgUUID,
	)
	if err != nil {
		h.Logger.Error("failed to process single-sentence analysis", "error", err)
		return c.Status(500).SendString("Failed to analyze business")
	}

	// Save the user message
	userConversation := models.BusinessContextConversation{
		OrganizationID:        orgUUID,
		UserID:                user.ID,
		ConversationSessionID: businessContext.ID,
		MessageRole:           "user",
		MessageContent:        message,
		MessageTimestamp:      time.Now(),
		TokenCount:            models.EstimateTokens(message),
	}
	err = h.DB.Create(&userConversation).Error
	if err != nil {
		h.Logger.Error("failed to save user message", "error", err)
		// Don't fail the request for this error
	}

	// Save AI response
	aiConversation := models.BusinessContextConversation{
		OrganizationID:        orgUUID,
		UserID:                user.ID,
		ConversationSessionID: businessContext.ID,
		MessageRole:           "assistant",
		MessageContent:        response.Message,
		MessageTimestamp:      time.Now(),
		TokenCount:            models.EstimateTokens(response.Message),
	}
	err = h.DB.Create(&aiConversation).Error
	if err != nil {
		h.Logger.Error("failed to save AI message", "error", err)
		// Don't fail the request for this error
	}

	// Get organization details
	var organization models.Organization
	err = h.DB.Where("id = ?", orgUUID).First(&organization).Error
	if err != nil {
		h.Logger.Error("failed to get organization", "error", err)
		return c.Status(500).SendString("Failed to load organization")
	}

	// Return the split layout view
	return c.Render("partials/onboarding-split", fiber.Map{
		"Organization":     &organization,
		"BusinessAnalysis": response.BusinessAnalysis,
		"InitialMessage":   message,
	})
}

// BusinessContextPreview handles real-time module preview for typing
func (h *Handlers) BusinessContextPreview(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Parse JSON body
	var input struct {
		OrganizationID string `json:"organization_id"`
		Message        string `json:"message"`
	}

	if err := c.BodyParser(&input); err != nil {
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"error":   "Invalid request data",
		})
	}

	// Validate input
	if input.OrganizationID == "" || input.Message == "" {
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"error":   "Organization ID and message are required",
		})
	}

	// Parse organization UUID
	orgUUID, err := uuid.Parse(input.OrganizationID)
	if err != nil {
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"error":   "Invalid organization ID",
		})
	}

	// Verify user has access to this organization
	var orgUser models.OrganizationUser
	err = h.DB.Where("organization_id = ? AND user_id = ?", orgUUID, user.ID).First(&orgUser).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(403).JSON(fiber.Map{
				"success": false,
				"error":   "Access denied",
			})
		}
		h.Logger.Error("failed to verify organization access", "error", err)
		return c.Status(500).JSON(fiber.Map{
			"success": false,
			"error":   "Failed to verify access",
		})
	}

	// Generate preview modules using single-sentence analysis
	ctx := c.Context()
	response, err := h.BusinessContextService.GeneratePreview(
		ctx,
		input.Message,
		orgUUID,
	)
	if err != nil {
		h.Logger.Error("failed to generate preview", "error", err)
		return c.Status(500).JSON(fiber.Map{
			"success": false,
			"error":   "Failed to generate preview",
		})
	}

	// Return JSON response with modules
	return c.JSON(fiber.Map{
		"success":          true,
		"modules":          response.BusinessAnalysis.RecommendedModules,
		"confidence_level": response.BusinessAnalysis.BusinessAnalysisData["confidence_level"],
	})
}

// GetOnboardingModules - Internal API for onboarding module selection
func (h *Handlers) GetOnboardingModules(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Get organization ID from URL parameter
	orgID := c.Params("id")
	if orgID == "" {
		return c.Status(400).SendString("Organization ID is required")
	}

	// Parse UUID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		return c.Status(400).SendString("Invalid organization ID")
	}

	// Verify user has access to this organization
	var orgUser models.OrganizationUser
	err = h.DB.Where("organization_id = ? AND user_id = ?", orgUUID, user.ID).First(&orgUser).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(404).SendString("Organization not found or access denied")
		}
		h.Logger.Error("failed to verify organization access", "error", err)
		return c.Status(500).SendString("Failed to verify access")
	}

	// Clean up any duplicate modules first
	if err := CleanupDuplicateModules(h.DB, orgUUID); err != nil {
		h.Logger.Error("failed to cleanup duplicate modules", "error", err)
		// Don't fail the request for this
	}

	// Fetch modules for the organization
	var modules []models.RecommendedModule
	err = h.DB.
		Where("organization_id = ? AND deleted_at IS NULL", orgUUID).
		Order("recommendation_score DESC, created_at DESC").
		Find(&modules).Error

	if err != nil {
		h.Logger.Error("failed to fetch modules", "error", err, "org_id", orgUUID)
		return c.Status(500).SendString("Failed to load modules")
	}

	// Return HTML for HTMX using the onboarding modules partial
	return c.Render("partials/onboarding-modules", fiber.Map{
		"modules":        modules,
		"OrganizationID": orgUUID.String(),
	})
}

// GetModules retrieves recommended modules for an organization
func (h *Handlers) GetModules(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Get organization ID from URL parameter
	orgID := c.Params("org_id")
	if orgID == "" {
		return c.Status(400).JSON(fiber.Map{"error": "Organization ID is required"})
	}

	// Parse UUID
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid organization ID"})
	}

	// Verify user has access to this organization
	var orgUser models.OrganizationUser
	err = h.DB.Where("organization_id = ? AND user_id = ?", orgUUID, user.ID).First(&orgUser).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(404).JSON(fiber.Map{"error": "Organization not found or access denied"})
		}
		h.Logger.Error("failed to verify organization access", "error", err)
		return c.Status(500).JSON(fiber.Map{"error": "Failed to verify access"})
	}

	// Get modules from database
	moduleService := models.NewRecommendedModuleService(h.DB)
	modules, err := moduleService.GetModulesForOrganization(orgUUID)
	if err != nil {
		h.Logger.Error("failed to get modules", "error", err, "organization_id", orgUUID)
		return c.Status(500).JSON(fiber.Map{"error": "Failed to load modules"})
	}

	return c.Render("partials/modules-list", fiber.Map{
		"modules":        modules,
		"OrganizationID": orgUUID,
	})
}

// UpdateModuleStatus updates the status of a recommended module
func (h *Handlers) UpdateModuleStatus(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Get module ID from URL parameter
	moduleID := c.Params("module_id")
	if moduleID == "" {
		return c.Status(400).JSON(fiber.Map{"error": "Module ID is required"})
	}

	// Parse UUID
	moduleUUID, err := uuid.Parse(moduleID)
	if err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid module ID"})
	}

	// Parse request body
	var input struct {
		Status   string `json:"status" form:"status"`
		Feedback string `json:"feedback" form:"feedback"`
	}

	if err := c.BodyParser(&input); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request data"})
	}

	// Validate status
	validStatuses := []string{
		models.ModuleStatusAccepted,
		models.ModuleStatusRejected,
		models.ModuleStatusModified,
	}
	isValidStatus := false
	for _, status := range validStatuses {
		if input.Status == status {
			isValidStatus = true
			break
		}
	}
	if !isValidStatus {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid status"})
	}

	// Verify user has access to this module's organization
	var module models.RecommendedModule
	err = h.DB.
		Joins("JOIN vertoie.organization_users ON vertoie.organization_users.organization_id = vertoie.organization_recommended_modules.organization_id").
		Where("vertoie.organization_recommended_modules.id = ? AND vertoie.organization_users.user_id = ?", moduleUUID, user.ID).
		First(&module).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(404).JSON(fiber.Map{"error": "Module not found or access denied"})
		}
		h.Logger.Error("failed to verify module access", "error", err)
		return c.Status(500).JSON(fiber.Map{"error": "Failed to verify access"})
	}

	// Update module status
	moduleService := models.NewRecommendedModuleService(h.DB)
	err = moduleService.UpdateModuleStatus(moduleUUID, input.Status, input.Feedback)
	if err != nil {
		h.Logger.Error("failed to update module status", "error", err, "module_id", moduleUUID)
		return c.Status(500).JSON(fiber.Map{"error": "Failed to update module"})
	}

	h.Logger.Info("module status updated", "module_id", moduleUUID, "status", input.Status, "user_id", user.ID)

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Module status updated successfully",
	})
}

// CreateCustomModule creates a custom module requested by the user
func (h *Handlers) CreateCustomModule(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Parse request body
	var input struct {
		OrganizationID string `json:"organization_id" form:"organization_id"`
		Name           string `json:"name" form:"name"`
		Description    string `json:"description" form:"description"`
		Category       string `json:"category" form:"category"`
	}

	if err := c.BodyParser(&input); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request data"})
	}

	// Validate input
	if input.OrganizationID == "" || input.Name == "" || input.Description == "" {
		return c.Status(400).JSON(fiber.Map{"error": "Organization ID, name, and description are required"})
	}

	// Parse organization UUID
	orgUUID, err := uuid.Parse(input.OrganizationID)
	if err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid organization ID"})
	}

	// Verify user has access to this organization
	var orgUser models.OrganizationUser
	err = h.DB.Where("organization_id = ? AND user_id = ?", orgUUID, user.ID).First(&orgUser).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(404).JSON(fiber.Map{"error": "Organization not found or access denied"})
		}
		h.Logger.Error("failed to verify organization access", "error", err)
		return c.Status(500).JSON(fiber.Map{"error": "Failed to verify access"})
	}

	// Set default category if not provided
	if input.Category == "" {
		input.Category = models.ModuleCategoryCustom
	}

	// Create custom module
	moduleService := models.NewRecommendedModuleService(h.DB)
	module, err := moduleService.CreateCustomModule(orgUUID, input.Name, input.Description, input.Category)
	if err != nil {
		h.Logger.Error("failed to create custom module", "error", err, "organization_id", orgUUID)
		return c.Status(500).JSON(fiber.Map{"error": "Failed to create custom module"})
	}

	h.Logger.Info("custom module created", "module_id", module.ID, "name", input.Name, "user_id", user.ID)

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Custom module created successfully",
		"module":  module,
	})
}

// BusinessContextAnalyze handles the initial business analysis and returns the full module selection UI
func (h *Handlers) BusinessContextAnalyze(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Parse form data
	organizationID := c.FormValue("organization_id")
	message := c.FormValue("message")

	// Validate input
	if organizationID == "" || message == "" {
		return c.Status(400).SendString(`<div class="alert alert-error">Organization ID and message are required</div>`)
	}

	// Parse organization UUID
	orgUUID, err := uuid.Parse(organizationID)
	if err != nil {
		return c.Status(400).SendString(`<div class="alert alert-error">Invalid organization ID</div>`)
	}

	// Verify user has access to this organization
	var orgUser models.OrganizationUser
	err = h.DB.Where("organization_id = ? AND user_id = ?", orgUUID, user.ID).First(&orgUser).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(403).SendString(`<div class="alert alert-error">Access denied</div>`)
		}
		h.Logger.Error("failed to verify organization access", "error", err)
		return c.Status(500).SendString(`<div class="alert alert-error">Failed to verify access</div>`)
	}

	// Get or create business context
	var businessContext models.BusinessContext
	err = h.DB.Where("organization_id = ?", orgUUID).First(&businessContext).Error
	if err == gorm.ErrRecordNotFound {
		// Create new business context
		businessContext = models.BusinessContext{
			OrganizationID: orgUUID,
		}
		err = h.DB.Create(&businessContext).Error
		if err != nil {
			h.Logger.Error("failed to create business context", "error", err)
			return c.Status(500).SendString(`<div class="alert alert-error">Failed to create business context</div>`)
		}
	} else if err != nil {
		h.Logger.Error("failed to fetch business context", "error", err)
		return c.Status(500).SendString(`<div class="alert alert-error">Failed to load business context</div>`)
	}

	// Process single-sentence business analysis
	ctx := c.Context()
	response, err := h.BusinessContextService.ProcessSingleSentenceAnalysis(
		ctx,
		message,
		orgUUID,
	)
	if err != nil {
		h.Logger.Error("failed to process single-sentence analysis", "error", err)
		return c.Status(500).SendString(`<div class="alert alert-error">Failed to analyze business</div>`)
	}

	// Save the user message
	userConversation := models.BusinessContextConversation{
		OrganizationID:        orgUUID,
		UserID:                user.ID,
		ConversationSessionID: businessContext.ID,
		MessageRole:           "user",
		MessageContent:        message,
		MessageTimestamp:      time.Now(),
		TokenCount:            models.EstimateTokens(message),
	}
	err = h.DB.Create(&userConversation).Error
	if err != nil {
		h.Logger.Error("failed to save user message", "error", err)
		// Don't fail the request for this error
	}

	// Save AI response
	aiConversation := models.BusinessContextConversation{
		OrganizationID:        orgUUID,
		UserID:                user.ID,
		ConversationSessionID: businessContext.ID,
		MessageRole:           "assistant",
		MessageContent:        response.Message,
		MessageTimestamp:      time.Now(),
		TokenCount:            models.EstimateTokens(response.Message),
	}
	err = h.DB.Create(&aiConversation).Error
	if err != nil {
		h.Logger.Error("failed to save AI message", "error", err)
		// Don't fail the request for this error
	}

	// Get the modules for the organization
	var modules []models.RecommendedModule
	err = h.DB.
		Where("organization_id = ? AND deleted_at IS NULL", orgUUID).
		Order("recommendation_score DESC, created_at DESC").
		Find(&modules).Error

	if err != nil {
		h.Logger.Error("failed to fetch modules", "error", err, "org_id", orgUUID)
		modules = []models.RecommendedModule{} // Use empty slice instead of failing
	}

	// Calculate initial pricing based on all selected modules
	selectedCount := 0
	for _, module := range modules {
		if module.Status == models.ModuleStatusAccepted || module.Status == models.ModuleStatusRecommended {
			selectedCount++
		}
	}

	// Get pricing plan
	plan, err := h.calculatePricingPlan(selectedCount)
	if err != nil {
		h.Logger.Error("failed to calculate pricing", "error", err)
		// Don't fail, just don't show pricing
		plan = nil
	}

	// Get the organization details
	var organization models.Organization
	if err := h.DB.Where("id = ?", orgUUID).First(&organization).Error; err != nil {
		h.Logger.Error("failed to get organization", "error", err)
		return c.Status(500).SendString(`<div class="alert alert-error">Failed to load organization</div>`)
	}

	// Render the module selection UI
	return c.Render("partials/onboarding-step2", fiber.Map{
		"Organization":     organization,
		"BusinessAnalysis": response.BusinessAnalysis,
		"Modules":          modules,
		"Plan":             plan,
		"ModuleCount":      selectedCount,
	})
}

// calculatePricingPlan determines which plan based on module count
func (h *Handlers) calculatePricingPlan(moduleCount int) (*models.Plan, error) {
	// If no modules selected, return nil to show $0
	if moduleCount == 0 {
		return nil, nil
	}

	var plans []models.Plan
	// Exclude enterprise plan from regular pricing calculations
	err := h.DB.Where("is_active = ? AND deleted_at IS NULL AND slug != ?", true, "enterprise").
		Order("price_cents ASC").
		Find(&plans).Error

	if err != nil {
		return nil, err
	}

	// Find the appropriate plan based on module count
	for _, plan := range plans {
		maxModules := plan.GetMaxModules()
		if maxModules == -1 || moduleCount <= maxModules {
			return &plan, nil
		}
	}

	// If no plan found, return the highest non-enterprise plan
	if len(plans) > 0 {
		return &plans[len(plans)-1], nil
	}

	return nil, fmt.Errorf("no plans available")
}

// ToggleModule handles module checkbox toggles - simplified version
func (h *Handlers) ToggleModule(c *fiber.Ctx) error {
	// Get user from context
	user := c.Locals("user").(*models.User)

	// Parse form data
	moduleID := c.FormValue("module_id")
	organizationID := c.FormValue("organization_id")
	// Get the checkbox state directly from the form
	isChecked := c.FormValue("checked") == "true"

	// Parse UUIDs
	modUUID, err := uuid.Parse(moduleID)
	if err != nil {
		return c.Status(400).SendString("Invalid module ID")
	}

	orgUUID, err := uuid.Parse(organizationID)
	if err != nil {
		return c.Status(400).SendString("Invalid organization ID")
	}

	// Verify user has access
	var orgUser models.OrganizationUser
	err = h.DB.Where("organization_id = ? AND user_id = ?", orgUUID, user.ID).First(&orgUser).Error
	if err != nil {
		return c.Status(403).SendString("Access denied")
	}

	// Simple logic: checked = accepted, unchecked = rejected
	newStatus := models.ModuleStatusRejected
	if isChecked {
		newStatus = models.ModuleStatusAccepted
	}

	// Update module status
	err = h.DB.Model(&models.RecommendedModule{}).
		Where("id = ? AND organization_id = ?", modUUID, orgUUID).
		Update("status", newStatus).Error

	if err != nil {
		h.Logger.Error("failed to update module status", "error", err)
		return c.Status(500).SendString("Failed to update module")
	}

	// Return updated pricing
	return h.renderPricingSummary(c, orgUUID)
}

// CalculatePricing returns the pricing summary for an organization
func (h *Handlers) CalculatePricing(c *fiber.Ctx) error {
	// Get user from context
	user := c.Locals("user").(*models.User)

	// Get organization ID from URL
	orgID := c.Params("org_id")
	orgUUID, err := uuid.Parse(orgID)
	if err != nil {
		return c.Status(400).SendString("Invalid organization ID")
	}

	// Verify user has access
	var orgUser models.OrganizationUser
	err = h.DB.Where("organization_id = ? AND user_id = ?", orgUUID, user.ID).First(&orgUser).Error
	if err != nil {
		return c.Status(403).SendString("Access denied")
	}

	return h.renderPricingSummary(c, orgUUID)
}

// renderPricingSummary is a helper to render pricing
func (h *Handlers) renderPricingSummary(c *fiber.Ctx, orgUUID uuid.UUID) error {
	// Get selected module count
	var selectedCount int64
	h.DB.Model(&models.RecommendedModule{}).
		Where("organization_id = ? AND (status = ? OR status = ?) AND deleted_at IS NULL",
			orgUUID, models.ModuleStatusAccepted, models.ModuleStatusRecommended).
		Count(&selectedCount)

	// Calculate pricing
	plan, err := h.calculatePricingPlan(int(selectedCount))
	if err != nil {
		h.Logger.Error("failed to calculate pricing", "error", err)
		return c.SendString(`<div class="pricing-summary"></div>`) // Return empty on error
	}

	// Return updated pricing display using template
	return c.Render("partials/pricing-summary", fiber.Map{
		"Plan":        plan, // Will be nil for 0 modules
		"ModuleCount": int(selectedCount),
	})
}

// OnboardingAnalyze handles the business description analysis and returns the module selection view
func (h *Handlers) OnboardingAnalyze(c *fiber.Ctx) error {
	user := c.Locals("user").(*models.User)

	// Get current organization
	currentOrg, err := h.ensureCurrentOrganization(c, user.ID)
	if err != nil {
		return c.Status(500).SendString(`<div class="alert alert-error">Failed to load organization</div>`)
	}

	// Get the business description
	description := c.FormValue("description")
	if description == "" {
		return c.Status(400).SendString(`<div class="alert alert-error">Please describe your business</div>`)
	}

	// Process the business analysis
	ctx := c.Context()
	response, err := h.BusinessContextService.ProcessSingleSentenceAnalysis(ctx, description, currentOrg.ID)
	if err != nil {
		h.Logger.Error("failed to analyze business", "error", err)
		return c.Status(500).SendString(`<div class="alert alert-error">Failed to analyze your business. Please try again.</div>`)
	}

	// Save the conversation
	var businessContext models.BusinessContext
	h.DB.Where("organization_id = ?", currentOrg.ID).FirstOrCreate(&businessContext, models.BusinessContext{
		OrganizationID: currentOrg.ID,
	})

	// Save user message
	h.DB.Create(&models.BusinessContextConversation{
		OrganizationID:        currentOrg.ID,
		UserID:                user.ID,
		ConversationSessionID: businessContext.ID,
		MessageRole:           "user",
		MessageContent:        description,
		MessageTimestamp:      time.Now(),
		TokenCount:            models.EstimateTokens(description),
	})

	// Load the modules for this organization
	var modules []models.RecommendedModule
	h.DB.Where("organization_id = ? AND deleted_at IS NULL", currentOrg.ID).
		Order("recommendation_score DESC").
		Find(&modules)

	// Calculate pricing
	selectedCount := 0
	for _, module := range modules {
		if module.Status == models.ModuleStatusAccepted || module.Status == models.ModuleStatusRecommended {
			selectedCount++
		}
	}

	// Extract business type from analysis
	businessType := "your business"
	if response.BusinessAnalysis != nil && response.BusinessAnalysis.BusinessAnalysisData != nil {
		if typeVal, ok := response.BusinessAnalysis.BusinessAnalysisData["type"].(string); ok {
			businessType = typeVal
		}
	}

	// Render the module selection view
	return c.Render("partials/onboarding-modules-view", fiber.Map{
		"Organization": currentOrg,
		"Modules":      modules,
		"ModuleCount":  selectedCount,
		"BusinessType": businessType,
		"UserMessage":  description,
	})
}

// CompleteOnboarding marks the organization as active and completes onboarding
func (h *Handlers) CompleteOnboarding(c *fiber.Ctx) error {
	// Get user from context (set by RequireAuth middleware)
	user := c.Locals("user").(*models.User)

	// Get current organization from session
	currentOrg, err := h.ensureCurrentOrganization(c, user.ID)
	if err != nil {
		h.Logger.Error("failed to get current organization", "error", err, "user_id", user.ID)
		return c.Status(500).JSON(fiber.Map{"error": "Failed to load organization context"})
	}

	// Update organization status to active
	err = h.DB.Model(&currentOrg).Update("app_status", models.AppStatusActive).Error
	if err != nil {
		h.Logger.Error("failed to complete onboarding", "error", err, "org_id", currentOrg.ID)
		return c.Status(500).JSON(fiber.Map{"error": "Failed to complete onboarding"})
	}

	h.Logger.Info("onboarding completed", "org_id", currentOrg.ID, "user_id", user.ID)

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Onboarding completed successfully",
	})
}

// PricingPage - Dedicated pricing page with all plans
func (h *Handlers) PricingPage(c *fiber.Ctx) error {
	// Check if user is authenticated for optional auth context
	user, _ := c.Locals("user").(*models.User)

	return c.Render("pricing", fiber.Map{
		"Title": "Pricing",
		"User":  user,
	}, "layouts/base")
}
