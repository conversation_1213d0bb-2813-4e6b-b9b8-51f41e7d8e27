package handlers

import (
	"strings"

	"github.com/google/uuid"
	"github.com/vertoie/models"
	"gorm.io/gorm"
)

// CleanupDuplicateModules removes duplicate modules for an organization
func CleanupDuplicateModules(db *gorm.DB, organizationID uuid.UUID) error {
	// Use a transaction to ensure consistency
	return db.Transaction(func(tx *gorm.DB) error {
		// Define module groups where only one should exist
		moduleGroups := map[string][]string{
			"Project Management": {
				"Project Management",
				"Project Management Suite",
				"Project Management Dashboard",
				"Project Tracking",
				"Project Manager",
			},
			"Time Tracking & Invoicing": {
				"Time Tracking",
				"Time & Resource Tracking",
				"Time & Billing",
				"Time Tracking & Invoicing",
				"Time Management",
				"Billing System",
				"Invoicing System",
				"Invoice Management",
			},
			"Knowledge Base": {
				"Knowledge Base",
				"Knowledge Repository",
				"Documentation System",
				"Knowledge Management",
				"Document Repository",
			},
			"Client Portal": {
				"Client Portal",
				"Customer Portal",
				"Client Portal Access",
				"Customer Access Portal",
				"Client Dashboard",
			},
			"Analytics Dashboard": {
				"Analytics",
				"Analytics Dashboard",
				"Performance Analytics",
				"Business Analytics",
				"Reporting Dashboard",
				"Analytics & Reporting",
			},
			"Resource Management": {
				"Resource Allocation",
				"Resource Allocation Engine",
				"Resource Management",
				"Resource Planning",
				"Resource Tracker",
			},
		}

		// For each group, keep the highest scoring module and delete the rest
		for preferredName, variations := range moduleGroups {
			var modules []models.RecommendedModule
			
			// Build query to find all variations
			conditions := []string{}
			args := []interface{}{organizationID}
			
			for _, variation := range variations {
				conditions = append(conditions, "LOWER(name) LIKE ?")
				args = append(args, "%"+strings.ToLower(variation)+"%")
			}
			
			whereClause := "organization_id = ? AND deleted_at IS NULL AND (" + strings.Join(conditions, " OR ") + ")"
			query := tx.Where(whereClause, args...)
			
			// Get all matching modules
			err := query.Order("recommendation_score DESC, created_at ASC").Find(&modules).Error
			if err != nil {
				return err
			}
			
			// If we have duplicates, keep the first (highest score, oldest) and update its name
			if len(modules) > 1 {
				// Update the keeper module with the preferred name
				keeper := modules[0]
				updates := map[string]interface{}{
					"name": preferredName,
				}
				
				// If the keeper has a lower score than others, take the highest score
				for _, m := range modules[1:] {
					if m.RecommendationScore != nil && keeper.RecommendationScore != nil {
						if *m.RecommendationScore > *keeper.RecommendationScore {
							updates["recommendation_score"] = m.RecommendationScore
							updates["description"] = m.Description
							updates["key_benefit"] = m.KeyBenefit
						}
					}
				}
				
				// Update the keeper
				if err := tx.Model(&keeper).Updates(updates).Error; err != nil {
					return err
				}
				
				// Soft delete the duplicates
				for _, duplicate := range modules[1:] {
					if err := tx.Delete(&duplicate).Error; err != nil {
						return err
					}
				}
			}
		}
		
		return nil
	})
}