package llm

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"log/slog"
)

func TestNewBusinessGroqClient(t *testing.T) {
	tests := []struct {
		name     string
		apiKey   string
		expected *BusinessGroqClient
	}{
		{
			name:   "with API key",
			api<PERSON><PERSON>: "test-key",
			expected: &BusinessGroqClient{
				client: nil, // Will be set by NewGroqClient
				logger: slog.Default(),
			},
		},
		{
			name:   "without API key",
			apiKey: "",
			expected: &BusinessGroqClient{
				client: nil,
				logger: slog.Default(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := NewBusinessGroqClient(tt.apiKey, slog.Default())

			if client == nil {
				t.Error("expected client to be created, got nil")
			}

			if tt.apiKey == "" {
				if client.client != nil {
					t.Error("expected client to be nil when no API key provided")
				}
			} else {
				if client.client == nil {
					t.Error("expected client to be initialized when API key provided")
				}
			}
		})
	}
}

func TestBusinessGroqClient_AnalyzeBusiness(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{
			"id": "test-id",
			"choices": [{
				"index": 0,
				"message": {
					"role": "assistant",
					"content": "{\"business_analysis\":{\"type\":\"test business\"},\"recommended_modules\":[{\"name\":\"Test Module\",\"category\":\"core\"}]}"
				},
				"finish_reason": "stop"
			}],
			"usage": {
				"prompt_tokens": 100,
				"completion_tokens": 50,
				"total_tokens": 150
			}
		}`))
	}))
	defer server.Close()

	config := &ClientConfig{
		APIKey:  "test-key",
		BaseURL: server.URL,
		Timeout: 5 * time.Second,
		Logger:  slog.Default(),
	}
	underlyingClient := NewGroqClient(config)

	client := &BusinessGroqClient{
		client: underlyingClient,
		logger: slog.Default(),
	}

	ctx := context.Background()
	messages := []GroqMessage{
		{Role: "user", Content: "I run a pool cleaning business"},
	}
	result, err := client.AnalyzeBusinessWithMessages(ctx, messages)

	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}

	if result == nil {
		t.Error("expected result, got nil")
	}

	// Check that we got a valid response with business analysis data
	if len(result.BusinessAnalysisData) == 0 {
		t.Error("expected business analysis data, got empty")
	}
}

func TestBusinessGroqClient_AnalyzeBusiness_NoAPIKey(t *testing.T) {
	client := &BusinessGroqClient{
		client: nil,
		logger: slog.Default(),
	}

	ctx := context.Background()
	messages := []GroqMessage{
		{Role: "user", Content: "test"},
	}
	result, err := client.AnalyzeBusinessWithMessages(ctx, messages)

	if err == nil {
		t.Error("expected error when no client available, got nil")
	}

	if result != nil {
		t.Error("expected nil result when error occurs")
	}
}

func TestBusinessGroqClient_HelloWorld(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{
			"id": "test-id",
			"choices": [{
				"index": 0,
				"message": {
					"role": "assistant",
					"content": "{\"message\": \"Hello world!\"}"
				},
				"finish_reason": "stop"
			}],
			"usage": {
				"prompt_tokens": 10,
				"completion_tokens": 5,
				"total_tokens": 15
			}
		}`))
	}))
	defer server.Close()

	config := &ClientConfig{
		APIKey:  "test-key",
		BaseURL: server.URL,
		Timeout: 5 * time.Second,
		Logger:  slog.Default(),
	}
	underlyingClient := NewGroqClient(config)

	client := &BusinessGroqClient{
		client: underlyingClient,
		logger: slog.Default(),
	}

	ctx := context.Background()
	result, err := client.HelloWorld(ctx)

	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}

	if result == "" {
		t.Error("expected non-empty result")
	}

	if result != `{"message": "Hello world!"}` {
		t.Errorf("expected specific result, got %s", result)
	}
}

func TestBusinessGroqClient_StreamBusinessAnalysis(t *testing.T) {
	client := &BusinessGroqClient{
		client: nil,
		logger: slog.Default(),
	}

	ctx := context.Background()
	_, err := client.StreamBusinessAnalysis(ctx, "test")

	if err == nil {
		t.Error("expected error for unimplemented function")
	}

	if err.Error() != "streaming not implemented for Groq API yet" {
		t.Errorf("expected specific error message, got %s", err.Error())
	}
}

func TestBusinessGroqClient_parseBusinessAnalysis(t *testing.T) {
	tests := []struct {
		name        string
		content     string
		expectError bool
		expected    *BusinessAnalysis
	}{
		{
			name: "valid JSON response",
			content: `{
				"business_analysis": {
					"type": "pool cleaning service",
					"core_value": "clean and maintain swimming pools",
					"operational_model": "service-based with scheduling",
					"key_challenges": ["scheduling", "weather dependency"],
					"new_insights": ["residential focus"]
				},
				"recommended_modules": [
					{
						"name": "Scheduling System",
						"category": "core",
						"description": "Manage pool cleaning appointments",
						"key_benefit": "Efficient route planning",
						"effort_to_value": "high",
						"recommendation_score": 10,
						"score_reason": "Essential for service business"
					}
				],
				"discovery_questions": [
					{
						"question": "How many pools do you service weekly?",
						"why_asking": "Determine scheduling complexity",
						"potential_modules": ["route optimization", "inventory management"]
					}
				],
				"quick_start_package": {
					"essential_modules": ["Scheduling System", "Customer Management"],
					"reason": "Core operations for service business"
				},
				"advanced_possibilities": [
					"Automated weather-based rescheduling",
					"Customer portal for service history"
				]
			}`,
			expectError: false,
			expected: &BusinessAnalysis{
				BusinessAnalysisData: map[string]interface{}{
					"type":              "pool cleaning service",
					"core_value":        "clean and maintain swimming pools",
					"operational_model": "service-based with scheduling",
					"key_challenges":    []interface{}{"scheduling", "weather dependency"},
					"new_insights":      []interface{}{"residential focus"},
				},
				RecommendedModules: []map[string]interface{}{
					{
						"name":                 "Scheduling System",
						"category":             "core",
						"description":          "Manage pool cleaning appointments",
						"key_benefit":          "Efficient route planning",
						"effort_to_value":      "high",
						"recommendation_score": float64(10),
						"score_reason":         "Essential for service business",
					},
				},
				DiscoveryQuestions: []map[string]interface{}{
					{
						"question":          "How many pools do you service weekly?",
						"why_asking":        "Determine scheduling complexity",
						"potential_modules": []interface{}{"route optimization", "inventory management"},
					},
				},
				QuickStartPackage: map[string]interface{}{
					"essential_modules": []interface{}{"Scheduling System", "Customer Management"},
					"reason":            "Core operations for service business",
				},
				AdvancedPossibilities: []string{
					"Automated weather-based rescheduling",
					"Customer portal for service history",
				},
			},
		},
		{
			name:        "JSON with markdown code blocks",
			content:     "```json\n{\"business_analysis\":{\"type\":\"test\"}}\n```",
			expectError: false,
			expected: &BusinessAnalysis{
				BusinessAnalysisData: map[string]interface{}{
					"type": "test",
				},
			},
		},
		{
			name:        "no JSON object",
			content:     "This is not JSON",
			expectError: true,
		},
		{
			name:        "incomplete JSON",
			content:     `{"business_analysis": {"type": "test"`,
			expectError: true,
		},
	}

	client := &BusinessGroqClient{
		client: nil,
		logger: slog.Default(),
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := client.parseBusinessAnalysis(tt.content)

			if tt.expectError {
				if err == nil {
					t.Error("expected error, got nil")
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if result == nil {
					t.Error("expected result, got nil")
				}
				if tt.expected != nil {
					if len(result.RecommendedModules) != len(tt.expected.RecommendedModules) {
						t.Errorf("expected %d recommended modules, got %d", len(tt.expected.RecommendedModules), len(result.RecommendedModules))
					}
					if len(result.BusinessAnalysisData) != len(tt.expected.BusinessAnalysisData) {
						t.Errorf("expected %d business analysis fields, got %d", len(tt.expected.BusinessAnalysisData), len(result.BusinessAnalysisData))
					}
				}
			}
		})
	}
}

func TestBusinessGroqClient_AnalyzeBusinessWithMessages(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{
			"id": "test-id",
			"choices": [{
				"index": 0,
				"message": {
					"role": "assistant",
					"content": "{\"business_analysis\":{\"type\":\"test business\"},\"recommended_modules\":[{\"name\":\"Test Module\",\"category\":\"core\"}]}"
				},
				"finish_reason": "stop"
			}],
			"usage": {
				"prompt_tokens": 50,
				"completion_tokens": 25,
				"total_tokens": 75
			}
		}`))
	}))
	defer server.Close()

	config := &ClientConfig{
		APIKey:  "test-key",
		BaseURL: server.URL,
		Timeout: 5 * time.Second,
		Logger:  slog.Default(),
	}
	underlyingClient := NewGroqClient(config)

	client := &BusinessGroqClient{
		client: underlyingClient,
		logger: slog.Default(),
	}

	messages := []GroqMessage{
		{Role: "system", Content: "You are a business analyst"},
		{Role: "user", Content: "I run a pool cleaning business"},
	}

	ctx := context.Background()
	result, err := client.AnalyzeBusinessWithMessages(ctx, messages)

	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}

	if result == nil {
		t.Error("expected result, got nil")
	}

	// Check that we got a valid response with business analysis data
	if len(result.BusinessAnalysisData) == 0 {
		t.Error("expected business analysis data, got empty")
	}
}

func TestBusinessGroqClient_GetSystemPrompt(t *testing.T) {
	client := &BusinessGroqClient{
		client: nil,
		logger: slog.Default(),
	}

	prompt := client.GetSystemPrompt()

	if prompt == "" {
		t.Error("expected non-empty system prompt")
	}

	if !strings.Contains(prompt, "Vertoie's AI") {
		t.Error("expected prompt to contain Vertoie's AI")
	}

	if !strings.Contains(prompt, "business management software") {
		t.Error("expected prompt to contain business management software")
	}
}

// Benchmark tests
func BenchmarkBusinessGroqClient_AnalyzeBusiness(b *testing.B) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{
			"id": "test-id",
			"choices": [{
				"index": 0,
				"message": {
					"role": "assistant",
					"content": "{\"business_analysis\":{\"type\":\"test business\"},\"recommended_modules\":[{\"name\":\"Test Module\",\"category\":\"core\"}]}"
				},
				"finish_reason": "stop"
			}],
			"usage": {
				"prompt_tokens": 100,
				"completion_tokens": 50,
				"total_tokens": 150
			}
		}`))
	}))
	defer server.Close()

	config := &ClientConfig{
		APIKey:  "test-key",
		BaseURL: server.URL,
		Timeout: 5 * time.Second,
		Logger:  slog.Default(),
	}
	underlyingClient := NewGroqClient(config)

	client := &BusinessGroqClient{
		client: underlyingClient,
		logger: slog.Default(),
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		messages := []GroqMessage{
			{Role: "user", Content: "test description"},
		}
		_, err := client.AnalyzeBusinessWithMessages(ctx, messages)
		if err != nil {
			b.Fatalf("unexpected error: %v", err)
		}
	}
}
