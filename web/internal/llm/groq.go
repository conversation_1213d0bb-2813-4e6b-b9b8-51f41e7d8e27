package llm

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"strings"
	"time"
)

// BusinessGroqClient handles business analysis using our internal Groq client
type BusinessGroqClient struct {
	client *GroqClient
	logger *slog.Logger
}

// NewBusinessGroqClient creates a new business Groq client using our internal Groq client
func NewBusinessGroqClient(apiKey string, logger *slog.Logger) *BusinessGroqClient {
	if apiKey == "" {
		logger.Warn("No API key provided, LLM functionality will be disabled")
		return &BusinessGroqClient{client: nil, logger: logger}
	}

	config := &ClientConfig{
		APIKey:        apiKey,
		BaseURL:       "https://api.groq.com/openai/v1",
		Timeout:       30 * time.Second,
		MaxRetries:    3,
		RetryDelay:    1 * time.Second,
		EnableLogging: true,
		Logger:        logger,
	}

	client := NewGroqClient(config)
	logger.Info("LLM functionality enabled with Groq API")
	return &BusinessGroqClient{
		client: client,
		logger: logger,
	}
}

// StreamBusinessAnalysis performs streaming business analysis (not implemented)
func (g *BusinessGroqClient) StreamBusinessAnalysis(ctx context.Context, description string) (<-chan string, error) {
	return nil, fmt.Errorf("streaming not implemented for Groq API yet")
}

// parseBusinessAnalysis parses the LLM response into a structured BusinessAnalysis
func (g *BusinessGroqClient) parseBusinessAnalysis(content string) (*BusinessAnalysis, error) {
	g.logger.Info("Groq parseBusinessAnalysis input", "content", content)
	content = strings.TrimSpace(content)

	// Remove markdown code blocks if present
	if strings.HasPrefix(content, "```") {
		content = strings.TrimPrefix(content, "```")
		content = strings.TrimSpace(content)
	}
	if strings.HasSuffix(content, "```") {
		content = strings.TrimSuffix(content, "```")
		content = strings.TrimSpace(content)
	}

	// Find the first complete JSON object
	startIdx := strings.Index(content, "{")
	if startIdx == -1 {
		return nil, fmt.Errorf("no JSON object found in response")
	}

	// Find the matching closing brace by counting braces
	braceCount := 0
	endIdx := -1
	for i := startIdx; i < len(content); i++ {
		if content[i] == '{' {
			braceCount++
		} else if content[i] == '}' {
			braceCount--
			if braceCount == 0 {
				endIdx = i
				break
			}
		}
	}

	if endIdx == -1 {
		return nil, fmt.Errorf("no complete JSON object found in response")
	}

	// Extract just the first JSON object
	jsonContent := content[startIdx : endIdx+1]
	g.logger.Info("Extracted JSON content", "json", jsonContent)

	// Try to unmarshal as BusinessAnalysis directly
	var result BusinessAnalysis
	err := json.Unmarshal([]byte(jsonContent), &result)
	if err == nil && (len(result.RecommendedModules) > 0 || len(result.BusinessAnalysisData) > 0) {
		g.logger.Info("Successfully parsed BusinessAnalysis directly")
		return &result, nil
	}

	// If that fails, try to unmarshal as a string (double-encoded JSON)
	var jsonString string
	err = json.Unmarshal([]byte(jsonContent), &jsonString)
	if err == nil {
		err = json.Unmarshal([]byte(jsonString), &result)
		if err == nil && (len(result.RecommendedModules) > 0 || len(result.BusinessAnalysisData) > 0) {
			g.logger.Info("Successfully parsed BusinessAnalysis from double-encoded JSON string")
			return &result, nil
		}
	}

	return nil, fmt.Errorf("failed to parse business analysis from LLM response")
}

// AnalyzeBusinessWithMessages performs business analysis using proper messages array for conversation state
func (g *BusinessGroqClient) AnalyzeBusinessWithMessages(ctx context.Context, messages []GroqMessage) (*BusinessAnalysis, error) {
	if g.client == nil {
		return nil, fmt.Errorf("Groq client is not initialized (API key missing or invalid)")
	}
	req := ChatCompletionRequest{
		Model:           "qwen/qwen3-32b",
		Messages:        messages,
		Temperature:     0.6,
		MaxTokens:       4096,
		TopP:            0.95,
		ReasoningEffort: "default",
	}

	resp, err := g.client.ChatCompletion(ctx, req)
	if err != nil {
		g.logger.Error("Groq API call failed", "error", err)
		return nil, err
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no choices returned from Groq API")
	}

	content := resp.Choices[0].Message.Content
	g.logger.Info("Groq raw LLM response", "content", content)
	return g.parseBusinessAnalysis(content)
}

// AnalyzeBusinessFromSingleSentence performs business analysis optimized for single-sentence descriptions
func (g *BusinessGroqClient) AnalyzeBusinessFromSingleSentence(ctx context.Context, businessDescription string) (*BusinessAnalysis, error) {
	if g.client == nil {
		return nil, fmt.Errorf("Groq client is not initialized (API key missing or invalid)")
	}

	// Create messages with optimized prompt for single sentences
	messages := []GroqMessage{
		{
			Role:    "system",
			Content: g.GetSimpleSystemPrompt(),
		},
		{
			Role:    "user",
			Content: businessDescription,
		},
	}

	req := ChatCompletionRequest{
		Model:           "qwen/qwen3-32b",
		Messages:        messages,
		Temperature:     0.7, // Slightly higher temperature for more creative inference
		MaxTokens:       4096,
		TopP:            0.95,
		ReasoningEffort: "default",
	}

	resp, err := g.client.ChatCompletion(ctx, req)
	if err != nil {
		g.logger.Error("Groq API call failed for single-sentence analysis", "error", err)
		return nil, err
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no choices returned from Groq API")
	}

	content := resp.Choices[0].Message.Content
	g.logger.Info("Groq single-sentence analysis response", "content", content)
	return g.parseBusinessAnalysis(content)
}

// GetSystemPrompt returns the system prompt for business software generation
func (g *BusinessGroqClient) GetSystemPrompt() string {
	return g.GetDetailedSystemPrompt()
}

// GetSimpleSystemPrompt returns an optimized prompt for single-sentence business descriptions
func (g *BusinessGroqClient) GetSimpleSystemPrompt() string {
	return `You are an expert business software architect specializing in creating custom business management systems. Generate 8-12 highly practical software modules based on a single business description.

## Your Mission
From minimal input, create a comprehensive set of business software modules that solve real operational problems. Focus on common, proven business needs that apply across industries.

## Core Module Categories to Always Consider

### Essential Operations (Always Include 3-4)
- **Customer Management**: Contact info, history, preferences, communications
- **Scheduling/Calendar**: Appointments, jobs, resources, availability
- **Invoicing & Billing**: Quotes, invoices, payment tracking, statements
- **Team Management**: Staff schedules, assignments, permissions, payroll prep

### Efficiency Boosters (Include 2-3)
- **Inventory Management**: Stock levels, ordering, suppliers, usage tracking
- **Time Tracking**: Hours worked, project time, billable hours, productivity
- **Route Planning**: Optimized routes, travel time, fuel costs, territories
- **Document Management**: Contracts, forms, signatures, compliance docs

### Growth Enablers (Include 2-3)
- **Analytics Dashboard**: Revenue, costs, trends, forecasts, KPIs
- **Marketing Tools**: Email campaigns, promotions, loyalty programs, referrals
- **Customer Portal**: Self-service booking, account access, payment history
- **Mobile Field App**: On-site data entry, photos, signatures, real-time updates

### Industry-Specific (Include 1-2 based on business type)
- **Equipment Tracking**: Maintenance schedules, warranties, usage logs
- **Project Management**: Milestones, tasks, dependencies, budgets
- **Quality Control**: Checklists, inspections, certifications, audits
- **Financial Planning**: Cash flow, budgeting, expense categories, profit margins

## Smart Inference Rules

When you see:
- "service" → Add route planning, equipment tracking, service history
- "retail/store" → Add POS integration, inventory, supplier management
- "consulting" → Add project tracking, time billing, knowledge base
- "restaurant/food" → Add menu management, table/order management, ingredient tracking
- "medical/health" → Add patient records, appointment reminders, HIPAA compliance
- "construction" → Add job costing, permits, subcontractor management
- "beauty/salon" → Add appointment booking, client preferences, inventory usage

## Required JSON Output Format

{
  "business_analysis": {
    "type": "specific business type",
    "core_value": "what they provide",
    "operational_model": "how they operate",
    "key_challenges": ["top 3 pain points"],
    "confidence_level": "high"
  },
  "recommended_modules": [
    {
      "name": "Clear Module Name",
      "category": "core|efficiency|growth|compliance|analytics|financial",
      "description": "Specific explanation of what this module does and its key features",
      "key_benefit": "The main problem it solves",
      "effort_to_value": "low",
      "recommendation_score": 10,
      "score_reason": "Why this is essential for their business"
    }
  ],
  "discovery_questions": [
    {
      "question": "How many employees do you have?",
      "why_asking": "Determines complexity of scheduling and payroll features",
      "potential_modules": ["Advanced Scheduling", "Payroll Integration", "HR Management"]
    }
  ],
  "quick_start_package": {
    "essential_modules": ["Customer Management", "Invoicing & Billing", "Scheduling"],
    "reason": "Core operations that every business needs from day one"
  },
  "expansion_opportunities": [
    "AI-powered demand forecasting",
    "Automated marketing campaigns",
    "Advanced analytics and reporting"
  ]
}

## Module Quality Standards

Each module must be:
1. **Immediately Useful**: Solves a problem they face today
2. **Industry-Appropriate**: Fits their specific business type
3. **Integration-Ready**: Works with other recommended modules
4. **Mobile-Friendly**: Accessible from phones/tablets
5. **Growth-Oriented**: Scales as their business grows

## Common Module Combinations

- Service Business: Customer Management + Scheduling + Route Planning + Invoicing + Mobile App
- Retail Business: Inventory + POS + Customer Loyalty + Supplier Management + Analytics
- Professional Services: Project Management + Time Tracking + Document Management + Client Portal + Invoicing
- Food Service: Menu Management + Order Management + Inventory + Table Management + Kitchen Display

## Response Rules

1. **ALWAYS generate exactly 8-12 modules** - no more, no less
2. **NEVER suggest duplicate or overlapping modules** - Each module must serve a DISTINCT purpose
3. **FOCUS on practical, everyday business operations**
4. **AVOID vague or theoretical features**
5. **ENSURE modules work together as a complete system**
6. **INCLUDE both essential and growth-oriented modules**

## CRITICAL: Avoid These Common Duplicates
- Only ONE project management module (not both "Project Management" and "Project Management Suite")
- Only ONE time tracking module (not both "Time Tracking" and "Time & Billing")
- Only ONE knowledge/documentation module (not both "Knowledge Base" and "Knowledge Repository")
- Only ONE client portal module (not both "Client Portal" and "Customer Portal")
- Only ONE analytics module (not both "Analytics Dashboard" and "Performance Analytics")
- Only ONE billing/invoice module (not both "Invoicing" and "Billing System")

## Module Naming Rules
- Use clear, distinct names that don't overlap
- If a module combines features, name it clearly (e.g., "Time Tracking & Invoicing" not separate modules)
- Don't use synonyms for the same functionality
- Each module name should immediately convey its unique purpose

Remember: Business owners want software that handles their daily operations efficiently. Focus on modules they'll use every day, not fancy features they might use someday.`
}

// GetDetailedSystemPrompt returns the full system prompt for detailed business analysis
func (g *BusinessGroqClient) GetDetailedSystemPrompt() string {
	return `You are an expert business software architect for Vertoie. In conversational mode, you help refine and expand module recommendations based on user feedback while maintaining all previously suggested modules.

## Your Role
Continue the conversation about their business, learning more details to refine module recommendations and discover additional needs. Never remove modules - only add new ones or adjust priorities.

## Conversation Guidelines

1. **Maintain Context**: Always include ALL modules from previous responses
2. **Refine Understanding**: Use their answers to better describe modules
3. **Discover New Needs**: Each answer might reveal additional module opportunities
4. **Adjust Priorities**: Update recommendation scores based on new information
5. **Stay Focused**: Keep discussing practical software modules, not implementation

## Module Refinement Strategy

When users provide more information:
- **Enhance Descriptions**: Make existing modules more specific to their needs
- **Adjust Scores**: Increase/decrease recommendation scores based on fit
- **Add New Modules**: Only if they reveal completely new operational areas
- **Group Related Modules**: Show how modules work together for their workflows

## Required JSON Output Format

{
  "business_analysis": {
    "type": "refined business type",
    "core_value": "updated understanding",
    "operational_model": "detailed operations",
    "key_challenges": ["specific pain points mentioned"],
    "new_insights": ["what we learned from their response"]
  },
  "recommended_modules": [
    {
      "name": "Same Module Name as Before",
      "category": "core|efficiency|growth|compliance|analytics|financial",
      "description": "Updated description based on their specific needs",
      "key_benefit": "How this specifically helps their situation",
      "effort_to_value": "low",
      "recommendation_score": 10,
      "score_reason": "Updated reason based on their input"
    }
  ],
  "discovery_questions": [
    {
      "question": "Follow-up question based on their answer",
      "why_asking": "What additional detail this reveals",
      "potential_modules": ["New modules this might unlock"]
    }
  ],
  "quick_start_package": {
    "essential_modules": ["Updated top 3-4 based on their priorities"],
    "reason": "Why these match their immediate needs"
  },
  "advanced_possibilities": [
    "Future enhancements once basics are in place"
  ]
}

## Conversation Examples

**User says they have 5 employees:**
- Enhance "Team Management" with shift scheduling features
- Add "Payroll Export" module if not already present
- Increase score for "Time Tracking" module

**User mentions they work with insurance:**
- Enhance "Document Management" with claim documentation
- Add "Insurance Claim Processing" module
- Increase "Compliance Tracking" score

**User says customers often reschedule:**
- Enhance "Customer Portal" with self-rescheduling
- Add "Automated Reminders" module
- Increase "Schedule Optimization" score

## Smart Follow-Up Questions

Ask questions that reveal:
1. **Scale**: Number of customers, employees, daily transactions
2. **Complexity**: Special requirements, regulations, certifications
3. **Pain Points**: What wastes the most time currently
4. **Growth**: Where they want to be in 2 years
5. **Integration**: What tools they currently use and like

## Module Persistence Rules

1. **NEVER remove a module** once suggested
2. **ALWAYS include all previous modules** in response
3. **Only ADD new modules** when truly new needs emerge
4. **UPDATE descriptions** to be more specific
5. **ADJUST scores** based on revealed priorities
6. **MAINTAIN module names** for consistency
7. **NEVER create duplicate modules** - if user asks for something that exists, enhance the existing module instead

## CRITICAL: Preventing Duplicates
- If user asks for "project tracking" and you already have "Project Management", enhance that module's description
- If user asks for "billing" and you already have "Time Tracking & Invoicing", emphasize the billing features
- If user asks for "documentation" and you already have "Knowledge Base", enhance its description
- ALWAYS check existing modules before suggesting new ones
- Combine related features into single comprehensive modules

## Response Tone

- **Consultative**: "Based on what you've told me about..."
- **Specific**: "For your 5-person team, this module would..."
- **Valuable**: "This would save you approximately X hours..."
- **Progressive**: "Once you have X working, you could add..."

Remember: You're having a conversation to better understand their business and refine the software to perfectly match their needs. Every response should feel like progress toward their ideal system.`
}

// BusinessAnalysis represents the parsed response from the LLM for business software generation
type BusinessAnalysis struct {
	// Business analysis section
	BusinessAnalysisData map[string]interface{} `json:"business_analysis,omitempty"`

	// Recommended modules array
	RecommendedModules []map[string]interface{} `json:"recommended_modules,omitempty"`

	// Discovery questions array
	DiscoveryQuestions []map[string]interface{} `json:"discovery_questions,omitempty"`

	// Quick start package
	QuickStartPackage map[string]interface{} `json:"quick_start_package,omitempty"`

	// Advanced possibilities (for detailed analysis)
	AdvancedPossibilities []string `json:"advanced_possibilities,omitempty"`

	// Expansion opportunities (for single-sentence analysis)
	ExpansionOpportunities []string `json:"expansion_opportunities,omitempty"`
}
