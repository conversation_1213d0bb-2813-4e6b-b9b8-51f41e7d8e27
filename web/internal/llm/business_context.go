package llm

import (
	"context"
	"fmt"
	"log/slog"
	"strings"

	"github.com/google/uuid"
	"github.com/vertoie/models"
	"github.com/vertoie/web/internal/services"
)

// industryFallback provides general business guidance when no specific example is found
var industryFallback = `## General Business (Fallback)

Common business elements:
- Customer relationship tracking
- Service or product management
- Scheduling and appointments
- Invoice generation and payments
- Basic inventory/resource tracking
- Team task assignment
- Financial reporting
- Communication automation
- Document management
- Performance metrics
- Quality control processes
- Mobile access for field work
- Customer self-service options`

// BusinessContextService handles LLM interactions for business context gathering
type BusinessContextService struct {
	llmClient             *BusinessGroqClient
	logger                *slog.Logger
	exampleService        *models.BusinessPromptExampleService
	threadService         *models.ThreadService
	moduleService         *models.RecommendedModuleService
	prioritizationService *services.ModulePrioritizationService
	adaptationService     *services.BusinessAdaptationService
}

// BusinessContextResponse represents the structured response from business context chat
type BusinessContextResponse struct {
	Message          string                  `json:"message"`
	IsComplete       bool                    `json:"is_complete"`
	ContextData      *models.BusinessContext `json:"context_data,omitempty"`
	NextQuestion     string                  `json:"next_question,omitempty"`
	BusinessAnalysis *BusinessAnalysis       `json:"business_analysis,omitempty"`
}

// ConversationState tracks the current state of the business context conversation
type ConversationState struct {
	CurrentStep    string                 `json:"current_step"`
	CompletedSteps []string               `json:"completed_steps"`
	RequiredFields []string               `json:"required_fields"`
	ContextData    map[string]interface{} `json:"context_data"`
}

// NewBusinessContextService creates a new business context service
func NewBusinessContextService(llmClient *BusinessGroqClient, logger *slog.Logger, exampleService *models.BusinessPromptExampleService, threadService *models.ThreadService, moduleService *models.RecommendedModuleService, prioritizationService *services.ModulePrioritizationService) *BusinessContextService {
	// Create business adaptation service
	adaptationService := services.NewBusinessAdaptationService(logger, exampleService)

	return &BusinessContextService{
		llmClient:             llmClient,
		logger:                logger,
		exampleService:        exampleService,
		threadService:         threadService,
		moduleService:         moduleService,
		prioritizationService: prioritizationService,
		adaptationService:     adaptationService,
	}
}

// ProcessBusinessContextMessage processes a user message and returns an appropriate response
func (s *BusinessContextService) ProcessBusinessContextMessage(ctx context.Context, userMessage string, existingContext *models.BusinessContext, organizationID uuid.UUID) (*BusinessContextResponse, *models.ConversationThread, error) {
	// If LLM is disabled, return error
	if s.llmClient == nil {
		return nil, nil, fmt.Errorf("The AI service is currently unavailable. Please try again later.")
	}

	// Get or create active thread for this organization
	thread, err := s.threadService.GetActiveThread(organizationID)
	if err != nil {
		s.logger.Error("failed to get active thread", "error", err, "organization_id", organizationID)
		return nil, nil, fmt.Errorf("Failed to load conversation thread")
	}

	// Check if thread is full and needs replacement
	if thread.IsFull() {
		s.logger.Info("Thread is full, creating new thread", "thread_id", thread.ID, "organization_id", organizationID)
		newThread, err := s.threadService.CreateThreadFromFirstMessage(organizationID, userMessage, thread.BusinessExampleID)
		if err != nil {
			s.logger.Error("failed to create new thread", "error", err)
			return nil, nil, fmt.Errorf("Failed to create new conversation thread")
		}
		thread = newThread
	}

	// Get conversation history for this thread
	conversationHistory, err := s.threadService.GetThreadConversations(thread.ID)
	if err != nil {
		s.logger.Error("failed to get thread conversations", "error", err, "thread_id", thread.ID)
		// Don't fail the request, just use empty history
		conversationHistory = []models.BusinessContextConversation{}
	}

	// Perform business adaptation analysis for new threads
	var selectedExample *models.BusinessPromptExample
	var adaptationResult *services.AdaptationResult
	if len(conversationHistory) == 0 {
		adaptationResult = s.performBusinessAdaptation(userMessage, organizationID)
		if adaptationResult.Level == services.AdaptationSpecific && len(adaptationResult.Examples) > 0 {
			// For specific matches, try to get the actual example
			selectedExample = s.performKeywordMatching(userMessage, organizationID)
		}
	}

	// Build messages array for proper conversation state management
	// If we have a selected example, enhance the system prompt
	messages := s.buildMessagesArray(userMessage, conversationHistory, selectedExample, adaptationResult)

	// Get response from LLM using messages array
	llmResponse, err := s.llmClient.AnalyzeBusinessWithMessages(ctx, messages)
	if err != nil {
		s.logger.Error("failed to get LLM response", "error", err)
		return nil, thread, fmt.Errorf("The AI service is currently unavailable. Please try again later.")
	}

	// Save modules to database if we have recommendations
	if llmResponse != nil && len(llmResponse.RecommendedModules) > 0 {
		err = s.saveModulesToDatabase(llmResponse.RecommendedModules, organizationID)
		if err != nil {
			s.logger.Error("failed to save modules from chat", "error", err)
			// Don't fail the request for this
		}
	}

	// Use the actual LLM response for the chat
	response, err := s.processLLMResponse(llmResponse, userMessage, existingContext, conversationHistory)
	return response, thread, err
}

// performKeywordMatching finds relevant business examples using keyword matching
func (s *BusinessContextService) performKeywordMatching(userMessage string, organizationID uuid.UUID) *models.BusinessPromptExample {
	if s.exampleService == nil {
		return nil
	}

	// Extract keywords and search for matches
	keywords := s.exampleService.ExtractKeywords(userMessage)
	s.logger.Info("Extracted keywords for business matching",
		"organization_id", organizationID,
		"keywords", keywords,
		"user_message", userMessage)

	// Find the best match using keyword-based database query
	result, err := s.exampleService.FindBestMatch(userMessage)
	if err != nil {
		s.logger.Error("Error finding business example match",
			"organization_id", organizationID,
			"error", err)
		return nil
	}

	if result == nil || result.Score < 0.3 { // Minimum threshold for matching
		s.logger.Info("No good keyword match found for business",
			"organization_id", organizationID,
			"user_message", userMessage,
			"keywords", keywords)
		return nil
	}

	s.logger.Info("Found business example match",
		"organization_id", organizationID,
		"example_name", result.Example.Name,
		"match_score", result.Score,
		"keywords", keywords,
		"user_message", userMessage)

	return result.Example
}

// performBusinessAdaptation performs graduated business adaptation analysis
func (s *BusinessContextService) performBusinessAdaptation(userMessage string, organizationID uuid.UUID) *services.AdaptationResult {
	if s.adaptationService == nil {
		// Fallback to basic adaptation
		return &services.AdaptationResult{
			Level:       services.AdaptationGeneric,
			Confidence:  0.3,
			Guidance:    industryFallback,
			Examples:    []string{"General Business"},
			Category:    services.Generic,
			DisplayText: "💡 I've created general business recommendations.",
		}
	}

	result := s.adaptationService.GetAdaptationLevel(userMessage)

	s.logger.Info("Business adaptation analysis completed",
		"organization_id", organizationID,
		"level", result.Level,
		"confidence", result.Confidence,
		"category", result.Category,
		"user_message", userMessage)

	return result
}

// buildMessagesArray creates a proper messages array for LLM conversation state
func (s *BusinessContextService) buildMessagesArray(userMessage string, conversationHistory []models.BusinessContextConversation, selectedExample *models.BusinessPromptExample, adaptationResult *services.AdaptationResult) []GroqMessage {
	var messages []GroqMessage

	// Start with the system prompt from groq client
	systemPrompt := s.llmClient.GetSystemPrompt()

	// Add business-specific guidance based on adaptation result
	if adaptationResult != nil {
		switch adaptationResult.Level {
		case services.AdaptationSpecific:
			systemPrompt = systemPrompt + "\n\n## BUSINESS-SPECIFIC GUIDANCE\n\n" + adaptationResult.Guidance
			s.logger.Info("Enhanced system prompt with specific business guidance",
				"examples", adaptationResult.Examples,
				"confidence", adaptationResult.Confidence)
		case services.AdaptationCategory:
			systemPrompt = systemPrompt + "\n\n## BUSINESS CATEGORY GUIDANCE\n\n" + adaptationResult.Guidance
			s.logger.Info("Enhanced system prompt with category guidance",
				"category", adaptationResult.Category,
				"confidence", adaptationResult.Confidence)
		default:
			systemPrompt = systemPrompt + "\n\n## ENHANCED BUSINESS GUIDANCE\n\n" + adaptationResult.Guidance
			s.logger.Info("Using enhanced generic guidance for system prompt",
				"confidence", adaptationResult.Confidence)
		}
	} else if selectedExample != nil {
		// Fallback to old system for compatibility
		systemPrompt = systemPrompt + "\n\n## BUSINESS-SPECIFIC GUIDANCE\n\n" + selectedExample.Prompt
		s.logger.Info("Enhanced system prompt with business example", "example_name", selectedExample.Name)
	} else {
		// If no adaptation available, use the general fallback
		systemPrompt = systemPrompt + "\n\n## GENERAL BUSINESS GUIDANCE\n\n" + industryFallback
		s.logger.Info("Using general business fallback for system prompt")
	}

	messages = append(messages, GroqMessage{
		Role:    "system",
		Content: systemPrompt,
	})

	// Add conversation history as user/assistant messages
	for _, msg := range conversationHistory {
		if msg.MessageRole == "assistant" {
			messages = append(messages, GroqMessage{
				Role:    "assistant",
				Content: msg.MessageContent,
			})
		} else {
			messages = append(messages, GroqMessage{
				Role:    "user",
				Content: msg.MessageContent,
			})
		}
	}

	// Add the current user message
	messages = append(messages, GroqMessage{
		Role:    "user",
		Content: userMessage,
	})

	s.logger.Info("Built messages array", "message_count", len(messages))
	return messages
}

// processLLMResponse processes the LLM response and updates business context
func (s *BusinessContextService) processLLMResponse(llmResponse *BusinessAnalysis, userMessage string, existingContext *models.BusinessContext, conversationHistory []models.BusinessContextConversation) (*BusinessContextResponse, error) {
	// If the LLM response is nil, return an error
	if llmResponse == nil {
		return nil, fmt.Errorf("LLM response is nil")
	}

	// Extract the conversational message from the LLM response
	// The LLM response contains structured data, but we need a conversational message
	conversationalMessage := s.extractConversationalMessage(llmResponse, userMessage, conversationHistory)

	// Determine if the context is complete based on the LLM response
	isComplete := s.checkContextCompletenessFromLLM(llmResponse, existingContext, conversationHistory)

	return &BusinessContextResponse{
		Message:          conversationalMessage,
		IsComplete:       isComplete,
		BusinessAnalysis: llmResponse,
		NextQuestion:     s.getNextQuestionFromLLM(llmResponse),
	}, nil
}

// extractConversationalMessage extracts a conversational message from the LLM response
func (s *BusinessContextService) extractConversationalMessage(llmResponse *BusinessAnalysis, userMessage string, conversationHistory []models.BusinessContextConversation) string {
	// If this is the first response and we have business analysis, acknowledge the business type
	if len(conversationHistory) < 4 && llmResponse.BusinessAnalysisData != nil {
		if businessType, ok := llmResponse.BusinessAnalysisData["type"].(string); ok && businessType != "" {
			return fmt.Sprintf("Great! I can see you're in the %s business. I've analyzed your needs and have some software module recommendations for you. Let me know if you'd like me to ask any follow-up questions to refine these recommendations.", businessType)
		}
	}

	// If we have discovery questions, ask the first one
	if len(llmResponse.DiscoveryQuestions) > 0 {
		if question, ok := llmResponse.DiscoveryQuestions[0]["question"].(string); ok {
			return question
		}
	}

	// Default response acknowledging the input
	return "Thank you for that information! I've updated my recommendations based on what you've shared. Is there anything specific about your business operations you'd like me to know more about?"
}

// checkContextCompletenessFromLLM determines if the context is complete based on LLM response
func (s *BusinessContextService) checkContextCompletenessFromLLM(llmResponse *BusinessAnalysis, existingContext *models.BusinessContext, conversationHistory []models.BusinessContextConversation) bool {
	// If we have a business analysis with modules, consider it complete enough to show recommendations
	if llmResponse != nil && len(llmResponse.RecommendedModules) > 0 {
		return true
	}

	// Simple fallback: if we have existing context that's complete, return true
	if existingContext != nil && existingContext.IsComplete() {
		return true
	}

	return false
}

// getNextQuestionFromLLM extracts the next question from the LLM response
func (s *BusinessContextService) getNextQuestionFromLLM(llmResponse *BusinessAnalysis) string {
	if llmResponse != nil && len(llmResponse.DiscoveryQuestions) > 0 {
		if question, ok := llmResponse.DiscoveryQuestions[0]["question"].(string); ok {
			return question
		}
	}
	return ""
}

// UpdateBusinessContext updates the business context with new information
func (s *BusinessContextService) UpdateBusinessContext(existingContext *models.BusinessContext, userMessage string) *models.BusinessContext {
	if existingContext == nil {
		existingContext = &models.BusinessContext{}
	}

	message := strings.ToLower(userMessage)

	// Extract business type
	if existingContext.BusinessType == "" {
		if strings.Contains(message, "pool") && strings.Contains(message, "clean") {
			existingContext.BusinessType = "pool cleaning service"
		} else if strings.Contains(message, "consult") {
			existingContext.BusinessType = "consulting practice"
		} else if strings.Contains(message, "retail") || strings.Contains(message, "store") {
			existingContext.BusinessType = "retail store"
		} else if strings.Contains(message, "health") || strings.Contains(message, "medical") {
			existingContext.BusinessType = "healthcare practice"
		}
	}

	// Extract industry
	if existingContext.Industry == "" {
		if strings.Contains(message, "service") {
			existingContext.Industry = "services"
		} else if strings.Contains(message, "health") || strings.Contains(message, "medical") {
			existingContext.Industry = "healthcare"
		} else if strings.Contains(message, "retail") || strings.Contains(message, "store") {
			existingContext.Industry = "retail"
		} else if strings.Contains(message, "manufactur") {
			existingContext.Industry = "manufacturing"
		}
	}

	// Extract key processes
	if len(existingContext.KeyProcesses) == 0 {
		var processes []string
		if strings.Contains(message, "schedule") || strings.Contains(message, "appointment") {
			processes = append(processes, "scheduling appointments")
		}
		if strings.Contains(message, "invoice") || strings.Contains(message, "billing") {
			processes = append(processes, "invoicing clients")
		}
		if strings.Contains(message, "inventory") || strings.Contains(message, "stock") {
			processes = append(processes, "inventory management")
		}
		if strings.Contains(message, "customer") || strings.Contains(message, "client") {
			processes = append(processes, "customer relationship management")
		}
		existingContext.KeyProcesses = processes
	}

	// Extract customer types
	if len(existingContext.CustomerTypes) == 0 {
		var customerTypes []string
		if strings.Contains(message, "residential") || strings.Contains(message, "home") {
			customerTypes = append(customerTypes, "residential homeowners")
		}
		if strings.Contains(message, "commercial") || strings.Contains(message, "business") {
			customerTypes = append(customerTypes, "commercial businesses")
		}
		if strings.Contains(message, "individual") || strings.Contains(message, "consumer") {
			customerTypes = append(customerTypes, "individual consumers")
		}
		existingContext.CustomerTypes = customerTypes
	}

	return existingContext
}

// updateModulePriorityScores calculates and updates priority scores for all modules in an organization
func (s *BusinessContextService) updateModulePriorityScores(organizationID uuid.UUID) error {
	if s.prioritizationService == nil {
		return fmt.Errorf("prioritization service not available")
	}

	// Get all modules for the organization
	modules, err := s.moduleService.GetModulesForOrganization(organizationID)
	if err != nil {
		return fmt.Errorf("failed to get modules: %w", err)
	}

	// Get business description to classify business type
	businessType := s.getBusinessTypeForOrganization(organizationID)

	// Calculate priority scores for each module
	for _, module := range modules {
		priorityScore := s.prioritizationService.CalculateFinalScore(module, businessType)

		// Update the module with the new priority score
		err = s.moduleService.UpdateModulePriorityScore(module.ID, priorityScore)
		if err != nil {
			s.logger.Error("failed to update priority score for module",
				"error", err,
				"module_id", module.ID,
				"module_name", module.Name,
				"priority_score", priorityScore)
			continue
		}

		s.logger.Debug("updated module priority score",
			"module_id", module.ID,
			"module_name", module.Name,
			"old_score", module.RecommendationScore,
			"new_priority_score", priorityScore,
			"business_type", businessType)
	}

	return nil
}

// getBusinessTypeForOrganization determines the business type for an organization
func (s *BusinessContextService) getBusinessTypeForOrganization(organizationID uuid.UUID) services.BusinessCategory {
	// Try to get business description from recent conversations
	if s.threadService != nil {
		// Get thread history for this organization
		threads, err := s.threadService.GetThreadHistory(organizationID)
		if err == nil && len(threads) > 0 {
			// Get conversations from the most recent thread
			conversations, err := s.threadService.GetThreadConversations(threads[0].ID)
			if err == nil && len(conversations) > 0 {
				// Use the first user message to classify business type
				for _, conv := range conversations {
					if conv.MessageRole == "user" {
						return s.prioritizationService.ClassifyBusinessType(conv.MessageContent)
					}
				}
			}
		}
	}

	// Fallback to generic if we can't determine the business type
	return services.Generic
}

// ProcessSingleSentenceAnalysis handles single-sentence business analysis for onboarding
func (s *BusinessContextService) ProcessSingleSentenceAnalysis(
	ctx context.Context,
	businessDescription string,
	organizationID uuid.UUID,
) (*BusinessContextResponse, error) {
	s.logger.Info("Processing single-sentence business analysis",
		"organization_id", organizationID,
		"description", businessDescription)

	// Use the optimized single-sentence analysis
	llmResponse, err := s.llmClient.AnalyzeBusinessFromSingleSentence(ctx, businessDescription)
	if err != nil {
		s.logger.Error("failed to get single-sentence LLM response", "error", err)
		return nil, fmt.Errorf("The AI service is currently unavailable. Please try again later.")
	}

	// Save modules to database
	err = s.saveModulesToDatabase(llmResponse.RecommendedModules, organizationID)
	if err != nil {
		s.logger.Error("failed to save modules", "error", err)
		// Don't fail the request for this
	}

	// Create response
	response := &BusinessContextResponse{
		Message:          s.formatBusinessAnalysisMessage(llmResponse),
		IsComplete:       true,
		BusinessAnalysis: llmResponse,
	}

	return response, nil
}

// GeneratePreview generates a quick preview of modules for real-time typing
func (s *BusinessContextService) GeneratePreview(
	ctx context.Context,
	businessDescription string,
	organizationID uuid.UUID,
) (*BusinessContextResponse, error) {
	s.logger.Info("Generating module preview",
		"organization_id", organizationID,
		"description", businessDescription)

	// Use the optimized single-sentence analysis for preview
	llmResponse, err := s.llmClient.AnalyzeBusinessFromSingleSentence(ctx, businessDescription)
	if err != nil {
		s.logger.Error("failed to get preview LLM response", "error", err)
		return nil, fmt.Errorf("The AI service is currently unavailable. Please try again later.")
	}

	// Create response (don't save to database for previews)
	response := &BusinessContextResponse{
		Message:          "Preview generated",
		IsComplete:       false,
		BusinessAnalysis: llmResponse,
	}

	return response, nil
}

// saveModulesToDatabase saves recommended modules to the database
func (s *BusinessContextService) saveModulesToDatabase(modules []map[string]interface{}, organizationID uuid.UUID) error {
	if s.moduleService == nil {
		return fmt.Errorf("module service not available")
	}

	// Use the UpsertModulesFromAI method which handles the conversion and saving
	err := s.moduleService.UpsertModulesFromAI(organizationID, modules)
	if err != nil {
		s.logger.Error("failed to save modules", "error", err, "organization_id", organizationID)
		return err
	}

	// Calculate and update priority scores for all modules
	err = s.updateModulePriorityScores(organizationID)
	if err != nil {
		s.logger.Error("failed to update priority scores", "error", err, "organization_id", organizationID)
		// Don't fail the entire operation, just log the error
	}

	s.logger.Info("successfully saved modules to database", "organization_id", organizationID, "module_count", len(modules))
	return nil
}

// formatBusinessAnalysisMessage formats the business analysis into a readable message
func (s *BusinessContextService) formatBusinessAnalysisMessage(analysis *BusinessAnalysis) string {
	if analysis == nil {
		return "I've analyzed your business and generated custom module recommendations."
	}

	// Extract business type from analysis
	businessType := "business"
	if analysis.BusinessAnalysisData != nil {
		if typeData, ok := analysis.BusinessAnalysisData["type"].(string); ok {
			businessType = typeData
		}
	}

	moduleCount := len(analysis.RecommendedModules)

	return fmt.Sprintf("Perfect! I understand you're running a %s. I've generated %d custom software modules specifically tailored to your business needs. These modules will help streamline your operations, improve efficiency, and support your growth.", businessType, moduleCount)
}

// getStringFromMap safely extracts a string value from a map
func getStringFromMap(m map[string]interface{}, key string) string {
	if value, ok := m[key]; ok {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}
