package llm

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"log/slog"
)

func TestNewGroqClient(t *testing.T) {
	tests := []struct {
		name     string
		config   *ClientConfig
		expected *GroqClient
	}{
		{
			name:   "nil config uses defaults",
			config: nil,
			expected: &GroqClient{
				apiKey:  "",
				baseURL: "https://api.groq.com/openai/v1",
				logger:  slog.Default(),
			},
		},
		{
			name: "custom config",
			config: &ClientConfig{
				APIKey:  "test-key",
				BaseURL: "https://test.com",
				Timeout: 60 * time.Second,
				Logger:  slog.Default(),
			},
			expected: &GroqClient{
				apiKey:  "test-key",
				baseURL: "https://test.com",
				logger:  slog.Default(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := NewGroqClient(tt.config)

			if client.apiKey != tt.expected.apiKey {
				t.Errorf("expected apiKey %s, got %s", tt.expected.apiKey, client.apiKey)
			}
			if client.baseURL != tt.expected.baseURL {
				t.Errorf("expected baseURL %s, got %s", tt.expected.baseURL, client.baseURL)
			}
			if client.httpClient == nil {
				t.Error("expected httpClient to be initialized")
			}
		})
	}
}

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig("test-key")

	if config.APIKey != "test-key" {
		t.Errorf("expected APIKey %s, got %s", "test-key", config.APIKey)
	}
	if config.BaseURL != "https://api.groq.com/openai/v1" {
		t.Errorf("expected BaseURL %s, got %s", "https://api.groq.com/openai/v1", config.BaseURL)
	}
	if config.Timeout != 30*time.Second {
		t.Errorf("expected Timeout %v, got %v", 30*time.Second, config.Timeout)
	}
	if config.MaxRetries != 3 {
		t.Errorf("expected MaxRetries %d, got %d", 3, config.MaxRetries)
	}
}

func TestGroqClient_ChatCompletion(t *testing.T) {
	tests := []struct {
		name           string
		apiKey         string
		serverResponse string
		serverStatus   int
		request        ChatCompletionRequest
		expectError    bool
		errorContains  string
	}{
		{
			name:   "successful request",
			apiKey: "test-key",
			serverResponse: `{
				"id": "test-id",
				"object": "chat.completion",
				"created": 1234567890,
				"model": "qwen/qwen3-32b",
				"choices": [{
					"index": 0,
					"message": {
						"role": "assistant",
						"content": "Hello, world!"
					},
					"finish_reason": "stop"
				}],
				"usage": {
					"prompt_tokens": 10,
					"completion_tokens": 5,
					"total_tokens": 15
				}
			}`,
			serverStatus: http.StatusOK,
			request: ChatCompletionRequest{
				Model: "qwen/qwen3-32b",
				Messages: []GroqMessage{
					{Role: "user", Content: "Hello"},
				},
				Temperature: 0.7,
				MaxTokens:   100,
			},
			expectError: false,
		},
		{
			name:           "missing API key",
			apiKey:         "",
			serverResponse: "",
			serverStatus:   http.StatusOK,
			request: ChatCompletionRequest{
				Model: "qwen/qwen3-32b",
				Messages: []GroqMessage{
					{Role: "user", Content: "Hello"},
				},
			},
			expectError:   true,
			errorContains: "API key is required",
		},
		{
			name:   "API error response",
			apiKey: "test-key",
			serverResponse: `{
				"error": {
					"message": "Invalid API key",
					"type": "invalid_request_error",
					"code": "invalid_api_key"
				}
			}`,
			serverStatus: http.StatusUnauthorized,
			request: ChatCompletionRequest{
				Model: "qwen/qwen3-32b",
				Messages: []GroqMessage{
					{Role: "user", Content: "Hello"},
				},
			},
			expectError:   true,
			errorContains: "Invalid API key",
		},
		{
			name:   "no choices in response",
			apiKey: "test-key",
			serverResponse: `{
				"id": "test-id",
				"object": "chat.completion",
				"created": 1234567890,
				"model": "qwen/qwen3-32b",
				"choices": [],
				"usage": {
					"prompt_tokens": 10,
					"completion_tokens": 0,
					"total_tokens": 10
				}
			}`,
			serverStatus: http.StatusOK,
			request: ChatCompletionRequest{
				Model: "qwen/qwen3-32b",
				Messages: []GroqMessage{
					{Role: "user", Content: "Hello"},
				},
			},
			expectError:   true,
			errorContains: "no choices returned",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// Verify request headers
				if r.Header.Get("Authorization") != "Bearer "+tt.apiKey {
					t.Errorf("expected Authorization header 'Bearer %s', got %s", tt.apiKey, r.Header.Get("Authorization"))
				}
				if r.Header.Get("Content-Type") != "application/json" {
					t.Errorf("expected Content-Type header 'application/json', got %s", r.Header.Get("Content-Type"))
				}

				// Verify request body
				var req ChatCompletionRequest
				if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
					t.Errorf("failed to decode request body: %v", err)
				}

				// Check that stream is set to false
				if req.Stream {
					t.Error("expected stream to be false for non-streaming request")
				}

				w.WriteHeader(tt.serverStatus)
				w.Write([]byte(tt.serverResponse))
			}))
			defer server.Close()

			// Create client with test server URL
			config := &ClientConfig{
				APIKey:  tt.apiKey,
				BaseURL: server.URL,
				Timeout: 5 * time.Second,
				Logger:  slog.Default(),
			}
			client := NewGroqClient(config)

			// Make request
			ctx := context.Background()
			resp, err := client.ChatCompletion(ctx, tt.request)

			// Check results
			if tt.expectError {
				if err == nil {
					t.Error("expected error, got nil")
				} else if tt.errorContains != "" && !strings.Contains(err.Error(), tt.errorContains) {
					t.Errorf("expected error to contain '%s', got '%s'", tt.errorContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if resp == nil {
					t.Error("expected response, got nil")
				} else if len(resp.Choices) == 0 {
					t.Error("expected choices in response")
				}
			}
		})
	}
}

func TestGroqClient_StreamChatCompletion(t *testing.T) {
	tests := []struct {
		name            string
		apiKey          string
		serverResponses []string
		serverStatus    int
		request         ChatCompletionRequest
		expectError     bool
		errorContains   string
		expectedChunks  int
	}{
		{
			name:   "successful streaming",
			apiKey: "test-key",
			serverResponses: []string{
				"data: {\"id\":\"test-1\",\"object\":\"chat.completion.chunk\",\"created\":1234567890,\"model\":\"qwen/qwen3-32b\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"Hello\"}}]}\n\n",
				"data: {\"id\":\"test-2\",\"object\":\"chat.completion.chunk\",\"created\":1234567890,\"model\":\"qwen/qwen3-32b\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\" world\"}}]}\n\n",
				"data: {\"id\":\"test-3\",\"object\":\"chat.completion.chunk\",\"created\":1234567890,\"model\":\"qwen/qwen3-32b\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"!\"},\"finish_reason\":\"stop\"}]}\n\n",
				"data: [DONE]\n\n",
			},
			serverStatus: http.StatusOK,
			request: ChatCompletionRequest{
				Model: "qwen/qwen3-32b",
				Messages: []GroqMessage{
					{Role: "user", Content: "Hello"},
				},
				Temperature: 0.7,
				MaxTokens:   100,
			},
			expectError:    false,
			expectedChunks: 3,
		},
		{
			name:            "missing API key",
			apiKey:          "",
			serverResponses: []string{},
			serverStatus:    http.StatusOK,
			request: ChatCompletionRequest{
				Model: "qwen/qwen3-32b",
				Messages: []GroqMessage{
					{Role: "user", Content: "Hello"},
				},
			},
			expectError:    true,
			errorContains:  "API key is required",
			expectedChunks: 0,
		},
		{
			name:            "API error response",
			apiKey:          "test-key",
			serverResponses: []string{},
			serverStatus:    http.StatusUnauthorized,
			request: ChatCompletionRequest{
				Model: "qwen/qwen3-32b",
				Messages: []GroqMessage{
					{Role: "user", Content: "Hello"},
				},
			},
			expectError:    true,
			errorContains:  "HTTP error [401]",
			expectedChunks: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// Verify request headers
				if r.Header.Get("Authorization") != "Bearer "+tt.apiKey {
					t.Errorf("expected Authorization header 'Bearer %s', got %s", tt.apiKey, r.Header.Get("Authorization"))
				}
				if r.Header.Get("Content-Type") != "application/json" {
					t.Errorf("expected Content-Type header 'application/json', got %s", r.Header.Get("Content-Type"))
				}
				if r.Header.Get("Accept") != "text/event-stream" {
					t.Errorf("expected Accept header 'text/event-stream', got %s", r.Header.Get("Accept"))
				}

				// Verify request body
				var req ChatCompletionRequest
				if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
					t.Errorf("failed to decode request body: %v", err)
				}

				// Check that stream is set to true
				if !req.Stream {
					t.Error("expected stream to be true for streaming request")
				}

				w.WriteHeader(tt.serverStatus)
				w.Header().Set("Content-Type", "text/event-stream")

				// Write streaming responses
				for _, response := range tt.serverResponses {
					w.Write([]byte(response))
					if f, ok := w.(http.Flusher); ok {
						f.Flush()
					}
				}
			}))
			defer server.Close()

			// Create client with test server URL
			config := &ClientConfig{
				APIKey:  tt.apiKey,
				BaseURL: server.URL,
				Timeout: 5 * time.Second,
				Logger:  slog.Default(),
			}
			client := NewGroqClient(config)

			// Make request
			ctx := context.Background()
			responseChan, errorChan := client.StreamChatCompletion(ctx, tt.request)

			// Collect responses
			var chunks []StreamResponse
			var err error

			for {
				select {
				case resp, ok := <-responseChan:
					if !ok {
						goto done
					}
					chunks = append(chunks, resp)
				case err = <-errorChan:
					goto done
				case <-time.After(2 * time.Second):
					goto done
				}
			}
		done:

			// Check results
			if tt.expectError {
				if err == nil {
					t.Error("expected error, got nil")
				} else if tt.errorContains != "" && !strings.Contains(err.Error(), tt.errorContains) {
					t.Errorf("expected error to contain '%s', got '%s'", tt.errorContains, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				t.Logf("Received %d chunks: %+v", len(chunks), chunks)
				if len(chunks) != tt.expectedChunks {
					t.Errorf("expected %d chunks, got %d", tt.expectedChunks, len(chunks))
				}
			}
		})
	}
}

func TestGroqClient_IsAvailable(t *testing.T) {
	tests := []struct {
		name     string
		apiKey   string
		expected bool
	}{
		{
			name:     "available with API key",
			apiKey:   "test-key",
			expected: true,
		},
		{
			name:     "not available without API key",
			apiKey:   "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &ClientConfig{APIKey: tt.apiKey}
			client := NewGroqClient(config)

			if client.IsAvailable() != tt.expected {
				t.Errorf("expected IsAvailable() to return %v, got %v", tt.expected, client.IsAvailable())
			}
		})
	}
}

func TestGroqClient_GetAPIKey(t *testing.T) {
	expectedKey := "test-key"
	config := &ClientConfig{APIKey: expectedKey}
	client := NewGroqClient(config)

	if client.GetAPIKey() != expectedKey {
		t.Errorf("expected GetAPIKey() to return %s, got %s", expectedKey, client.GetAPIKey())
	}
}

func TestGroqClient_GetBaseURL(t *testing.T) {
	expectedURL := "https://test.com"
	config := &ClientConfig{BaseURL: expectedURL}
	client := NewGroqClient(config)

	if client.GetBaseURL() != expectedURL {
		t.Errorf("expected GetBaseURL() to return %s, got %s", expectedURL, client.GetBaseURL())
	}
}

func TestGroqClient_ContextCancellation(t *testing.T) {
	// Create a server that delays response
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(100 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"choices":[{"message":{"content":"test"}}]}`))
	}))
	defer server.Close()

	config := &ClientConfig{
		APIKey:  "test-key",
		BaseURL: server.URL,
		Timeout: 5 * time.Second,
		Logger:  slog.Default(),
	}
	client := NewGroqClient(config)

	// Create context with short timeout
	ctx, cancel := context.WithTimeout(context.Background(), 50*time.Millisecond)
	defer cancel()

	request := ChatCompletionRequest{
		Model: "qwen/qwen3-32b",
		Messages: []GroqMessage{
			{Role: "user", Content: "Hello"},
		},
	}

	_, err := client.ChatCompletion(ctx, request)
	if err == nil {
		t.Error("expected error due to context cancellation, got nil")
	}
}

func TestGroqClient_MalformedJSON(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"invalid": json}`)) // Malformed JSON
	}))
	defer server.Close()

	config := &ClientConfig{
		APIKey:  "test-key",
		BaseURL: server.URL,
		Timeout: 5 * time.Second,
		Logger:  slog.Default(),
	}
	client := NewGroqClient(config)

	request := ChatCompletionRequest{
		Model: "qwen/qwen3-32b",
		Messages: []GroqMessage{
			{Role: "user", Content: "Hello"},
		},
	}

	_, err := client.ChatCompletion(context.Background(), request)
	if err == nil {
		t.Error("expected error due to malformed JSON, got nil")
	}
}

func TestGroqClient_NetworkError(t *testing.T) {
	config := &ClientConfig{
		APIKey:  "test-key",
		BaseURL: "http://invalid-url-that-does-not-exist.com",
		Timeout: 1 * time.Second,
		Logger:  slog.Default(),
	}
	client := NewGroqClient(config)

	request := ChatCompletionRequest{
		Model: "qwen/qwen3-32b",
		Messages: []GroqMessage{
			{Role: "user", Content: "Hello"},
		},
	}

	_, err := client.ChatCompletion(context.Background(), request)
	if err == nil {
		t.Error("expected error due to network failure, got nil")
	}
}

// Benchmark tests
func BenchmarkGroqClient_ChatCompletion(b *testing.B) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{
			"id": "test-id",
			"choices": [{
				"index": 0,
				"message": {
					"role": "assistant",
					"content": "Hello, world!"
				},
				"finish_reason": "stop"
			}],
			"usage": {
				"prompt_tokens": 10,
				"completion_tokens": 5,
				"total_tokens": 15
			}
		}`))
	}))
	defer server.Close()

	config := &ClientConfig{
		APIKey:  "test-key",
		BaseURL: server.URL,
		Timeout: 5 * time.Second,
		Logger:  slog.Default(),
	}
	client := NewGroqClient(config)

	request := ChatCompletionRequest{
		Model: "qwen/qwen3-32b",
		Messages: []GroqMessage{
			{Role: "user", Content: "Hello"},
		},
		Temperature: 0.7,
		MaxTokens:   100,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := client.ChatCompletion(context.Background(), request)
		if err != nil {
			b.Fatalf("unexpected error: %v", err)
		}
	}
}
