package llm

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"log/slog"
)

// GroqClient handles communication with Groq API using direct HTTP calls
type GroqClient struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
	logger     *slog.Logger
}

// GroqMessage represents a chat message
type GroqMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ChatCompletionRequest represents a request to the Groq API
type ChatCompletionRequest struct {
	Model           string        `json:"model"`
	Messages        []GroqMessage `json:"messages"`
	Temperature     float64       `json:"temperature,omitempty"`
	MaxTokens       int           `json:"max_tokens,omitempty"`
	TopP            float64       `json:"top_p,omitempty"`
	Stream          bool          `json:"stream,omitempty"`
	ReasoningEffort string        `json:"reasoning_effort,omitempty"`
}

// ChatCompletionResponse represents a response from the Groq API
type ChatCompletionResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// StreamResponse represents a streaming response chunk from the Groq API
type StreamResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index        int     `json:"index"`
		Delta        Delta   `json:"delta"`
		FinishReason *string `json:"finish_reason"`
	} `json:"choices"`
}

// Delta represents the delta content in streaming responses
type Delta struct {
	Role    string `json:"role,omitempty"`
	Content string `json:"content,omitempty"`
}

// ErrorResponse represents an error response from the Groq API
type ErrorResponse struct {
	Error struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	} `json:"error"`
}

// ClientConfig holds configuration for the Groq client
type ClientConfig struct {
	APIKey        string
	BaseURL       string
	Timeout       time.Duration
	MaxRetries    int
	RetryDelay    time.Duration
	EnableLogging bool
	Logger        *slog.Logger
}

// DefaultConfig returns a default configuration for the Groq client
func DefaultConfig(apiKey string) *ClientConfig {
	return &ClientConfig{
		APIKey:        apiKey,
		BaseURL:       "https://api.groq.com/openai/v1",
		Timeout:       30 * time.Second,
		MaxRetries:    3,
		RetryDelay:    1 * time.Second,
		EnableLogging: true,
		Logger:        slog.Default(),
	}
}

// NewGroqClient creates a new Groq client with the given configuration
func NewGroqClient(config *ClientConfig) *GroqClient {
	if config == nil {
		config = DefaultConfig("")
	}

	httpClient := &http.Client{
		Timeout: config.Timeout,
	}

	return &GroqClient{
		apiKey:     config.APIKey,
		baseURL:    config.BaseURL,
		httpClient: httpClient,
		logger:     config.Logger,
	}
}

// ChatCompletion performs a chat completion request
func (c *GroqClient) ChatCompletion(ctx context.Context, req ChatCompletionRequest) (*ChatCompletionResponse, error) {
	if c.apiKey == "" {
		return nil, fmt.Errorf("API key is required")
	}

	// Ensure stream is false for non-streaming requests
	req.Stream = false

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", c.baseURL+"/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		var errResp ErrorResponse
		if json.Unmarshal(body, &errResp) == nil {
			return nil, fmt.Errorf("API error [%d]: %s", resp.StatusCode, errResp.Error.Message)
		}
		return nil, fmt.Errorf("HTTP error [%d]: %s", resp.StatusCode, string(body))
	}

	var chatResp ChatCompletionResponse
	if err := json.Unmarshal(body, &chatResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if len(chatResp.Choices) == 0 {
		return nil, fmt.Errorf("no choices returned from Groq API")
	}

	return &chatResp, nil
}

// StreamChatCompletion performs a streaming chat completion request
func (c *GroqClient) StreamChatCompletion(ctx context.Context, req ChatCompletionRequest) (<-chan StreamResponse, <-chan error) {
	responseChan := make(chan StreamResponse, 10)
	errorChan := make(chan error, 1)

	go func() {
		defer close(responseChan)
		defer close(errorChan)

		if c.apiKey == "" {
			errorChan <- fmt.Errorf("API key is required")
			return
		}

		// Ensure stream is true for streaming requests
		req.Stream = true

		jsonData, err := json.Marshal(req)
		if err != nil {
			errorChan <- fmt.Errorf("failed to marshal request: %w", err)
			return
		}

		httpReq, err := http.NewRequestWithContext(ctx, "POST", c.baseURL+"/chat/completions", bytes.NewBuffer(jsonData))
		if err != nil {
			errorChan <- fmt.Errorf("failed to create request: %w", err)
			return
		}

		httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)
		httpReq.Header.Set("Content-Type", "application/json")
		httpReq.Header.Set("Accept", "text/event-stream")
		httpReq.Header.Set("Cache-Control", "no-cache")

		resp, err := c.httpClient.Do(httpReq)
		if err != nil {
			errorChan <- fmt.Errorf("request failed: %w", err)
			return
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(resp.Body)
			var errResp ErrorResponse
			if json.Unmarshal(body, &errResp) == nil {
				errorChan <- fmt.Errorf("API error [%d]: %s", resp.StatusCode, errResp.Error.Message)
			} else {
				errorChan <- fmt.Errorf("HTTP error [%d]: %s", resp.StatusCode, string(body))
			}
			return
		}

		scanner := bufio.NewScanner(resp.Body)
		for scanner.Scan() {
			select {
			case <-ctx.Done():
				errorChan <- ctx.Err()
				return
			default:
			}

			line := scanner.Text()

			// Skip empty lines and comments
			if line == "" || strings.HasPrefix(line, ":") {
				continue
			}

			// Parse SSE format: "data: {json}"
			if strings.HasPrefix(line, "data: ") {
				data := strings.TrimPrefix(line, "data: ")

				// Check for end of stream
				if data == "[DONE]" {
					return
				}

				var streamResp StreamResponse
				if err := json.Unmarshal([]byte(data), &streamResp); err != nil {
					// Log but continue - some malformed chunks are expected
					c.logger.Debug("Failed to parse stream chunk", "error", err, "data", data)
					continue
				}

				responseChan <- streamResp
			}
		}

		if err := scanner.Err(); err != nil {
			errorChan <- fmt.Errorf("error reading stream: %w", err)
		}
	}()

	return responseChan, errorChan
}

// IsAvailable checks if the Groq API is available
func (c *GroqClient) IsAvailable() bool {
	return c.apiKey != ""
}

// GetAPIKey returns the API key (for testing purposes)
func (c *GroqClient) GetAPIKey() string {
	return c.apiKey
}

// GetBaseURL returns the base URL (for testing purposes)
func (c *GroqClient) GetBaseURL() string {
	return c.baseURL
}
