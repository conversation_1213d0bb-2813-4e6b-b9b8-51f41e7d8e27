package database

import (
	"fmt"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

func Initialize(databaseURL string) (*gorm.DB, error) {
	// Configure GORM
	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Error), // Enable all SQL logging for development
		NamingStrategy: schema.NamingStrategy{
			SingularTable: false,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Configure connection pool for read-only workload
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Conservative connection settings for a read-only web app
	sqlDB.SetMaxIdleConns(5)
	sqlDB.SetMaxOpenConns(10)

	// Verify we can read from the required tables (read-only check)
	var count int64
	if err := db.Table("vertoie.plans").Count(&count).Error; err != nil {
		return nil, fmt.Errorf("failed to access plans table: %w", err)
	}

	return db, nil
}
