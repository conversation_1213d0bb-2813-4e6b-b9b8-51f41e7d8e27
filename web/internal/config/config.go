package config

import "os"

type Config struct {
	DatabaseURL string
	Port        string
	Mailgun     MailgunConfig
	Groq        GroqConfig
}

type MailgunConfig struct {
	Domain string
	APIKey string
	Sender string
}

type GroqConfig struct {
	APIKey string
}

func Load() *Config {
	return &Config{
		DatabaseURL: getEnv("DATABASE_URL", "postgres://vertoie:vertoie@localhost:5433/vertoie_dev?sslmode=disable"),
		Port:        getEnv("WEB_PORT", "8001"),
		Mailgun: MailgunConfig{
			Domain: getEnv("MAILGUN_DOMAIN", ""),
			APIKey: getEnv("MAILGUN_API_KEY", ""),
			Sender: getEnv("MAILGUN_SENDER", "<EMAIL>"),
		},
		Groq: GroqConfig{
			APIKey: getEnv("GROQ_API_KEY", ""),
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
