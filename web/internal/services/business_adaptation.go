package services

import (
	"log/slog"
	"strings"
	"github.com/vertoie/models"
)

// BusinessAdaptationService handles graduated business adaptation with confidence scoring
type BusinessAdaptationService struct {
	logger         *slog.Logger
	exampleService *models.BusinessPromptExampleService
}

// NewBusinessAdaptationService creates a new business adaptation service
func NewBusinessAdaptationService(logger *slog.Logger, exampleService *models.BusinessPromptExampleService) *BusinessAdaptationService {
	return &BusinessAdaptationService{
		logger:         logger,
		exampleService: exampleService,
	}
}

// AdaptationLevel represents the level of business-specific adaptation
type AdaptationLevel string

const (
	AdaptationSpecific AdaptationLevel = "specific"  // Exact industry match
	AdaptationCategory AdaptationLevel = "category"  // Business category match
	AdaptationGeneric  AdaptationLevel = "generic"   // Enhanced generic fallback
)

// AdaptationResult contains the result of business adaptation analysis
type AdaptationResult struct {
	Level       AdaptationLevel `json:"level"`
	Confidence  float64         `json:"confidence"`
	Guidance    string          `json:"guidance"`
	Examples    []string        `json:"examples"`
	Category    BusinessCategory `json:"category"`
	DisplayText string          `json:"display_text"`
}

// GetAdaptationLevel analyzes a business description and returns the appropriate adaptation level
func (s *BusinessAdaptationService) GetAdaptationLevel(userMessage string) *AdaptationResult {
	// Try specific industry match first
	if specificMatch := s.findSpecificMatch(userMessage); specificMatch.Confidence > 0.7 {
		return &AdaptationResult{
			Level:       AdaptationSpecific,
			Confidence:  specificMatch.Confidence,
			Guidance:    specificMatch.Example.Prompt,
			Examples:    []string{specificMatch.Example.Name},
			Category:    s.classifyBusinessCategory(userMessage),
			DisplayText: s.getSpecificDisplayText(specificMatch.Example.Name, specificMatch.Confidence),
		}
	}

	// Try business category match
	if categoryMatch := s.findCategoryMatch(userMessage); categoryMatch.Confidence > 0.4 {
		return &AdaptationResult{
			Level:       AdaptationCategory,
			Confidence:  categoryMatch.Confidence,
			Guidance:    s.buildCategoryGuidance(categoryMatch.Category),
			Examples:    s.getCategoryExamples(categoryMatch.Category),
			Category:    categoryMatch.Category,
			DisplayText: s.getCategoryDisplayText(categoryMatch.Category, categoryMatch.Confidence),
		}
	}

	// Enhanced generic fallback
	return &AdaptationResult{
		Level:       AdaptationGeneric,
		Confidence:  0.3,
		Guidance:    s.buildEnhancedGenericGuidance(userMessage),
		Examples:    []string{"General Business"},
		Category:    Generic,
		DisplayText: s.getGenericDisplayText(),
	}
}

// SpecificMatchResult represents a specific industry match
type SpecificMatchResult struct {
	Example    *models.BusinessPromptExample
	Confidence float64
}

// CategoryMatchResult represents a business category match
type CategoryMatchResult struct {
	Category   BusinessCategory
	Confidence float64
}

// findSpecificMatch attempts to find a specific industry example
func (s *BusinessAdaptationService) findSpecificMatch(userMessage string) *SpecificMatchResult {
	if s.exampleService == nil {
		return &SpecificMatchResult{Confidence: 0}
	}

	result, err := s.exampleService.FindBestMatch(userMessage)
	if err != nil || result == nil {
		return &SpecificMatchResult{Confidence: 0}
	}

	return &SpecificMatchResult{
		Example:    result.Example,
		Confidence: result.Score,
	}
}

// findCategoryMatch attempts to classify the business into a broader category
func (s *BusinessAdaptationService) findCategoryMatch(userMessage string) *CategoryMatchResult {
	category := s.classifyBusinessCategory(userMessage)
	confidence := s.calculateCategoryConfidence(userMessage, category)

	return &CategoryMatchResult{
		Category:   category,
		Confidence: confidence,
	}
}

// classifyBusinessCategory classifies business into broader categories
func (s *BusinessAdaptationService) classifyBusinessCategory(userMessage string) BusinessCategory {
	message := strings.ToLower(userMessage)

	// Field service indicators
	fieldServiceKeywords := []string{"service", "repair", "maintenance", "cleaning", "landscaping", "hvac", "plumbing", "electrical", "mobile", "on-site"}
	if s.containsKeywords(message, fieldServiceKeywords, 2) {
		return FieldService
	}

	// Appointment-based indicators
	appointmentKeywords := []string{"appointment", "booking", "salon", "spa", "clinic", "therapy", "consultation", "schedule", "calendar"}
	if s.containsKeywords(message, appointmentKeywords, 2) {
		return AppointmentBased
	}

	// Retail/commerce indicators
	retailKeywords := []string{"retail", "store", "shop", "sell", "product", "merchandise", "boutique", "inventory", "pos"}
	if s.containsKeywords(message, retailKeywords, 2) {
		return RetailCommerce
	}

	// Professional services indicators
	professionalKeywords := []string{"consulting", "agency", "law", "accounting", "marketing", "design", "development", "professional", "client"}
	if s.containsKeywords(message, professionalKeywords, 2) {
		return ProfessionalServices
	}

	// Food service indicators
	foodKeywords := []string{"restaurant", "cafe", "bakery", "catering", "food", "kitchen", "dining", "menu", "order"}
	if s.containsKeywords(message, foodKeywords, 2) {
		return FoodService
	}

	// Healthcare indicators
	healthcareKeywords := []string{"medical", "health", "doctor", "nurse", "patient", "clinic", "hospital", "therapy", "treatment"}
	if s.containsKeywords(message, healthcareKeywords, 2) {
		return HealthcareServices
	}

	return Generic
}

// containsKeywords checks if message contains at least minCount keywords
func (s *BusinessAdaptationService) containsKeywords(message string, keywords []string, minCount int) bool {
	count := 0
	for _, keyword := range keywords {
		if strings.Contains(message, keyword) {
			count++
			if count >= minCount {
				return true
			}
		}
	}
	return false
}

// calculateCategoryConfidence calculates confidence score for category classification
func (s *BusinessAdaptationService) calculateCategoryConfidence(userMessage string, category BusinessCategory) float64 {
	message := strings.ToLower(userMessage)
	
	categoryKeywords := map[BusinessCategory][]string{
		FieldService:        {"service", "repair", "maintenance", "cleaning", "mobile", "on-site"},
		AppointmentBased:    {"appointment", "booking", "salon", "spa", "clinic", "schedule"},
		RetailCommerce:      {"retail", "store", "shop", "sell", "product", "inventory"},
		ProfessionalServices: {"consulting", "agency", "professional", "client", "marketing"},
		FoodService:         {"restaurant", "cafe", "food", "kitchen", "dining", "menu"},
		HealthcareServices:  {"medical", "health", "patient", "clinic", "therapy"},
	}

	keywords, exists := categoryKeywords[category]
	if !exists {
		return 0.3
	}

	matchCount := 0
	for _, keyword := range keywords {
		if strings.Contains(message, keyword) {
			matchCount++
		}
	}

	// Calculate confidence based on keyword matches
	confidence := 0.4 + (float64(matchCount) * 0.1)
	if confidence > 0.9 {
		confidence = 0.9
	}

	return confidence
}

// buildCategoryGuidance creates guidance text for a business category
func (s *BusinessAdaptationService) buildCategoryGuidance(category BusinessCategory) string {
	categoryGuidance := map[BusinessCategory]string{
		FieldService: `## Field Service Business Guidance

Common operational elements:
- Route optimization and scheduling for mobile teams
- Equipment and inventory tracking
- Mobile workforce management with GPS
- Photo documentation workflows
- Customer location and service history
- Work order management and completion tracking`,

		AppointmentBased: `## Appointment-Based Business Guidance

Common operational elements:
- Calendar management and online booking
- Client preference and history tracking
- Reminder and confirmation systems
- Resource and room allocation
- Service package management
- Staff availability coordination`,

		RetailCommerce: `## Retail Commerce Business Guidance

Common operational elements:
- Inventory management and stock tracking
- Point of sale integration
- Supplier relationship management
- Customer loyalty programs
- Product catalog management
- Sales analytics and reporting`,

		ProfessionalServices: `## Professional Services Business Guidance

Common operational elements:
- Project and time tracking
- Document management and templates
- Client portal access
- Billing and invoicing automation
- Knowledge base and resources
- Team collaboration tools`,

		FoodService: `## Food Service Business Guidance

Common operational elements:
- Menu management and pricing
- Order management and kitchen workflows
- Inventory and ingredient tracking
- Table management and reservations
- Staff scheduling and roles
- Health compliance tracking`,

		HealthcareServices: `## Healthcare Services Business Guidance

Common operational elements:
- Patient records and HIPAA compliance
- Appointment scheduling and reminders
- Treatment plans and progress tracking
- Insurance and billing management
- Medical equipment tracking
- Regulatory compliance monitoring`,
	}

	if guidance, exists := categoryGuidance[category]; exists {
		return guidance
	}

	return s.buildEnhancedGenericGuidance("")
}

// buildEnhancedGenericGuidance creates intelligent fallback guidance
func (s *BusinessAdaptationService) buildEnhancedGenericGuidance(userMessage string) string {
	characteristics := s.extractBusinessCharacteristics(userMessage)

	guidance := "## Tailored Business Guidance\n\nBased on your business description, here are relevant operational elements:\n\n"

	if characteristics.HasCustomers {
		guidance += "- Customer relationship and communication management\n"
	}
	if characteristics.HasAppointments {
		guidance += "- Scheduling and calendar coordination\n"
	}
	if characteristics.HasInventory {
		guidance += "- Inventory tracking and management\n"
	}
	if characteristics.HasFieldWork {
		guidance += "- Mobile access and route optimization\n"
	}
	if characteristics.HasPayments {
		guidance += "- Payment processing and financial tracking\n"
	}

	guidance += "\nCore business operations:\n"
	guidance += "- Customer relationship tracking\n"
	guidance += "- Service or product management\n"
	guidance += "- Invoice generation and payments\n"
	guidance += "- Basic reporting and analytics\n"

	return guidance
}

// BusinessCharacteristics represents extracted business characteristics
type BusinessCharacteristics struct {
	HasCustomers    bool
	HasAppointments bool
	HasInventory    bool
	HasFieldWork    bool
	HasPayments     bool
}

// extractBusinessCharacteristics analyzes business description for characteristics
func (s *BusinessAdaptationService) extractBusinessCharacteristics(userMessage string) BusinessCharacteristics {
	message := strings.ToLower(userMessage)

	return BusinessCharacteristics{
		HasCustomers:    strings.Contains(message, "customer") || strings.Contains(message, "client"),
		HasAppointments: strings.Contains(message, "appointment") || strings.Contains(message, "booking") || strings.Contains(message, "schedule"),
		HasInventory:    strings.Contains(message, "inventory") || strings.Contains(message, "stock") || strings.Contains(message, "product"),
		HasFieldWork:    strings.Contains(message, "mobile") || strings.Contains(message, "on-site") || strings.Contains(message, "service"),
		HasPayments:     strings.Contains(message, "payment") || strings.Contains(message, "billing") || strings.Contains(message, "invoice"),
	}
}

// getCategoryExamples returns example business types for a category
func (s *BusinessAdaptationService) getCategoryExamples(category BusinessCategory) []string {
	examples := map[BusinessCategory][]string{
		FieldService:        {"Pool Service", "HVAC Repair", "Landscaping", "Cleaning Service"},
		AppointmentBased:    {"Hair Salon", "Medical Clinic", "Massage Therapy", "Consulting"},
		RetailCommerce:      {"Boutique Store", "Electronics Shop", "Bookstore", "Gift Shop"},
		ProfessionalServices: {"Marketing Agency", "Law Firm", "Accounting", "Design Studio"},
		FoodService:         {"Restaurant", "Cafe", "Bakery", "Catering"},
		HealthcareServices:  {"Medical Practice", "Dental Office", "Physical Therapy", "Veterinary"},
	}

	if exampleList, exists := examples[category]; exists {
		return exampleList
	}

	return []string{"General Business"}
}

// Display text methods for user feedback
func (s *BusinessAdaptationService) getSpecificDisplayText(exampleName string, confidence float64) string {
	return "🎯 Great match! I found specific guidance for " + exampleName + " businesses."
}

func (s *BusinessAdaptationService) getCategoryDisplayText(category BusinessCategory, confidence float64) string {
	categoryNames := map[BusinessCategory]string{
		FieldService:        "field service",
		AppointmentBased:    "appointment-based",
		RetailCommerce:      "retail commerce",
		ProfessionalServices: "professional services",
		FoodService:         "food service",
		HealthcareServices:  "healthcare services",
	}

	if name, exists := categoryNames[category]; exists {
		return "📋 I've identified this as a " + name + " business and tailored recommendations accordingly."
	}

	return s.getGenericDisplayText()
}

func (s *BusinessAdaptationService) getGenericDisplayText() string {
	return "💡 I've created general business recommendations. Feel free to provide more details for better customization."
}
