package services

import (
	"strings"
	"log/slog"
	"github.com/vertoie/models"
)

// ModulePrioritizationService handles intelligent module scoring and prioritization
type ModulePrioritizationService struct {
	logger *slog.Logger
}

// NewModulePrioritizationService creates a new module prioritization service
func NewModulePrioritizationService(logger *slog.Logger) *ModulePrioritizationService {
	return &ModulePrioritizationService{
		logger: logger,
	}
}

// BusinessCategory represents different types of business operations
type BusinessCategory string

const (
	FieldService        BusinessCategory = "field_service"
	AppointmentBased    BusinessCategory = "appointment_based"
	RetailCommerce      BusinessCategory = "retail_commerce"
	ProfessionalServices BusinessCategory = "professional_services"
	FoodService         BusinessCategory = "food_service"
	HealthcareServices  BusinessCategory = "healthcare_services"
	Generic             BusinessCategory = "generic"
)

// CoreModuleDefinition defines core business modules and their base priority scores
type CoreModuleDefinition struct {
	Name            string
	Category        string
	BasePriority    int // 1-3, where 3 is highest priority
	Keywords        []string
	BusinessTypes   []BusinessCategory
}

// GetCoreModules returns the definition of core business modules
func (s *ModulePrioritizationService) GetCoreModules() []CoreModuleDefinition {
	return []CoreModuleDefinition{
		{
			Name:         "Customer Management",
			Category:     "core",
			BasePriority: 3,
			Keywords:     []string{"customer", "client", "contact", "crm"},
			BusinessTypes: []BusinessCategory{FieldService, AppointmentBased, RetailCommerce, ProfessionalServices, FoodService, HealthcareServices, Generic},
		},
		{
			Name:         "Invoicing & Billing",
			Category:     "core", 
			BasePriority: 3,
			Keywords:     []string{"invoice", "billing", "payment", "charge"},
			BusinessTypes: []BusinessCategory{FieldService, AppointmentBased, RetailCommerce, ProfessionalServices, FoodService, HealthcareServices, Generic},
		},
		{
			Name:         "Scheduling",
			Category:     "core",
			BasePriority: 3,
			Keywords:     []string{"schedule", "calendar", "appointment", "booking"},
			BusinessTypes: []BusinessCategory{FieldService, AppointmentBased, ProfessionalServices, HealthcareServices},
		},
		{
			Name:         "Payment Processing",
			Category:     "core",
			BasePriority: 2,
			Keywords:     []string{"payment", "transaction", "checkout", "pay"},
			BusinessTypes: []BusinessCategory{RetailCommerce, FoodService, AppointmentBased, ProfessionalServices, HealthcareServices},
		},
		{
			Name:         "Inventory Management",
			Category:     "efficiency",
			BasePriority: 2,
			Keywords:     []string{"inventory", "stock", "product", "supply"},
			BusinessTypes: []BusinessCategory{RetailCommerce, FoodService, FieldService},
		},
		{
			Name:         "Team Management",
			Category:     "efficiency",
			BasePriority: 2,
			Keywords:     []string{"team", "staff", "employee", "worker"},
			BusinessTypes: []BusinessCategory{FieldService, AppointmentBased, RetailCommerce, ProfessionalServices, FoodService, HealthcareServices},
		},
	}
}

// CalculateFinalScore computes the final priority score for a module
func (s *ModulePrioritizationService) CalculateFinalScore(module models.RecommendedModule, businessType BusinessCategory) int {
	// Start with AI recommendation score or default to 5
	baseScore := 5
	if module.RecommendationScore != nil {
		baseScore = *module.RecommendationScore
	}

	// Get core module boost
	coreBoost := s.getCoreModuleBoost(module.Name, module.Category, businessType)
	
	// Get business-type specific boost
	businessBoost := s.getBusinessTypeBoost(module.Name, businessType)
	
	// Calculate final score
	finalScore := baseScore + coreBoost + businessBoost
	
	// Cap at 10
	if finalScore > 10 {
		finalScore = 10
	}
	
	// Ensure minimum of 1
	if finalScore < 1 {
		finalScore = 1
	}

	s.logger.Debug("Module score calculation",
		"module", module.Name,
		"base_score", baseScore,
		"core_boost", coreBoost,
		"business_boost", businessBoost,
		"final_score", finalScore,
		"business_type", businessType)

	return finalScore
}

// getCoreModuleBoost returns priority boost for core business modules
func (s *ModulePrioritizationService) getCoreModuleBoost(moduleName, category string, businessType BusinessCategory) int {
	coreModules := s.GetCoreModules()
	
	// Check for exact name match first
	for _, coreModule := range coreModules {
		if s.isModuleMatch(moduleName, coreModule.Keywords) {
			// Check if this core module applies to the business type
			for _, supportedType := range coreModule.BusinessTypes {
				if supportedType == businessType || supportedType == Generic {
					return coreModule.BasePriority
				}
			}
		}
	}
	
	// Category-based boost for core operations
	if category == "core" {
		return 1
	}
	
	return 0
}

// getBusinessTypeBoost returns additional boost based on business type relevance
func (s *ModulePrioritizationService) getBusinessTypeBoost(moduleName string, businessType BusinessCategory) int {
	businessSpecificBoosts := map[BusinessCategory]map[string]int{
		FieldService: {
			"route": 2, "mobile": 2, "gps": 1, "equipment": 1,
		},
		AppointmentBased: {
			"booking": 2, "reminder": 1, "calendar": 2, "availability": 1,
		},
		RetailCommerce: {
			"pos": 2, "inventory": 2, "supplier": 1, "loyalty": 1,
		},
		ProfessionalServices: {
			"project": 2, "time": 2, "document": 1, "portal": 1,
		},
		FoodService: {
			"menu": 2, "order": 2, "kitchen": 1, "table": 1,
		},
		HealthcareServices: {
			"patient": 2, "medical": 2, "hipaa": 1, "record": 1,
		},
	}

	if boosts, exists := businessSpecificBoosts[businessType]; exists {
		moduleNameLower := strings.ToLower(moduleName)
		for keyword, boost := range boosts {
			if strings.Contains(moduleNameLower, keyword) {
				return boost
			}
		}
	}

	return 0
}

// isModuleMatch checks if a module name matches any of the given keywords
func (s *ModulePrioritizationService) isModuleMatch(moduleName string, keywords []string) bool {
	moduleNameLower := strings.ToLower(moduleName)
	for _, keyword := range keywords {
		if strings.Contains(moduleNameLower, strings.ToLower(keyword)) {
			return true
		}
	}
	return false
}

// ClassifyBusinessType attempts to classify business type from description
func (s *ModulePrioritizationService) ClassifyBusinessType(businessDescription string) BusinessCategory {
	description := strings.ToLower(businessDescription)
	
	// Field service indicators
	fieldServiceKeywords := []string{"service", "repair", "maintenance", "cleaning", "landscaping", "hvac", "plumbing", "electrical"}
	for _, keyword := range fieldServiceKeywords {
		if strings.Contains(description, keyword) {
			return FieldService
		}
	}
	
	// Appointment-based indicators
	appointmentKeywords := []string{"appointment", "booking", "salon", "spa", "clinic", "therapy", "consultation"}
	for _, keyword := range appointmentKeywords {
		if strings.Contains(description, keyword) {
			return AppointmentBased
		}
	}
	
	// Retail/commerce indicators
	retailKeywords := []string{"retail", "store", "shop", "sell", "product", "merchandise", "boutique"}
	for _, keyword := range retailKeywords {
		if strings.Contains(description, keyword) {
			return RetailCommerce
		}
	}
	
	// Professional services indicators
	professionalKeywords := []string{"consulting", "agency", "law", "accounting", "marketing", "design", "development"}
	for _, keyword := range professionalKeywords {
		if strings.Contains(description, keyword) {
			return ProfessionalServices
		}
	}
	
	// Food service indicators
	foodKeywords := []string{"restaurant", "cafe", "bakery", "catering", "food", "kitchen", "dining"}
	for _, keyword := range foodKeywords {
		if strings.Contains(description, keyword) {
			return FoodService
		}
	}
	
	// Healthcare indicators
	healthcareKeywords := []string{"medical", "health", "doctor", "nurse", "patient", "clinic", "hospital"}
	for _, keyword := range healthcareKeywords {
		if strings.Contains(description, keyword) {
			return HealthcareServices
		}
	}
	
	return Generic
}

// GetCategoryPriority returns priority order for module categories
func (s *ModulePrioritizationService) GetCategoryPriority(category string) int {
	priorities := map[string]int{
		"core":       1,
		"efficiency": 2,
		"growth":     3,
		"compliance": 4,
		"analytics":  5,
		"financial":  6,
	}
	
	if priority, exists := priorities[category]; exists {
		return priority
	}
	
	return 7 // Default for unknown categories
}
