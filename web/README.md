# Vertoie Landing Page

A simple, clean Fiber-based landing page for Vertoie that displays pricing plans and features.

## Features

- 🎨 Modern, responsive design with Vertoie brand colors
- 🌙 Light/dark mode toggle (follows system preference by default)
- 📊 Dynamic pricing display from database
- 🔒 Secure, read-only API endpoints
- ⚡ Fast Go Fiber backend
- 📱 Mobile-friendly responsive design

## Getting Started

### Prerequisites

- Go 1.23 or later
- PostgreSQL database (same as platform backend)

### Configuration

Copy `.env.example` to `.env` and configure:

```bash
DATABASE_URL=postgres://vertoie:vertoie@localhost:5433/vertoie?sslmode=disable
PORT=8001
```

### Running

```bash
# Install dependencies
go mod download

# Run the server
go run main.go
```

The landing page will be available at `http://localhost:8001`

## Security Features

- API endpoints (`/api/plans`, `/api/addons`) are protected and only accessible by the website itself
- Read-only database queries with minimal data exposure
- Secure headers and CORS configuration
- No write operations to the database

## Project Structure

```
web/
├── main.go                 # Application entry point
├── internal/
│   ├── config/            # Configuration management
│   ├── database/          # Database connection
│   ├── handlers/          # HTTP handlers
│   └── models/            # Data models
├── static/
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript
│   └── images/            # Static assets
└── templates/
    └── index.html         # Main page template
```

## Development Notes

- The app connects to the same database as the platform backend
- Plans and addons are read from the `vertoie.plans` and `vertoie.plan_addons` tables
- Brand colors and design elements match the Vertoie brand guidelines
- All API calls include security headers to prevent external access
