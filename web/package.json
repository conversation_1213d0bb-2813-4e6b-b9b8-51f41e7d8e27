{"name": "web", "version": "1.0.0", "description": "A simple, clean Fiber-based landing page for Vertoie that displays pricing plans and features.", "main": "index.js", "dependencies": {"browserslist": "^4.25.0", "caniuse-lite": "^1.0.30001724", "cssesc": "^3.0.0", "electron-to-chromium": "^1.5.171", "escalade": "^3.2.0", "fraction.js": "^4.3.7", "lodash.castarray": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.merge": "^4.6.2", "mini-svg-data-uri": "^1.4.4", "nanoid": "^3.3.11", "node-releases": "^2.0.19", "normalize-range": "^0.1.2", "picocolors": "^1.1.1", "postcss-selector-parser": "^6.0.10", "postcss-value-parser": "^4.2.0", "source-map-js": "^1.2.1", "update-browserslist-db": "^1.1.3", "util-deprecate": "^1.0.2"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.10"}, "scripts": {"build:css": "tailwindcss -i static/css/input.css -o static/css/tailwind.css", "watch:css": "tailwindcss -i static/css/input.css -o static/css/tailwind.css --watch", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC"}