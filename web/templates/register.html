<!-- Auth Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1>Create your account</h1>
            <p>Start building your business platform with Vertoie</p>
            
            <div id="messages"></div>

            <!-- Magic Link Info -->
            <div class="magic-link-info">
                <div class="magic-link-icon">🔐</div>
                <div class="magic-link-content">
                    <h3>Secure Magic Link</h3>
                    <p>We'll send you a secure magic link to verify your email. No passwords needed.</p>
                </div>
            </div>

            <!-- Registration Form -->
            <form id="register-form" 
                  hx-post="/auth/register" 
                  hx-target="#messages" 
                  hx-swap="innerHTML"
                  hx-indicator="#register-submit-btn"
                  hx-disabled-elt="#register-submit-btn"
                  hx-on::after-request="if(event.detail.xhr.status === 200 && event.detail.xhr.responseText.includes('Account Created!')) { this.reset(); document.getElementById('first_name').focus(); }">
                <div id="messages"></div>
                <div class="form-group">
                    <label for="first_name" class="form-label">First Name</label>
                    <input type="text" id="first_name" name="first_name" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label for="last_name" class="form-label">Last Name</label>
                    <input type="text" id="last_name" name="last_name" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" id="email" name="email" class="form-input" required>
                </div>

                <button type="submit" class="btn btn-primary w-full" id="register-submit-btn">
                    <span class="htmx-indicator">
                        <div class="spinner" style="display: inline-block; width: 1.2rem; height: 1.2rem; margin-right: 0.5rem; vertical-align: middle;"></div>
                        Creating Account...
                    </span>
                    <span class="default-text">Create Account</span>
                </button>
            </form>

            <div class="text-center-margin-top">
                <p>Already have an account? <a href="/login">Sign in</a></p>
                <p class="text-small-muted">
                    By creating an account, you agree to our 
                    <a href="/terms">Terms of Service</a> and 
                    <a href="/privacy">Privacy Policy</a>
                </p>
            </div>
        </div>
        
        <div class="hero-visual" id="hero-visual">
            <div class="mockup-browser">
                <div class="mockup-header">
                    <div class="mockup-dots">
                        <span></span><span></span><span></span>
                    </div>
                </div>
                <div class="mockup-content">
                    <div class="mockup-nav">
                        <div class="mockup-nav-item active">Dashboard</div>
                        <div class="mockup-nav-item">Organizations</div>
                        <div class="mockup-nav-item">Settings</div>
                    </div>
                    <div class="mockup-cards">
                        <div class="mockup-card">
                            <div class="mockup-card-header"></div>
                            <div class="mockup-card-content"></div>
                            <div class="mockup-card-content"></div>
                        </div>
                        <div class="mockup-card">
                            <div class="mockup-card-header mockup-card-header-40"></div>
                            <div class="mockup-card-content"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section> 