<!-- Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="page-title-section">
            <h1 class="page-title">Dashboard</h1>
            <p class="page-subtitle">Welcome back, {{.User.FirstName}}! Here's what's happening today.</p>
        </div>
        <div class="page-actions">
            <button class="btn btn-primary" hx-get="/api/tasks/new" hx-target="#modal-content">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                New Task
            </button>
        </div>
    </div>
</div>

<!-- Stats Overview -->
<div class="stats-overview">
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22,4 12,14.01 9,11.01"></polyline>
                    </svg>
                </div>
                <div class="stat-trend positive">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="18,15 12,9 6,15"></polyline>
                    </svg>
                    <span>+12%</span>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-number">156</div>
                <div class="stat-label">Total Tasks</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M12 6v6l4 2"></path>
                    </svg>
                </div>
                <div class="stat-trend negative">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="6,9 12,15 18,9"></polyline>
                    </svg>
                    <span>-3%</span>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-number">24</div>
                <div class="stat-label">Due Today</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                    </svg>
                </div>
                <div class="stat-trend positive">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="18,15 12,9 6,15"></polyline>
                    </svg>
                    <span>+8%</span>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-number">89%</div>
                <div class="stat-label">Completion Rate</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                </div>
                <div class="stat-trend positive">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="18,15 12,9 6,15"></polyline>
                    </svg>
                    <span>+5%</span>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-number">12</div>
                <div class="stat-label">Team Members</div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="dashboard-grid">
    <!-- Recent Activity -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3 class="card-title">Recent Activity</h3>
            <button class="card-action">View All</button>
        </div>
        <div class="card-content">
            <div class="activity-list">
                <div class="activity-item">
                    <div class="activity-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></svg>
                        </svg>
                    </div>
                    <div class="activity-content">
                        <p class="activity-text"><strong>Sarah Johnson</strong> completed task "Update homepage design"</p>
                        <span class="activity-time">2 minutes ago</span>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                        </svg>
                    </div>
                    <div class="activity-content">
                        <p class="activity-text"><strong>Mike Chen</strong> created new project "Mobile App Redesign"</p>
                        <span class="activity-time">15 minutes ago</span>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                        </svg>
                    </div>
                    <div class="activity-content">
                        <p class="activity-text"><strong>Emily Davis</strong> joined the team</p>
                        <span class="activity-time">1 hour ago</span>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 6v6l4 2"></path>
                        </svg>
                    </div>
                    <div class="activity-content">
                        <p class="activity-text"><strong>Project deadline</strong> approaching for "E-commerce Platform"</p>
                        <span class="activity-time">2 hours ago</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3 class="card-title">Quick Actions</h3>
        </div>
        <div class="card-content">
            <div class="quick-actions-grid">
                <button class="quick-action-btn" hx-get="/api/tasks/new" hx-target="#modal-content">
                    <div class="action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                    </div>
                    <span>Create Task</span>
                </button>

                <button class="quick-action-btn" hx-get="/api/projects/new" hx-target="#modal-content">
                    <div class="action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                        </svg>
                    </div>
                    <span>New Project</span>
                </button>

                <button class="quick-action-btn" hx-get="/api/analytics" hx-target="#modal-content">
                    <div class="action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="20" x2="18" y2="10"></line>
                            <line x1="12" y1="20" x2="12" y2="4"></line>
                            <line x1="6" y1="20" x2="6" y2="14"></line>
                        </svg>
                    </div>
                    <span>View Reports</span>
                </button>

                <button class="quick-action-btn" hx-get="/api/team/invite" hx-target="#modal-content">
                    <div class="action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="8.5" cy="7" r="4"></circle>
                            <line x1="20" y1="8" x2="20" y2="14"></line>
                            <line x1="23" y1="11" x2="17" y2="11"></line>
                        </svg>
                    </div>
                    <span>Invite Member</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Recent Projects -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3 class="card-title">Recent Projects</h3>
            <button class="card-action">View All</button>
        </div>
        <div class="card-content">
            <div class="project-list">
                <div class="project-item">
                    <div class="project-info">
                        <h4 class="project-name">E-commerce Platform</h4>
                        <p class="project-description">Modern online store with payment integration</p>
                        <div class="project-meta">
                            <span class="project-status in-progress">In Progress</span>
                            <span class="project-deadline">Due Dec 15</span>
                        </div>
                    </div>
                    <div class="project-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%"></div>
                        </div>
                        <span class="progress-text">75%</span>
                    </div>
                </div>

                <div class="project-item">
                    <div class="project-info">
                        <h4 class="project-name">Mobile App Redesign</h4>
                        <p class="project-description">UI/UX overhaul for iOS and Android apps</p>
                        <div class="project-meta">
                            <span class="project-status planning">Planning</span>
                            <span class="project-deadline">Due Jan 20</span>
                        </div>
                    </div>
                    <div class="project-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 25%"></div>
                        </div>
                        <span class="progress-text">25%</span>
                    </div>
                </div>

                <div class="project-item">
                    <div class="project-info">
                        <h4 class="project-name">Marketing Website</h4>
                        <p class="project-description">Company website with lead generation</p>
                        <div class="project-meta">
                            <span class="project-status completed">Completed</span>
                            <span class="project-deadline">Completed Dec 1</span>
                        </div>
                    </div>
                    <div class="project-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%"></div>
                        </div>
                        <span class="progress-text">100%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Team Overview -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3 class="card-title">Team Overview</h3>
            <button class="card-action">Manage Team</button>
        </div>
        <div class="card-content">
            <div class="team-stats">
                <div class="team-stat">
                    <div class="team-stat-number">12</div>
                    <div class="team-stat-label">Total Members</div>
                </div>
                <div class="team-stat">
                    <div class="team-stat-number">8</div>
                    <div class="team-stat-label">Active Today</div>
                </div>
                <div class="team-stat">
                    <div class="team-stat-number">156</div>
                    <div class="team-stat-label">Tasks Completed</div>
                </div>
            </div>
            <div class="team-avatars">
                <div class="avatar-group">
                    <div class="avatar" title="Sarah Johnson">SJ</div>
                    <div class="avatar" title="Mike Chen">MC</div>
                    <div class="avatar" title="Emily Davis">ED</div>
                    <div class="avatar" title="Alex Rodriguez">AR</div>
                    <div class="avatar" title="Lisa Wang">LW</div>
                    <div class="avatar-more">+7</div>
                </div>
            </div>
        </div>
    </div>
</div> 