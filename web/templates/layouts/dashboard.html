<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .Title }} - Vertoie</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/8.0.1/normalize.css">
    <link rel="stylesheet" href="/static/css/vertoie-design-system.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="icon" href="/static/images/favicon.svg" type="image/svg+xml">
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script>
        // Theme detection and initialization
        (function() {
            const theme = localStorage.getItem('theme') || 
                         (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
            document.documentElement.setAttribute('data-theme', theme);
        })();
        
        // Sidebar state initialization (before Alpine loads)
        (function() {
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                document.documentElement.classList.add('sidebar-collapsed-init');
            }
        })();
    </script>
</head>
<body class="dashboard-body flex-column" x-data="{ sidebarOpen: false }">
    <!-- Shared Header -->
    {{template "partials/app-header" .}}

    <!-- Dashboard Layout -->
    <div class="dashboard-layout flex-row" :class="{ 'sidebar-collapsed': $store.sidebar.collapsed }">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar" :class="{ 'open': sidebarOpen, 'collapsed': $store.sidebar.collapsed }">
            <nav class="sidebar-nav">
                <div class="sidebar-section">
                    <h3 class="sidebar-title">Main</h3>
                    <ul>
                        <li>
                            <a href="/dashboard" class="nav-item {{if eq .Title "Dashboard"}}active{{end}}" 
                               @mouseenter="$store.sidebar.collapsed && $store.tooltip.show($event, 'Dashboard')"
                               @mouseleave="$store.tooltip.hide()">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="3" y="3" width="7" height="7"></rect>
                                    <rect x="14" y="3" width="7" height="7"></rect>
                                    <rect x="14" y="14" width="7" height="7"></rect>
                                    <rect x="3" y="14" width="7" height="7"></rect>
                                </svg>
                                <span class="nav-text">Dashboard</span>
                            </a>
                        </li>

                    </ul>
                </div>

                <div class="sidebar-section">
                    <h3 class="sidebar-title">Analytics</h3>
                    <ul>
                        <li>
                            <a href="/analytics" class="nav-item {{if eq .Title "Analytics"}}active{{end}}"
                               @mouseenter="$store.sidebar.collapsed && $store.tooltip.show($event, 'Reports')"
                               @mouseleave="$store.tooltip.hide()">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="18" y1="20" x2="18" y2="10"></line>
                                    <line x1="12" y1="20" x2="12" y2="4"></line>
                                    <line x1="6" y1="20" x2="6" y2="14"></line>
                                </svg>
                                <span class="nav-text">Reports</span>
                            </a>
                        </li>
                        <li>
                            <a href="/insights" class="nav-item {{if eq .Title "Insights"}}active{{end}}"
                               @mouseenter="$store.sidebar.collapsed && $store.tooltip.show($event, 'Insights')"
                               @mouseleave="$store.tooltip.hide()">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"></circle>
                                    <path d="m21 21-4.35-4.35"></path>
                                </svg>
                                <span class="nav-text">Insights</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="sidebar-section">
                    <h3 class="sidebar-title">Settings</h3>
                    <ul>
                        <li>
                            <a href="/profile" class="nav-item {{if eq .Title "Profile"}}active{{end}}"
                               @mouseenter="$store.sidebar.collapsed && $store.tooltip.show($event, 'Profile')"
                               @mouseleave="$store.tooltip.hide()">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                                <span class="nav-text">Profile</span>
                            </a>
                        </li>
                        <li>
                            <a href="/settings" class="nav-item {{if eq .Title "Settings"}}active{{end}}"
                               @mouseenter="$store.sidebar.collapsed && $store.tooltip.show($event, 'Settings')"
                               @mouseleave="$store.tooltip.hide()">
                                <svg class="nav-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="3"></circle>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                </svg>
                                <span class="nav-text">Settings</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Sidebar Footer with Collapse Button -->
            <div class="sidebar-footer">
                <button class="sidebar-collapse-btn" @click="$store.sidebar.toggle()"
                        @mouseenter="$store.tooltip.show($event, $store.sidebar.collapsed ? 'Expand sidebar' : 'Collapse sidebar')"
                        @mouseleave="$store.tooltip.hide()">
                    <svg class="collapse-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M11 19l-7-7 7-7"></path>
                        <path d="M21 19l-7-7 7-7"></path>
                    </svg>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="dashboard-main">
            <div class="main-content">
                {{embed}}
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" x-show="sidebarOpen" @click="sidebarOpen = false"></div>

    <!-- Custom Tooltip -->
    <div class="tooltip" 
         x-show="$store.tooltip.visible" 
         x-text="$store.tooltip.text"
         :class="{ 'show': $store.tooltip.visible }"
         :style="{ left: $store.tooltip.x + 'px', top: $store.tooltip.y + 'px' }"></div>

    <!-- Hidden div for logout responses -->
    <div id="logout-response" style="display: none;"></div>

    <!-- Theme toggle script -->
    <script>
        // Theme toggle functionality
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', function() {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
            });
        }
        
        // Sidebar collapse state persistence
        document.addEventListener('alpine:init', () => {
            // Initialize sidebar state from localStorage
            const savedState = localStorage.getItem('sidebarCollapsed') === 'true';
            
            Alpine.store('sidebar', {
                collapsed: savedState,
                toggle() {
                    this.collapsed = !this.collapsed;
                    localStorage.setItem('sidebarCollapsed', this.collapsed.toString());
                }
            });
            
            // Remove initial class once Alpine takes over
            document.documentElement.classList.remove('sidebar-collapsed-init');
            
            Alpine.store('tooltip', {
                visible: false,
                text: '',
                x: 0,
                y: 0,
                show(event, text) {
                    const rect = event.target.getBoundingClientRect();
                    this.text = text;
                    this.x = rect.right + 12;
                    this.y = rect.top + (rect.height / 2);
                    this.visible = true;
                },
                hide() {
                    this.visible = false;
                }
            });
        });
    </script>

    {{ if .ExtraScripts }}
        {{ range .ExtraScripts }}
            <script src="{{.}}"></script>
        {{ end }}
    {{ end }}
</body>
</html> 