<!-- Organization Management Header -->
<div class="welcome-section">
    <div class="organization-header">
        <div class="organization-icon">🏢</div>
        <div class="organization-info">
            <h1>{{.Organization.Name}}</h1>
            <p>Build your custom business software</p>
        </div>
    </div>
</div>

<!-- 2-Panel Layout Container -->
<div class="two-panel-layout">
    <!-- Chat Panel -->
    <div class="panel chat-panel">
        <div class="panel-header">
            <h2>💬 AI Assistant</h2>
            <p>Tell me about your business to get personalized recommendations</p>
        </div>
        
        <!-- Thread Management Section -->
        <div class="thread-management">
            <!-- Thread Switcher -->
            <div hx-get="/api/threads/{{.Organization.ID}}" 
                 hx-trigger="load" 
                 hx-swap="innerHTML">
                <!-- Thread switcher will load here -->
            </div>
            
            <!-- Thread Progress -->
            <div id="thread-progress-container">
                <!-- Thread progress will load here -->
            </div>
        </div>
        
        <div class="chat-container">
            <div class="chat-messages" id="chat-messages">
                <div class="message ai-message">
                    <div class="message-content">
                        <p>Hi! I'm here to help you build custom software for your business. I'll analyze your needs and recommend the perfect modules and data models.</p>
                        <p>Let's start with the basics. What industry is your business in?</p>
                    </div>
                </div>
            </div>
            
            <form class="chat-input" 
                  hx-post="/api/business-context/chat-htmx"
                  hx-target="#chat-messages"
                  hx-swap="beforeend"
                  hx-on::before-request="addUserMessage(event)"
                  x-data="{ sending: false }"
                  @htmx:before-request="sending = true"
                  @htmx:after-request="sending = false; document.querySelector('textarea[name=message]').value = ''">
                <input type="hidden" name="organization_id" value="{{.Organization.ID}}">
                <textarea name="message" placeholder="Tell me about your business..." rows="3" required></textarea>
                <button type="submit" class="btn btn-primary" :disabled="sending">
                    <span x-show="!sending">📤</span>
                    <span x-show="sending">⏳</span>
                    <span x-text="sending ? 'Sending...' : 'Send'">Send</span>
                </button>
            </form>
        </div>
    </div>

    <!-- Modules Panel -->
    <div class="panel modules-panel">
        <div class="panel-header">
            <h2>🔧 Recommended Modules</h2>
            <p>AI-recommended software modules for your business</p>
        </div>
        
        <div class="modules-container" id="modules-container"
             hx-get="/api/modules/{{.Organization.ID}}"
             hx-trigger="load"
             hx-swap="innerHTML">
            <div class="empty-state">
                <div class="empty-icon">🔧</div>
                <h3>Loading modules...</h3>
                <p>Please wait while we load your recommendations</p>
            </div>
        </div>
    </div>
</div>

<style>
/* 2-Panel Layout Styles */
.two-panel-layout {
    display: grid;
    grid-template-columns: 60% 40%;
    gap: 2rem;
    height: calc(100vh - 200px);
    min-height: 600px;
}

.panel {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    padding: 2rem;
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-secondary);
}

.panel-header h2 {
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.panel-header p {
    color: var(--text-secondary);
    font-size: 1.4rem;
    margin: 0;
}

/* Chat Panel Styles */
.chat-panel {
    display: flex;
    flex-direction: column;
}

/* Thread Management Styles */
.thread-management {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-primary);
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.message {
    display: flex;
    max-width: 100%;
}

.ai-message {
    justify-content: flex-start;
}

.user-message {
    justify-content: flex-end;
}

.message-content {
    max-width: 85%;
    padding: 1.2rem 1.6rem;
    border-radius: 12px;
    font-size: 1.4rem;
    line-height: 1.6;
    word-wrap: break-word;
}

.ai-message .message-content {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    color: var(--text-primary);
}

.user-message .message-content {
    background: var(--vertoie-orange);
    color: white;
}

.chat-input {
    padding: 2rem;
    border-top: 1px solid var(--border-light);
    background: var(--bg-primary);
    display: flex;
    gap: 1rem;
    align-items: end;
}

.chat-input textarea {
    flex: 1;
    padding: 1.2rem;
    border: 1px solid var(--border-light);
    border-radius: 8px;
    resize: none;
    font-family: inherit;
    font-size: 1.4rem;
    min-height: 80px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    margin-bottom: 0;
}

.chat-input textarea:focus {
    outline: none;
    border-color: var(--vertoie-orange);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.chat-input button {
    margin-bottom: 0;
    white-space: nowrap;
}

/* Modules Panel Styles */
.modules-container {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
}

.module-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.2s ease;
}

.module-card:hover {
    border-color: var(--vertoie-orange);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.module-name {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.module-score {
    background: var(--vertoie-orange);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 1.3rem;
    font-weight: 600;
}

.module-category {
    display: inline-block;
    padding: 0.4rem 1rem;
    border-radius: 8px;
    font-size: 1.3rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.module-category.core { background: #e3f2fd; color: #1976d2; }
.module-category.efficiency { background: #e8f5e8; color: #388e3c; }
.module-category.growth { background: #fff3e0; color: #f57c00; }
.module-category.compliance { background: #fce4ec; color: #c2185b; }
.module-category.analytics { background: #f3e5f5; color: #7b1fa2; }
.module-category.financial { background: #e0f2f1; color: #00796b; }

.module-description {
    color: var(--text-secondary);
    font-size: 1.5rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.module-benefit {
    background: var(--bg-primary);
    border-left: 4px solid var(--vertoie-orange);
    padding: 1.5rem;
    border-radius: 0 8px 8px 0;
    margin-bottom: 1.5rem;
}

.module-benefit-label {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.module-benefit-text {
    font-size: 1.4rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
}

.module-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.module-action-btn {
    padding: 0.8rem 1.5rem;
    border: 1px solid var(--border-light);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1.3rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.module-action-btn:hover {
    background: var(--bg-secondary);
    border-color: var(--vertoie-orange);
}

.module-action-btn.accept {
    background: var(--vertoie-orange);
    color: white;
    border-color: var(--vertoie-orange);
}

.module-action-btn.accept:hover {
    background: #e65a00;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 6rem 2rem;
    color: var(--text-secondary);
}

/* Module Status Styles */
.module-status {
    margin-bottom: 1.5rem;
}

.status-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.status-badge.accepted {
    background: #e8f5e8;
    color: #388e3c;
}

.status-badge.rejected {
    background: #ffebee;
    color: #d32f2f;
}

.status-badge.custom {
    background: #f3e5f5;
    color: #7b1fa2;
}

.user-feedback {
    font-size: 1.4rem;
    color: var(--text-secondary);
    font-style: italic;
    margin: 0;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 6px;
}



.empty-state .empty-icon {
    font-size: 8rem;
    margin-bottom: 2rem;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 2.4rem;
    margin: 0 0 1rem 0;
    color: var(--text-primary);
}

.empty-state p {
    font-size: 1.6rem;
    margin: 0;
    line-height: 1.5;
}

/* Typing Indicator Animation */
.typing-indicator {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 0.5rem;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--text-secondary);
    animation: typing-pulse 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: 0s;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing-pulse {
    0%, 60%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    30% {
        opacity: 1;
        transform: scale(1.2);
    }
}

/* Discovery Questions */
.discovery-questions {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.discovery-questions h4 {
    color: var(--text-primary);
    font-size: 1.6rem;
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.discovery-questions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.discovery-question {
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-light);
}

.discovery-question:last-child {
    border-bottom: none;
}

.discovery-question strong {
    color: var(--text-primary);
    font-size: 1.5rem;
    display: block;
    margin-bottom: 0.5rem;
}

.question-explanation {
    color: var(--text-secondary);
    font-size: 1.3rem;
    margin: 0.5rem 0 0 0;
    font-style: italic;
}

/* Buttons */
.button {
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1.2rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1.4rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.button-primary {
    background: var(--vertoie-orange);
    color: white;
}

.button-primary:hover {
    background: #e65a00;
}

.button-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-light);
}

.button-secondary:hover {
    background: var(--border-light);
}

.button-icon {
    font-size: 1.6rem;
}

/* Organization Header */
.organization-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 3rem;
}

.organization-icon {
    font-size: 4rem;
    width: 6rem;
    height: 6rem;
    background: var(--vertoie-orange);
    color: white;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.organization-info h1 {
    color: var(--text-primary);
    font-size: 3.2rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
}

.organization-info p {
    color: var(--text-secondary);
    font-size: 1.6rem;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .two-panel-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto;
        height: auto;
        gap: 1.5rem;
    }
    
    .panel {
        min-height: 500px;
    }
}

@media (max-width: 768px) {
    .two-panel-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto;
        height: auto;
        gap: 1.5rem;
    }
    
    .panel {
        min-height: 400px;
    }
    
    .organization-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .chat-input {
        flex-direction: column;
    }
    
    .module-actions {
        flex-direction: column;
    }
    
    .module-action-btn {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .panel-header {
        padding: 1.5rem;
    }
    
    .panel-header h2 {
        font-size: 1.8rem;
    }
    
    .chat-messages,
    .modules-container {
        padding: 1.5rem;
    }
    
    .organization-info h1 {
        font-size: 2.4rem;
    }
    
    .module-card {
        padding: 1.5rem;
    }
    
    .module-name {
        font-size: 1.6rem;
    }
}
</style>

<!-- Alpine.js for interactive components -->
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

<script>
// HTMX Chat functionality
function addUserMessage(event) {
    const form = event.target;
    const formData = new FormData(form);
    const message = formData.get('message');
    
    if (!message.trim()) {
        event.preventDefault();
        return;
    }
    
    // Add user message to chat immediately
    const messagesContainer = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message user-message';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = `<p>${message}</p>`;
    
    messageDiv.appendChild(contentDiv);
    messagesContainer.appendChild(messageDiv);
    
    // Add loading indicator
    addLoadingMessage();
    
    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function addLoadingMessage() {
    // Remove any existing loading message first
    removeLoadingMessage();
    
    const messagesContainer = document.getElementById('chat-messages');
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'message ai-message';
    loadingDiv.id = 'loading-message';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = `
        <div class="typing-indicator">
            <span class="typing-dot"></span>
            <span class="typing-dot"></span>
            <span class="typing-dot"></span>
        </div>
        <p>AI is analyzing your business...</p>
    `;
    
    loadingDiv.appendChild(contentDiv);
    messagesContainer.appendChild(loadingDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function removeLoadingMessage() {
    const loadingMessage = document.getElementById('loading-message');
    if (loadingMessage) {
        loadingMessage.remove();
    }
}

// HTMX event listeners
document.addEventListener('htmx:afterRequest', function(event) {
    // Remove loading message after HTMX request completes
    removeLoadingMessage();
    
    // Scroll to bottom
    const messagesContainer = document.getElementById('chat-messages');
    if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
});

function updateModules(modules) {
    const container = document.getElementById('modules-container');
    
    if (!modules || modules.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">🔧</div>
                <h3>No modules recommended</h3>
                <p>Continue chatting to get personalized recommendations</p>
            </div>
        `;
        return;
    }
    
    // Sort modules by recommendation_score descending (highest first)
    const sortedModules = modules.sort((a, b) => {
        const scoreA = parseFloat(a.recommendation_score) || 0;
        const scoreB = parseFloat(b.recommendation_score) || 0;
        return scoreB - scoreA;
    });
    
    const modulesHTML = sortedModules.map(module => `
        <div class="module-card">
            <div class="module-header">
                <h3 class="module-name">${module.name}</h3>
                <span class="module-score">${module.recommendation_score || 'N/A'}</span>
            </div>
            <span class="module-category ${module.category}">${module.category}</span>
            <p class="module-description">${module.description}</p>
            <div class="module-benefit">
                <div class="module-benefit-label">Key Benefit:</div>
                <p class="module-benefit-text">${module.key_benefit}</p>
            </div>
            <div class="module-actions">
                <button class="module-action-btn accept" onclick="acceptModule('${module.name}')">
                    ✅ Accept Module
                </button>
                <button class="module-action-btn" onclick="rejectModule('${module.name}')">
                    ❌ Reject
                </button>
                <button class="module-action-btn" onclick="modifyModule('${module.name}')">
                    ✏️ Modify
                </button>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = modulesHTML;
}

function displayDiscoveryQuestions(questions) {
    if (!questions || questions.length === 0) return;
    
    const messagesContainer = document.getElementById('chat-messages');
    const questionsDiv = document.createElement('div');
    questionsDiv.className = 'message ai-message';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    let questionsHTML = '<div class="discovery-questions"><h4>💡 Let me ask you some questions to better understand your needs:</h4><ul>';
    
    questions.forEach((q, index) => {
        const question = q.question || '';
        const why = q.why_asking || '';
        questionsHTML += `
            <li class="discovery-question">
                <strong>Q${index + 1}: ${question}</strong>
                ${why ? `<p class="question-explanation"><em>This helps me ${why.toLowerCase()}</em></p>` : ''}
            </li>
        `;
    });
    
    questionsHTML += '</ul></div>';
    contentDiv.innerHTML = questionsHTML;
    
    questionsDiv.appendChild(contentDiv);
    messagesContainer.appendChild(questionsDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Module action functions
function acceptModule(moduleId, moduleName) {
    if (confirm(`Accept the "${moduleName}" module?`)) {
        updateModuleStatus(moduleId, 'accepted', `User accepted the ${moduleName} module`);
    }
}

function rejectModule(moduleId, moduleName) {
    const feedback = prompt(`Why are you rejecting the "${moduleName}" module? (optional)`);
    if (feedback !== null) { // User didn't cancel
        updateModuleStatus(moduleId, 'rejected', feedback || `User rejected the ${moduleName} module`);
    }
}

function modifyModule(moduleId, moduleName) {
    const feedback = prompt(`How would you like to modify the "${moduleName}" module?`);
    if (feedback !== null && feedback.trim() !== '') {
        updateModuleStatus(moduleId, 'modified', feedback);
    }
}

function updateModuleStatus(moduleId, status, feedback) {
    fetch(`/api/modules/${moduleId}/status`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: status,
            feedback: feedback
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload the modules list
            htmx.trigger('#modules-container', 'load');
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error updating module status:', error);
        alert('Failed to update module status');
    });
}

// Handle Enter key in chat input
document.addEventListener('DOMContentLoaded', function() {
    const input = document.getElementById('chat-input');
    if (input) {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendChatMessage();
            }
        });
    }
});
</script> 