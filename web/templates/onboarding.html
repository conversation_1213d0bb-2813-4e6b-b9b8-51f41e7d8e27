<!-- Onboarding Page -->
<div class="onboarding-container" hx-headers='{"X-Requested-With": "XMLHttpRequest"}'>
    <div id="content">
        <!-- Initial form -->
        <div class="onboarding-initial">
            <div class="onboarding-card">
                <div class="card-header">
                    <h2>Tell us about your business</h2>
                    <p>Describe your business in one sentence so our AI can generate custom software modules tailored to your specific operations. The more specific you are about what you do, the better we can recommend modules that solve your daily challenges and streamline your workflows.</p>
                </div>
                
                <form hx-post="/onboarding/analyze" 
                      hx-target="#content" 
                      hx-swap="innerHTML">
                    <div class="card-body">
                        <input type="text" 
                               name="description" 
                               class="form-input" 
                               placeholder="We're a pool cleaning service..."
                               required>
                        
                        <button type="submit" class="btn btn-primary">
                            Generate My Software
                        </button>
                    </div>
                </form>
                
                <div class="card-footer">
                    <p class="examples-title">Examples:</p>
                    <ul class="examples-list">
                        <li>We're a pool cleaning service with 50+ customers across three cities</li>
                        <li>We run a boutique digital marketing agency specializing in local businesses</li>
                        <li>We're a family-owned restaurant with takeout, delivery, and catering services</li>
                        <li>We operate a mobile auto detailing business serving residential and commercial clients</li>
                        <li>We're a massage therapy clinic with 4 therapists and online booking</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

