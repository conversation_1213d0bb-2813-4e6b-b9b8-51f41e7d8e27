{{define "base"}}
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{block "title" .}}Vertoie{{end}}</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300italic,700,700italic">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/8.0.1/normalize.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/milligram/1.4.1/milligram.min.css">
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="icon" href="/static/images/favicon.svg" type="image/svg+xml">
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script>
        // Theme detection and initialization
        (function() {
            const theme = localStorage.getItem('theme') || 
                         (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
            document.documentElement.setAttribute('data-theme', theme);
        })();
    </script>
    {{block "head" .}}{{end}}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-brand">
                <a href="/" class="nav-link">
                    <img src="/static/images/logo.svg" alt="Vertoie" class="logo">
                </a>
            </div>
            <div class="nav-links">
                {{block "nav_links" .}}{{end}}
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        {{block "content" .}}{{end}}
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <img src="/static/images/logo.svg" alt="Vertoie" class="logo">
                    <p>Building the future of business platforms.</p>
                </div>
                <div class="footer-links">
                    <div class="footer-group">
                        <h4>Product</h4>
                        <a href="/">Features</a>
                        <a href="/">Pricing</a>
                        <a href="/">Security</a>
                    </div>
                    <div class="footer-group">
                        <h4>Company</h4>
                        <a href="/">About</a>
                        <a href="/">Blog</a>
                        <a href="/">Careers</a>
                    </div>
                    <div class="footer-group">
                        <h4>Support</h4>
                        <a href="/">Help Center</a>
                        <a href="/">Contact</a>
                        <a href="/">Status</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Vertoie. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Theme toggle script -->
    <script>
        // Theme toggle functionality
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', function() {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
            });
        }
    </script>

    {{block "scripts" .}}{{end}}
</body>
</html>
{{end}} 