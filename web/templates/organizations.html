<!-- Organizations Header -->
<div class="welcome-section">
    <div class="organizations-header">
        <div class="header-text">
            <h1>Organizations</h1>
            <p>Manage your organizations and teams.</p>
        </div>
        <button class="btn btn-primary" onclick="openCreateOrganizationModal()">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            Create Organization
        </button>
    </div>
</div>

<!-- Organizations Content -->
<div class="organizations-content">
    <!-- Organizations List -->
    <div id="organizations-list" hx-get="/api/organizations" hx-trigger="load">
        <!-- Loading placeholder -->
        <div class="loading-placeholder">
            <div class="loading-spinner"></div>
            <p>Loading organizations...</p>
        </div>
    </div>
</div>

<style>
.organizations-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.header-text h1 {
    margin: 0 0 0.5rem 0;
}

.header-text p {
    margin: 0;
}

.organizations-content {
    width: 100%;
}

.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
    font-size: 1.6rem;
}

.loading-spinner {
    width: 4rem;
    height: 4rem;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--vertoie-orange);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.organizations-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.organization-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 2rem;
    transition: all 0.2s ease;
}

.organization-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--vertoie-orange);
}

.organization-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.organization-icon {
    font-size: 3rem;
    width: 4rem;
    height: 4rem;
    background: var(--vertoie-orange);
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.organization-info h3 {
    color: var(--text-primary);
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
}

.organization-info p {
    color: var(--text-secondary);
    font-size: 1.3rem;
    margin: 0;
}

.organization-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem 0;
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
}

.organization-stats .stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.organization-stats .stat-number {
    font-size: 2.4rem;
    font-weight: 700;
    color: var(--vertoie-orange);
    line-height: 1;
}

.organization-stats .stat-label {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
}

.organization-actions {
    display: flex;
    gap: 1rem;
}

.organization-actions .button {
    flex: 1;
    padding: 1rem 1.5rem;
    font-size: 1.3rem;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.empty-state .empty-icon {
    font-size: 6rem;
    margin-bottom: 2rem;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 2rem;
    margin: 0 0 1rem 0;
    color: var(--text-primary);
}

.empty-state p {
    font-size: 1.6rem;
    margin: 0;
}

/* Tablet: 2 columns */
@media (max-width: 1024px) {
    .organizations-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Mobile: 1 column */
@media (max-width: 768px) {
    .organizations-grid {
        grid-template-columns: 1fr;
    }
    
    .organizations-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .organization-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .organization-actions {
        flex-direction: column;
    }
}
</style>

<script>
function openCreateOrganizationModal() {
    // Fetch the modal HTML and add it to the page
    fetch('/api/organizations/create-modal')
        .then(response => response.text())
        .then(html => {
            // Remove any existing modal
            const existingModal = document.getElementById('create-organization-modal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // Add the new modal to the page
            document.body.insertAdjacentHTML('beforeend', html);
            
            // Focus on the name input
            setTimeout(() => {
                const nameInput = document.getElementById('org-name');
                if (nameInput) {
                    nameInput.focus();
                }
            }, 100);
        })
        .catch(error => {
            console.error('Error loading modal:', error);
            alert('Failed to load create organization form');
        });
}
</script> 