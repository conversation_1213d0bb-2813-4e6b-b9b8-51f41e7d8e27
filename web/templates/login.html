<!-- Auth Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1>Welcome back</h1>
            <p>Sign in to your Vertoie account with a secure magic link</p>
            
            <div id="messages" hx-swap-oob="true"></div>

            <!-- Magic Link Info -->
            <div class="magic-link-info">
                <div class="magic-link-icon">🔐</div>
                <div class="magic-link-content">
                    <h3>Secure Magic Link</h3>
                    <p>We'll send you a secure magic link to your email address. No passwords needed.</p>
                </div>
            </div>

            <!-- Magic Link Form -->
            <form id="login-form" 
                  hx-post="/auth/magic-link" 
                  hx-target="#messages" 
                  hx-swap="innerHTML"
                  hx-indicator="#login-submit-btn"
                  hx-disabled-elt="#login-submit-btn"
                  hx-on::after-request="if(event.detail.xhr.status === 200 && event.detail.xhr.responseText.includes('Magic Link Sent!')) { this.reset(); document.getElementById('email').focus(); }">
                <div class="form-group">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" id="email" name="email" class="form-input" required>
                </div>

                <button type="submit" class="btn btn-primary w-full" id="login-submit-btn">
                    <span class="htmx-indicator">
                        <div class="spinner" style="display: inline-block; width: 1.2rem; height: 1.2rem; margin-right: 0.5rem; vertical-align: middle;"></div>
                        Sending Magic Link...
                    </span>
                    <span class="default-text">Send Magic Link</span>
                </button>
            </form>

            <div class="text-center-margin-top">
                <p>Don't have an account? <a href="/register">Sign up</a></p>
            </div>
        </div>
        
        <div class="hero-visual" id="hero-visual">
            <div class="mockup-browser">
                <div class="mockup-header">
                    <div class="mockup-dots">
                        <span></span><span></span><span></span>
                    </div>
                </div>
                <div class="mockup-content">
                    <div class="mockup-nav">
                        <div class="mockup-nav-item active">Dashboard</div>
                        <div class="mockup-nav-item">Organizations</div>
                        <div class="mockup-nav-item">Settings</div>
                    </div>
                    <div class="mockup-cards">
                        <div class="mockup-card">
                            <div class="mockup-card-header"></div>
                            <div class="mockup-card-content"></div>
                            <div class="mockup-card-content"></div>
                        </div>
                        <div class="mockup-card">
                            <div class="mockup-card-header mockup-card-header-40"></div>
                            <div class="mockup-card-content"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section> 