<!-- Business Confidence Indicator -->
{{if .AdaptationResult}}
<div class="business-confidence {{.AdaptationResult.Level}}">
    {{if eq .AdaptationResult.Level "specific"}}
        <div class="confidence-indicator high">
            <span class="confidence-icon">🎯</span>
            <div class="confidence-content">
                <p class="confidence-text">{{.AdaptationResult.DisplayText}}</p>
                <div class="confidence-details">
                    <span class="confidence-score">{{printf "%.0f" (mul .AdaptationResult.Confidence 100)}}% match</span>
                    <span class="confidence-examples">Examples: {{range $i, $example := .AdaptationResult.Examples}}{{if $i}}, {{end}}{{$example}}{{end}}</span>
                </div>
            </div>
        </div>
    {{else if eq .AdaptationResult.Level "category"}}
        <div class="confidence-indicator medium">
            <span class="confidence-icon">📋</span>
            <div class="confidence-content">
                <p class="confidence-text">{{.AdaptationResult.DisplayText}}</p>
                <div class="confidence-details">
                    <span class="confidence-score">{{printf "%.0f" (mul .AdaptationResult.Confidence 100)}}% confidence</span>
                    <span class="confidence-category">Category: {{.AdaptationResult.Category}}</span>
                </div>
            </div>
        </div>
    {{else}}
        <div class="confidence-indicator low">
            <span class="confidence-icon">💡</span>
            <div class="confidence-content">
                <p class="confidence-text">{{.AdaptationResult.DisplayText}}</p>
                <div class="confidence-details">
                    <span class="confidence-help">Provide more details for better customization</span>
                </div>
            </div>
        </div>
    {{end}}
</div>

<style>
.business-confidence {
    margin: 1rem 0;
    border-radius: 8px;
    overflow: hidden;
}

.confidence-indicator {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    gap: 0.75rem;
}

.confidence-indicator.high {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.confidence-indicator.medium {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.confidence-indicator.low {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

.confidence-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.confidence-content {
    flex: 1;
    min-width: 0;
}

.confidence-text {
    margin: 0 0 0.5rem 0;
    font-weight: 500;
    line-height: 1.4;
}

.confidence-details {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.875rem;
    opacity: 0.9;
}

.confidence-score {
    font-weight: 600;
}

.confidence-examples,
.confidence-category,
.confidence-help {
    font-style: italic;
}

@media (max-width: 640px) {
    .confidence-details {
        flex-direction: column;
        gap: 0.25rem;
    }
}
</style>
{{end}}
