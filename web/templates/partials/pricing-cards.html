<div class="pricing-cards">
    {{range .Plans}}
    <div class="pricing-card {{if eq .Slug "professional"}}pricing-card--featured{{end}}">
        {{if eq .Slug "professional"}}
        <div class="pricing-card__badge">Most Popular</div>
        {{end}}
        
        <div class="pricing-card__header">
            <h3 class="pricing-card__title">{{.Name}}</h3>
            {{if .Description}}
            <p class="pricing-card__description">{{.Description}}</p>
            {{end}}
        </div>
        
        <div class="pricing-card__price">
            <div class="pricing-card__price-main">
                <span class="pricing-card__amount">{{.GetPriceDisplay}}</span>
                {{if and (ne .BillingCycle "custom") (ne .PriceCents 0)}}
                <span class="pricing-card__period">/{{.BillingCycle}}</span>
                {{end}}
            </div>
            {{if eq .PriceCents 0}}
            <div class="pricing-card__price-note">Forever free</div>
            {{end}}
        </div>
        
        <div class="pricing-card__features">
            <ul class="pricing-card__feature-list">
                <li class="pricing-card__feature">
                    <svg class="pricing-card__check" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    {{.GetModulesDisplay}}
                </li>
                <li class="pricing-card__feature">
                    <svg class="pricing-card__check" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    {{.GetUsersDisplay}}
                </li>
                <li class="pricing-card__feature">
                    <svg class="pricing-card__check" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    {{.GetActionsDisplay}}
                </li>
                {{if .SupportsEmail}}
                <li class="pricing-card__feature">
                    <svg class="pricing-card__check" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Email notifications
                </li>
                {{end}}
                {{if .SupportsSMS}}
                <li class="pricing-card__feature">
                    <svg class="pricing-card__check" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    SMS notifications
                </li>
                {{end}}
                {{if .SupportsVoice}}
                <li class="pricing-card__feature">
                    <svg class="pricing-card__check" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Voice commands
                </li>
                {{end}}
                {{if .SupportsIntegrations}}
                <li class="pricing-card__feature">
                    <svg class="pricing-card__check" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Third-party integrations
                </li>
                {{end}}
                {{if .SupportsCustomBranding}}
                <li class="pricing-card__feature">
                    <svg class="pricing-card__check" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Custom branding
                </li>
                {{end}}
                {{if .SupportsCustomDomain}}
                <li class="pricing-card__feature">
                    <svg class="pricing-card__check" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Custom domain
                </li>
                {{end}}
                {{if .SupportsAPIAccess}}
                <li class="pricing-card__feature">
                    <svg class="pricing-card__check" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    API access
                </li>
                {{end}}
                {{if .HasPrioritySupport}}
                <li class="pricing-card__feature">
                    <svg class="pricing-card__check" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Priority support
                </li>
                {{end}}
            </ul>
        </div>
        
        <div class="pricing-card__cta">
            {{if eq .PriceCents 0}}
            <a href="/register" class="btn btn-secondary pricing-card__button">Get Started Free</a>
            {{else}}
            <a href="/register" class="btn {{if eq .Slug "professional"}}btn-primary{{else}}btn-secondary{{end}} pricing-card__button">Register</a>
            {{end}}
        </div>
    </div>
    {{end}}
</div> 