<!-- Thread Progress Bar -->
<div class="thread-progress" id="thread-progress">
    <div class="thread-info">
        <div class="thread-title">{{.Title}}</div>
        <div class="thread-usage">{{.UsagePercentage}}% used</div>
    </div>
    <div class="progress-bar">
        <div class="progress-fill {{.Status}}" id="progress-fill-{{.ID}}"></div>
    </div>
    {{if eq .Status "warning"}}
        <div class="thread-warning">⚠️ Thread is getting full</div>
    {{else if eq .Status "full"}}
        <div class="thread-warning">🔄 Starting new thread soon</div>
    {{end}}
</div>

<style>
.thread-progress {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 1.2rem;
    margin-bottom: 1rem;
}

.thread-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.8rem;
}

.thread-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.4rem;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.thread-usage {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.progress-bar {
    height: 6px;
    background: var(--border-light);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease, background-color 0.3s ease;
}

.progress-fill.normal {
    background: #10b981; /* Green */
}

.progress-fill.moderate {
    background: #f59e0b; /* Yellow */
}

.progress-fill.warning {
    background: #f97316; /* Orange */
}

.progress-fill.full {
    background: #ef4444; /* Red */
}

.thread-warning {
    margin-top: 0.8rem;
    padding: 0.6rem 1rem;
    background: rgba(249, 115, 22, 0.1);
    border: 1px solid rgba(249, 115, 22, 0.3);
    border-radius: 6px;
    color: #ea580c;
    font-size: 1.2rem;
    font-weight: 500;
    text-align: center;
}
</style>

<script>
// Set progress bar width dynamically
document.addEventListener('DOMContentLoaded', function() {
    const progressFill = document.getElementById('progress-fill-{{.ID}}');
    if (progressFill) {
        progressFill.style.width = '{{.UsagePercentage}}%';
    }
});
</script> 