<!-- Mo<PERSON>les Grid -->
<div class="modules-grid">
    {{if .modules}}
        {{range .modules}}
            <div class="module-card {{if or (eq .Status "accepted") (eq .Status "recommended")}}module-selected{{end}}" data-module-id="{{.ID}}">
                <div class="module-header">
                    <div class="module-info">
                        <h4 class="module-name">{{.Name}}</h4>
                        <div class="module-tags">
                            <span class="tag tag-{{.Category}}">{{.Category}}</span>
                            {{if .RecommendationScore}}
                                <span class="tag tag-score">{{.RecommendationScore}}% match</span>
                            {{end}}
                        </div>
                    </div>
                    <input type="checkbox"
                           class="module-checkbox"
                           {{if or (eq .Status "accepted") (eq .Status "recommended")}}checked{{end}}
                           hx-post="/api/modules/toggle"
                           hx-trigger="change"
                           hx-vals='js:{module_id: "{{.ID}}", organization_id: "{{$.OrganizationID}}", checked: event.target.checked.toString()}'
                           hx-target="#pricing-summary"
                           hx-swap="innerHTML">
                </div>
                
                <p class="module-description">{{.Description}}</p>
                
                {{if .KeyBenefit}}
                    <div class="module-benefit">
                        <svg class="icon-benefit" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                        {{.KeyBenefit}}
                    </div>
                {{end}}
            </div>
        {{end}}
    {{else}}
        <div class="empty-state">
            <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
            <h3>No modules available</h3>
            <p>We're still analyzing your business needs...</p>
        </div>
    {{end}}
</div>

<!-- Update pricing after modules load -->
<script>
htmx.ajax('GET', '/api/pricing/calculate/{{$.OrganizationID}}', {target: '#pricing-summary'});

// Module selection handling
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('module-checkbox')) {
        const card = e.target.closest('.module-card');
        if (e.target.checked) {
            card.classList.add('module-selected');
        } else {
            card.classList.remove('module-selected');
        }
    }
});
</script>