<!-- Thread Switcher -->
<div class="thread-switcher" x-data="{ open: false }" @click.away="open = false">
    <button class="thread-switcher-button" @click="open = !open">
        <span class="thread-switcher-icon">💬</span>
        <span class="thread-switcher-text">Conversations</span>
        <span class="thread-switcher-arrow" :class="{ 'rotated': open }">▼</span>
    </button>
    
    <div class="thread-switcher-dropdown" x-show="open" x-transition>
        <div class="thread-switcher-header">
            <h4>Recent Conversations</h4>
            <button class="new-thread-btn" 
                    hx-post="/api/threads/new" 
                    hx-vals='{"organization_id": "{{$.OrganizationID}}"}'
                    hx-target="#thread-progress"
                    hx-swap="outerHTML">
                ➕ New
            </button>
        </div>
        
        <div class="thread-list">
            {{range .Threads}}
            <div class="thread-item {{if .IsActive}}active{{end}}"
                 hx-get="/api/threads/{{.ID}}/switch"
                 hx-target="#chat-messages"
                 hx-swap="innerHTML"
                 hx-trigger="click"
                 @click="open = false">
                <div class="thread-item-content">
                    <div class="thread-item-title">{{.Title}}</div>
                    <div class="thread-item-meta">
                        <span class="thread-item-usage {{.Status}}">{{.UsagePercentage}}%</span>
                        <span class="thread-item-date">{{.UpdatedAt.Format "Jan 2"}}</span>
                    </div>
                </div>
                {{if .IsActive}}
                <div class="thread-active-indicator">●</div>
                {{end}}
            </div>
            {{end}}
        </div>
    </div>
</div>

<style>
.thread-switcher {
    position: relative;
    margin-bottom: 1rem;
}

.thread-switcher-button {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem 1.2rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.thread-switcher-button:hover {
    background: var(--bg-primary);
    border-color: var(--vertoie-orange);
}

.thread-switcher-icon {
    font-size: 1.6rem;
}

.thread-switcher-text {
    flex: 1;
    text-align: left;
    font-weight: 500;
    font-size: 1.4rem;
}

.thread-switcher-arrow {
    font-size: 1.2rem;
    transition: transform 0.2s ease;
}

.thread-switcher-arrow.rotated {
    transform: rotate(180deg);
}

.thread-switcher-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 10;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-top: 0.4rem;
    max-height: 400px;
    overflow: hidden;
}

.thread-switcher-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.2rem;
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-secondary);
}

.thread-switcher-header h4 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
}

.new-thread-btn {
    padding: 0.6rem 1rem;
    background: var(--vertoie-orange);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 1.2rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.new-thread-btn:hover {
    background: #e5611e;
}

.thread-list {
    max-height: 300px;
    overflow-y: auto;
}

.thread-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.2rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid var(--border-light);
}

.thread-item:last-child {
    border-bottom: none;
}

.thread-item:hover {
    background: var(--bg-secondary);
}

.thread-item.active {
    background: rgba(255, 107, 53, 0.1);
    border-left: 3px solid var(--vertoie-orange);
}

.thread-item-content {
    flex: 1;
}

.thread-item-title {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 1.3rem;
    margin-bottom: 0.4rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.thread-item-meta {
    display: flex;
    gap: 1rem;
    align-items: center;
    font-size: 1.1rem;
}

.thread-item-usage {
    font-weight: 600;
    padding: 0.2rem 0.6rem;
    border-radius: 4px;
    font-size: 1rem;
}

.thread-item-usage.normal {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.thread-item-usage.moderate {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.thread-item-usage.warning {
    background: rgba(249, 115, 22, 0.1);
    color: #ea580c;
}

.thread-item-usage.full {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.thread-item-date {
    color: var(--text-secondary);
}

.thread-active-indicator {
    color: var(--vertoie-orange);
    font-size: 1.6rem;
    margin-left: 0.8rem;
}
</style> 