{{if .modules}}
    {{range .modules}}
        <div class="module-card" data-module-id="{{.ID}}">
            <div class="module-header">
                <h3 class="module-name">{{.Name}}</h3>
                <span class="module-score">{{if .RecommendationScore}}{{.RecommendationScore}}{{else}}N/A{{end}}</span>
            </div>
            <span class="module-category {{.Category}}">{{.Category}}</span>
            <p class="module-description">{{.Description}}</p>
            <div class="module-benefit">
                <div class="module-benefit-label">Key Benefit:</div>
                <p class="module-benefit-text">{{.KeyBenefit}}</p>
            </div>
            {{if .IsCustom}}
                <div class="module-status custom">
                    <span class="status-badge custom">Custom Module</span>
                    {{if .UserFeedback}}
                        <p class="user-feedback">{{.UserFeedback}}</p>
                    {{end}}
                </div>
            {{else if .IsAccepted}}
                <div class="module-status accepted">
                    <span class="status-badge accepted">✅ Accepted</span>
                    {{if .UserFeedback}}
                        <p class="user-feedback">{{.UserFeedback}}</p>
                    {{end}}
                </div>
            {{else if .IsRejected}}
                <div class="module-status rejected">
                    <span class="status-badge rejected">❌ Rejected</span>
                    {{if .UserFeedback}}
                        <p class="user-feedback">{{.UserFeedback}}</p>
                    {{end}}
                </div>
            {{else}}
                <div class="module-actions">
                    <button class="module-action-btn accept" onclick="acceptModule('{{.ID}}', '{{.Name}}')">
                        ✅ Accept Module
                    </button>
                    <button class="module-action-btn" onclick="rejectModule('{{.ID}}', '{{.Name}}')">
                        ❌ Reject
                    </button>
                    <button class="module-action-btn" onclick="modifyModule('{{.ID}}', '{{.Name}}')">
                        ✏️ Modify
                    </button>
                </div>
            {{end}}
        </div>
    {{end}}
{{else}}
    <div class="empty-state">
        <div class="empty-icon">🔧</div>
        <h3>No modules yet</h3>
        <p>Start chatting with the AI to get personalized module recommendations</p>
    </div>
{{end}} 