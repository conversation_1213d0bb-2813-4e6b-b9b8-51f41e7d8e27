<!-- AI Message Response -->
<div class="chat-message ai-message">
    <div class="message-content">
        <p>{{.Message}}</p>
        
        {{if .BusinessAnalysis}}
            {{if .BusinessAnalysis.DiscoveryQuestions}}
                <div class="discovery-questions">
                    <p class="questions-intro">Here are some things to consider:</p>
                    {{range $question := .BusinessAnalysis.DiscoveryQuestions}}
                        <div class="question-item">
                            <span class="question-text">{{index $question "question"}}</span>
                            {{if index $question "why_asking"}}
                                <span class="question-hint">({{index $question "why_asking"}})</span>
                            {{end}}
                        </div>
                    {{end}}
                </div>
            {{end}}
        {{end}}
    </div>
</div>

<style>
.discovery-questions {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-light);
}

.questions-intro {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
}

.question-item {
    margin-bottom: 0.5rem;
    padding-left: 1.25rem;
    position: relative;
}

.question-item::before {
    content: "→";
    position: absolute;
    left: 0;
    color: var(--vertoie-orange);
    font-weight: 600;
}

.question-text {
    color: var(--text-primary);
    font-size: 0.875rem;
}

.question-hint {
    color: var(--text-secondary);
    font-size: 0.8125rem;
    font-style: italic;
    margin-left: 0.5rem;
}
</style>

<script>
// Scroll to bottom after adding message
document.addEventListener('DOMContentLoaded', function() {
    const messagesContainer = document.getElementById('chat-messages');
    if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
});
</script> 