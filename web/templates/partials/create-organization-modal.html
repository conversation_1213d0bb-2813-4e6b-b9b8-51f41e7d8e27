<div class="modal-overlay" id="create-organization-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Create New Organization</h2>
            <button class="modal-close" onclick="closeModal()">&times;</button>
        </div>
        
        <form hx-post="/api/organizations" hx-target="#organizations-list" hx-swap="outerHTML">
            <div class="form-group">
                <label for="org-name">Organization Name *</label>
                <input type="text" id="org-name" name="name" required 
                       placeholder="Enter organization name">
            </div>
            
            <div class="form-group">
                <label for="org-description">Description</label>
                <textarea id="org-description" name="description" rows="3" 
                          placeholder="Describe your organization (optional)"></textarea>
            </div>
            
            <div class="modal-actions">
                            <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
            <button type="submit" class="btn btn-primary">Create Organization</button>
            </div>
        </form>
    </div>
</div>

<style>
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-light);
}

.modal-header h2 {
    margin: 0;
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 2.4rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--bg-accent);
    color: var(--text-primary);
}

.form-group {
    margin-bottom: 2rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.8rem;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 1.4rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1.2rem;
    border: 1px solid var(--border-light);
    border-radius: 8px;
    font-size: 1.4rem;
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--vertoie-orange);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-light);
}

.modal-actions .button {
    padding: 1rem 2rem;
    font-size: 1.4rem;
}

@media (max-width: 768px) {
    .modal-content {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .modal-actions {
        flex-direction: column;
    }
    
    .modal-actions .button {
        width: 100%;
    }
}
</style>

<script>
function closeModal() {
    const modal = document.getElementById('create-organization-modal');
    if (modal) {
        modal.remove();
    }
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('create-organization-modal');
    if (modal && event.target === modal) {
        closeModal();
    }
});
</script> 