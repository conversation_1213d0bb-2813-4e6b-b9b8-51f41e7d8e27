<!-- Onboarding Split Layout -->
<div class="onboarding-split">
    <!-- Chat Panel (33%) -->
    <div class="chat-panel">
        <div class="chat-header">
            <h3>Refine Your Software</h3>
            <p>Tell us more about your business needs</p>
        </div>
        
        <div class="chat-messages" id="chat-messages">
            <!-- User's initial message -->
            <div class="message message-user">
                <div class="message-content">{{.InitialMessage}}</div>
            </div>
            
            <!-- AI response -->
            <div class="message message-assistant">
                <div class="message-content">
                    I've analyzed your {{.BusinessAnalysis.Type}} and recommended software modules below. 
                    Feel free to tell me more about your specific needs to refine these suggestions.
                </div>
            </div>
        </div>
        
        <div class="chat-input-wrapper">
            <form hx-post="/api/business-context/chat-htmx" 
                  hx-target="#chat-messages" 
                  hx-swap="beforeend"
                  hx-trigger="submit"
                  hx-on::after-request="this.reset()">
                <input type="hidden" name="organization_id" value="{{.Organization.ID}}">
                <input type="text" 
                       name="message" 
                       placeholder="Tell me more..."
                       class="chat-input"
                       required>
                <button type="submit" class="btn btn-primary btn-icon">
                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                    </svg>
                </button>
            </form>
        </div>
    </div>

    <!-- Modules Panel (67%) -->
    <div class="modules-panel">
        <div class="modules-header">
            <div>
                <h2>Your Custom Software Modules</h2>
                <p class="business-type">{{.BusinessAnalysis.Type}} • {{.BusinessAnalysis.CoreValue}}</p>
            </div>
            
            <!-- Pricing Summary -->
            <div id="pricing-summary" class="pricing-summary">
                <!-- Will be loaded via HTMX -->
            </div>
        </div>

        <!-- Modules Container -->
        <div id="modules-container" 
             class="modules-container"
             hx-get="/api/onboarding-modules/{{.Organization.ID}}"
             hx-trigger="load delay:500ms"
             hx-swap="innerHTML">
            <div class="modules-loading">
                <svg class="spinner-large" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="3" fill="none"/>
                </svg>
                <p>Loading your custom modules...</p>
            </div>
        </div>

        <!-- Complete Button -->
        <div class="complete-wrapper">
            <form hx-post="/api/onboarding/complete" 
                  hx-trigger="submit"
                  hx-on::after-request="if(event.detail.successful) window.location.href = '/dashboard'">
                <button type="submit" class="btn btn-primary btn-large">
                    <span class="btn-text">Create My Software</span>
                    <span class="htmx-indicator">
                        <svg class="spinner" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"/>
                        </svg>
                        Creating...
                    </span>
                </button>
            </form>
        </div>
    </div>
</div>