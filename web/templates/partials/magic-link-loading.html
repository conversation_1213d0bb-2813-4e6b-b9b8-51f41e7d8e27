{{define "content"}}
<style>
.loading-viewport {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.loading-main {
  flex: 1 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.loading-footer {
  flex-shrink: 0;
}
.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #FF6B35;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  animation: spin 1s linear infinite;
  margin-bottom: 24px;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
<div class="loading-viewport">
  <main class="loading-main">
    <img src="/static/images/logo.svg" alt="Vertoie Logo" style="width: 80px; margin-bottom: 24px;">
    <div id="magic-link-status">
      <div class="spinner"></div>
      <h2>Logging you in securely. Please wait…</h2>
    </div>
    <div id="magic-link-verify-trigger"
         hx-get="/auth/verify?token={{.Token}}"
         hx-trigger="load"
         hx-swap="none"
         hx-on::after-request="if(event.detail.xhr.status === 401){document.getElementById('magic-link-status').innerHTML='<h2 style=\'color:#c00\'>This link is invalid, has expired, or has already been used.</h2>'}">
    </div>
  </main>
  <footer class="footer loading-footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-brand">
          <img src="/static/images/logo.svg" alt="Vertoie" class="logo">
          <p>Building the future of business platforms.</p>
        </div>
        <div class="footer-links">
          <div class="footer-group">
            <h4>Product</h4>
            <a href="/">Features</a>
            <a href="/">Pricing</a>
            <a href="/">Security</a>
          </div>
          <div class="footer-group">
            <h4>Company</h4>
            <a href="/">About</a>
            <a href="/">Blog</a>
            <a href="/">Careers</a>
          </div>
          <div class="footer-group">
            <h4>Support</h4>
            <a href="/">Help Center</a>
            <a href="/">Contact</a>
            <a href="/">Status</a>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2025 Vertoie. All rights reserved.</p>
      </div>
    </div>
  </footer>
</div>
{{end}} 