<div class="hero-visual" id="hero-visual" hx-swap-oob="true">
    <div class="mockup-browser">
        <div class="mockup-header">
            <div class="mockup-dots">
                <span></span><span></span><span></span>
            </div>
        </div>
        <div class="mockup-content magic-link-confirmation">
            <div class="magic-link-success">
                <div class="success-icon">✅</div>
                <h3>Magic Link Sent!</h3>
                <p>We've sent a secure magic link to <strong>{{ .Email }}</strong>.</p>
                <p>Check your inbox to sign in.</p>
            </div>
        </div>
    </div>
</div>

<style>
.magic-link-confirmation {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    padding: 2rem;
}

.magic-link-success {
    text-align: center;
    color: var(--text-primary);
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
}

.magic-link-success h3 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.magic-link-success p {
    font-size: 1.4rem;
    line-height: 1.6;
    margin-bottom: 0.8rem;
    color: var(--text-secondary);
}

</style>