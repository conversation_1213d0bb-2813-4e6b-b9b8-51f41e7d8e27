<!-- Step 2: Module Selection with Chat -->
<div class="onboarding-split">
    <!-- Chat Panel (Left) -->
    <div class="onboarding-chat">
        <div class="chat-header">
            <h3 class="text-xl font-semibold">Refine Your Software</h3>
            <p class="text-secondary text-sm">Tell us more to customize your modules</p>
        </div>
        
        <div class="chat-messages" id="chat-messages">
            <!-- Initial assistant message -->
            <div class="chat-message assistant">
                <div class="message-avatar">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                    </svg>
                </div>
                <div class="message-content">
                    <p>I've analyzed your business and recommended some modules. Feel free to tell me more about your specific needs to refine these suggestions.</p>
                </div>
            </div>
        </div>
        
        <div class="chat-input-container">
            <form hx-post="/api/business-context/chat-htmx" 
                  hx-target="#chat-messages" 
                  hx-swap="beforeend"
                  hx-trigger="submit"
                  hx-on::after-request="this.reset()"
                  class="chat-form">
                <input type="hidden" name="organization_id" value="{{.Organization.ID}}">
                <input type="text" 
                       name="message" 
                       placeholder="e.g., 'We need inventory tracking' or 'We have 5 employees'"
                       class="chat-input"
                       required>
                <button type="submit" class="btn btn-primary">
                    <span class="default-text">Send</span>
                    <span class="htmx-indicator">
                        <svg class="spinner h-5 w-5" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"/>
                        </svg>
                    </span>
                </button>
            </form>
        </div>
    </div>

    <!-- Modules Panel (Right) -->
    <div class="onboarding-modules">
        <div class="modules-header">
            <h2>Your Custom Software Modules</h2>
            {{if .BusinessAnalysis}}
            <p class="business-summary">
                <strong>{{.BusinessAnalysis.Type}}</strong> - 
                <span>{{.BusinessAnalysis.CoreValue}}</span>
            </p>
            {{end}}
        </div>

        <!-- Pricing Summary -->
        <div id="pricing-container">
            {{template "partials/pricing-summary" .}}
        </div>

        <!-- Modules Grid -->
        <div class="modules-container">
            <div class="modules-grid">
                {{if .Modules}}
                    {{range .Modules}}
                        <div class="module-card {{if or (eq .Status "accepted") (eq .Status "recommended")}}module-selected{{end}}"
                             data-module-id="{{.ID}}">

                            <div class="module-header">
                                <div class="module-header-content">
                                    <div class="module-header-text">
                                        <h3 class="module-title">{{.Name}}</h3>
                                        <div class="module-meta">
                                            <span class="module-badge module-badge-{{.Category}}">{{.Category}}</span>
                                            {{if .RecommendationScore}}
                                                <span class="module-score">{{.RecommendationScore}}% match</span>
                                            {{end}}
                                        </div>
                                    </div>
                                    <label class="module-checkbox">
                                        <input type="checkbox"
                                               class="module-checkbox"
                                               {{if or (eq .Status "accepted") (eq .Status "recommended")}}checked{{end}}
                                               hx-post="/api/modules/toggle"
                                               hx-trigger="change"
                                               hx-target="#pricing-container"
                                               hx-vals='js:{module_id: "{{.ID}}", organization_id: "{{$.Organization.ID}}", checked: event.target.checked.toString()}'
                                               hx-swap="innerHTML">
                                        <span class="checkbox-mark"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="module-content">
                                <p class="module-description">{{.Description}}</p>
                                
                                {{if .KeyBenefit}}
                                    <div class="module-benefit">
                                        <svg class="benefit-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                        <span class="benefit-text">{{.KeyBenefit}}</span>
                                    </div>
                                {{end}}
                            </div>
                        </div>
                    {{end}}
                {{else}}
                    <div class="empty-modules">
                        <div class="empty-card">
                            <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                            <h3 class="empty-title">No modules generated yet</h3>
                            <p class="empty-text">Analyzing your business needs...</p>
                        </div>
                    </div>
                {{end}}
            </div>
        </div>

        <!-- Complete Button -->
        <div class="complete-section">
            <form hx-post="/api/onboarding/complete" 
                  hx-trigger="submit"
                  hx-swap="none">
                <button type="submit" 
                        class="btn btn-primary btn-complete">
                    <span class="default-text">Create My Software</span>
                    <span class="htmx-indicator flex items-center gap-2">
                        <svg class="spinner h-5 w-5" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"/>
                        </svg>
                        Creating your software...
                    </span>
                </button>
            </form>
        </div>
    </div>
</div>

<script>
// Minimal JavaScript for UI polish only
document.body.addEventListener('htmx:afterRequest', function(evt) {
    if (evt.detail.pathInfo.requestPath === '/api/onboarding/complete' && evt.detail.successful) {
        window.location.href = '/dashboard';
    }
});

// Toggle module card selection state on checkbox change
document.body.addEventListener('change', function(evt) {
    if (evt.target.type === 'checkbox' && evt.target.closest('.module-checkbox')) {
        const moduleCard = evt.target.closest('.module-card');
        if (moduleCard) {
            if (evt.target.checked) {
                moduleCard.classList.add('module-selected');
            } else {
                moduleCard.classList.remove('module-selected');
            }
        }
    }
});
</script>