{{if .Organizations}}
<div class="organizations-grid">
    {{range .Organizations}}
    <div class="organization-card">
        <div class="organization-header">
            <div class="organization-icon">🏢</div>
            <div class="organization-info">
                <h3>{{.Name}}</h3>
                <p>Created on {{.CreatedAt.Format "January 2, 2006"}}</p>
            </div>
        </div>
        <div class="organization-stats">
            <div class="stat">
                <span class="stat-number">{{len .Users}}</span>
                <span class="stat-label">Members</span>
            </div>
        </div>
        <div class="organization-actions">
            <form method="POST" action="/organizations/{{.ID}}/switch" style="display: inline;">
                <button type="submit" class="btn btn-primary">Switch to This Org</button>
            </form>
        </div>
    </div>
    {{end}}
</div>
{{else}}
<div class="empty-state">
    <div class="empty-icon">🏢</div>
    <h3>No organizations yet</h3>
    <p>Create your first organization to get started</p>
    <button class="btn btn-primary" onclick="openCreateOrganizationModal()" style="margin-top: 2rem;">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
        Create Your First Organization
    </button>
</div>
{{end}} 