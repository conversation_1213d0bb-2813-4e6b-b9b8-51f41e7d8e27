package main

import (
	"log/slog"
	"os"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/template/html/v2"
	"github.com/joho/godotenv"
	"github.com/vertoie/models"
	"github.com/vertoie/web/internal/config"
	"github.com/vertoie/web/internal/database"
	"github.com/vertoie/web/internal/email"
	"github.com/vertoie/web/internal/handlers"
	"github.com/vertoie/web/internal/llm"
	"github.com/vertoie/web/internal/services"
)

func main() {
	if err := godotenv.Load(); err != nil {
		slog.Warn("no .env file found")
	}

	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))
	cfg := config.Load()

	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		logger.Error("failed to initialize database", "error", err)
		os.Exit(1)
	}

	// Initialize email service
	var emailService *email.EmailService
	if cfg.Mailgun.Domain != "" && cfg.Mailgun.APIKey != "" {
		emailService = email.NewEmailService(cfg.Mailgun.Domain, cfg.Mailgun.APIKey, cfg.Mailgun.Sender)
		logger.Info("email service initialized", "domain", cfg.Mailgun.Domain, "sender", cfg.Mailgun.Sender)
	} else {
		logger.Warn("mailgun configuration missing, email service disabled")
		emailService = nil
	}

	// Initialize LLM service
	var llmClient *llm.BusinessGroqClient
	if cfg.Groq.APIKey != "" {
		llmClient = llm.NewBusinessGroqClient(cfg.Groq.APIKey, logger)
		logger.Info("LLM service initialized with Groq")
	} else {
		logger.Warn("Groq API key missing, LLM service disabled")
		llmClient = nil
	}

	// Initialize business prompt example service
	exampleService := models.NewBusinessPromptExampleService(db)
	logger.Info("Business prompt example service initialized")

	// Initialize thread service
	threadService := models.NewThreadService(db)
	logger.Info("Thread service initialized")

	// Initialize recommended module service
	moduleService := models.NewRecommendedModuleService(db)
	logger.Info("Recommended module service initialized")

	// Initialize module prioritization service
	prioritizationService := services.NewModulePrioritizationService(logger)
	logger.Info("Module prioritization service initialized")

	// Initialize business context service
	businessContextService := llm.NewBusinessContextService(llmClient, logger, exampleService, threadService, moduleService, prioritizationService)

	engine := html.New("./templates", ".html")

	// Load all templates including partials
	if err := engine.Load(); err != nil {
		logger.Error("failed to load templates", "error", err)
		os.Exit(1)
	}

	app := fiber.New(fiber.Config{
		Views: engine,
	})

	// Initialize handlers
	h := handlers.New(db, logger, businessContextService)
	authHandler := handlers.NewAuthHandler(db, logger, emailService)

	app.Use(recover.New())
	app.Use(cors.New())

	// Add request logging middleware
	app.Use(func(c *fiber.Ctx) error {
		logger.Info("Request received",
			"method", c.Method(),
			"path", c.Path(),
			"user_agent", c.Get("User-Agent"))
		return c.Next()
	})

	// Serve static files
	app.Static("/static", "./static")

	// Public routes (with optional auth to show different navigation)
	app.Get("/", authHandler.OptionalAuth, h.Home)
	app.Get("/pricing", authHandler.OptionalAuth, h.PricingPage)
	app.Get("/login", authHandler.RedirectIfAuthenticated, h.LoginPage)
	app.Get("/register", authHandler.RedirectIfAuthenticated, h.RegisterPage)

	// Protected routes (redirect unauthenticated users to login)
	app.Get("/dashboard", authHandler.RequireAuth, h.DashboardPage)
	app.Get("/onboarding", authHandler.RequireAuth, h.OnboardingPage)
	app.Post("/onboarding/analyze", authHandler.RequireAuth, h.OnboardingAnalyze)

	// Onboarding API routes (protected)
	app.Post("/api/onboarding/complete", authHandler.RequireAuth, h.CompleteOnboarding)
	app.Post("/api/business-context/analyze", authHandler.RequireAuth, h.BusinessContextAnalyze)
	app.Post("/api/business-context/single-sentence", authHandler.RequireAuth, h.BusinessContextSingleSentence)
	app.Post("/api/business-context/preview", authHandler.RequireAuth, h.BusinessContextPreview)
	app.Post("/api/business-context/chat-htmx", authHandler.RequireAuth, h.BusinessContextChatHTMX)
	app.Get("/api/onboarding-modules/:id", authHandler.RequireAuth, h.GetOnboardingModules)
	app.Post("/api/modules/toggle", authHandler.RequireAuth, h.ToggleModule)
	app.Get("/api/pricing/calculate/:org_id", authHandler.RequireAuth, h.CalculatePricing)

	// Organization API routes (protected) - simplified
	app.Post("/api/organizations", authHandler.RequireAuth, h.CreateOrganization)
	app.Get("/api/organizations", authHandler.RequireAuth, h.GetOrganizations)

	// Authentication API routes
	auth := app.Group("/auth")
	auth.Post("/register", authHandler.Register)
	auth.Post("/magic-link", authHandler.RequestMagicLink)
	auth.Get("/verify", authHandler.VerifyMagicLink)
	auth.Post("/logout", authHandler.Logout)
	auth.Get("/me", authHandler.GetCurrentUser)

	// API routes (for HTMX)
	app.Get("/api/pricing", h.GetPricing)
	app.Get("/api/pricing/main", h.GetMainPricing)
	app.Get("/api/plans", authHandler.RequireAuth, h.GetPlans)

	logger.Info("starting server", "address", ":"+cfg.Port)
	if err := app.Listen(":" + cfg.Port); err != nil {
		logger.Error("failed to start server", "error", err)
		os.Exit(1)
	}
}
